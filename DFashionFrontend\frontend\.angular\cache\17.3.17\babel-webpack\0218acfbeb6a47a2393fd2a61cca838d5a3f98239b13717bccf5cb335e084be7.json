{"ast": null, "code": "import _asyncToGenerator from \"E:/Fashion/DFashionFrontend/frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\nimport { IonicModule } from '@ionic/angular';\nimport { Subscription } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"@ionic/angular\";\nfunction TrendingCollectionsComponent_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 9);\n    i0.ɵɵelement(1, \"ion-spinner\", 10);\n    i0.ɵɵelementStart(2, \"p\");\n    i0.ɵɵtext(3, \"Loading trending collections...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction TrendingCollectionsComponent_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 11);\n    i0.ɵɵelement(1, \"ion-icon\", 12);\n    i0.ɵɵelementStart(2, \"p\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"button\", 13);\n    i0.ɵɵlistener(\"click\", function TrendingCollectionsComponent_div_9_Template_button_click_4_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onRetry());\n    });\n    i0.ɵɵtext(5, \"Try Again\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r1.error);\n  }\n}\nfunction TrendingCollectionsComponent_div_10_div_1_div_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 36);\n  }\n  if (rf & 2) {\n    const image_r4 = ctx.$implicit;\n    i0.ɵɵstyleProp(\"background-image\", \"url(\" + image_r4 + \")\");\n  }\n}\nfunction TrendingCollectionsComponent_div_10_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 20);\n    i0.ɵɵlistener(\"click\", function TrendingCollectionsComponent_div_10_div_1_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onCollectionClick(ctx_r1.featuredCollection));\n    });\n    i0.ɵɵelementStart(1, \"div\", 21)(2, \"div\", 22)(3, \"div\", 23);\n    i0.ɵɵelement(4, \"img\", 24);\n    i0.ɵɵelementStart(5, \"div\", 25)(6, \"div\", 26)(7, \"h4\", 27);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"p\", 28);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"div\", 29)(12, \"span\", 30);\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"span\", 31);\n    i0.ɵɵtext(15);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(16, \"div\", 32);\n    i0.ɵɵelement(17, \"ion-icon\", 33);\n    i0.ɵɵtext(18, \" Hot Collection \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(19, \"div\", 34);\n    i0.ɵɵtemplate(20, TrendingCollectionsComponent_div_10_div_1_div_20_Template, 1, 2, \"div\", 35);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"src\", ctx_r1.featuredCollection.coverImage, i0.ɵɵsanitizeUrl)(\"alt\", ctx_r1.featuredCollection.name);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r1.featuredCollection.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.featuredCollection.description);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r1.featuredCollection.itemCount, \" items\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate2(\"\", ctx_r1.formatPrice(ctx_r1.featuredCollection.priceRange.min), \" - \", ctx_r1.formatPrice(ctx_r1.featuredCollection.priceRange.max), \"\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.featuredCollection.previewImages.slice(0, 3));\n  }\n}\nfunction TrendingCollectionsComponent_div_10_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 37);\n    i0.ɵɵlistener(\"click\", function TrendingCollectionsComponent_div_10_div_3_Template_div_click_0_listener() {\n      const collection_r6 = i0.ɵɵrestoreView(_r5).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onCollectionClick(collection_r6));\n    });\n    i0.ɵɵelementStart(1, \"div\", 38);\n    i0.ɵɵelement(2, \"img\", 39);\n    i0.ɵɵelementStart(3, \"div\", 25)(4, \"div\", 40)(5, \"h5\", 27);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"p\", 28);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"div\", 41)(10, \"span\", 30);\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"span\", 42);\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(14, \"div\", 43);\n    i0.ɵɵtext(15);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"div\", 44);\n    i0.ɵɵlistener(\"click\", function TrendingCollectionsComponent_div_10_div_3_Template_div_click_16_listener($event) {\n      const collection_r6 = i0.ɵɵrestoreView(_r5).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.toggleLike(collection_r6, $event));\n    });\n    i0.ɵɵelement(17, \"ion-icon\", 45);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(18, \"div\", 46)(19, \"div\", 47)(20, \"span\", 31);\n    i0.ɵɵtext(21);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"span\", 48);\n    i0.ɵɵtext(23);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(24, \"div\", 49)(25, \"button\", 50);\n    i0.ɵɵlistener(\"click\", function TrendingCollectionsComponent_div_10_div_3_Template_button_click_25_listener($event) {\n      const collection_r6 = i0.ɵɵrestoreView(_r5).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onCollectionClick(collection_r6, $event));\n    });\n    i0.ɵɵelement(26, \"ion-icon\", 51);\n    i0.ɵɵtext(27, \" View \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(28, \"button\", 52);\n    i0.ɵɵlistener(\"click\", function TrendingCollectionsComponent_div_10_div_3_Template_button_click_28_listener($event) {\n      const collection_r6 = i0.ɵɵrestoreView(_r5).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.toggleSave(collection_r6, $event));\n    });\n    i0.ɵɵelement(29, \"ion-icon\", 45);\n    i0.ɵɵtext(30);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const collection_r6 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"src\", collection_r6.coverImage, i0.ɵɵsanitizeUrl)(\"alt\", collection_r6.name);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(collection_r6.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(collection_r6.description);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\", collection_r6.itemCount, \" items\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"by \", collection_r6.curator.name, \"\");\n    i0.ɵɵadvance();\n    i0.ɵɵstyleProp(\"background-color\", collection_r6.category.color);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", collection_r6.category.name, \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"active\", ctx_r1.isCollectionLiked(collection_r6._id));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"name\", ctx_r1.isCollectionLiked(collection_r6._id) ? \"heart\" : \"heart-outline\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate2(\"\", ctx_r1.formatPrice(collection_r6.priceRange.min), \" - \", ctx_r1.formatPrice(collection_r6.priceRange.max), \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"Avg: \", ctx_r1.formatPrice(collection_r6.averagePrice), \"\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵclassProp(\"saved\", ctx_r1.isCollectionSaved(collection_r6._id));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"name\", ctx_r1.isCollectionSaved(collection_r6._id) ? \"bookmark\" : \"bookmark-outline\");\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.isCollectionSaved(collection_r6._id) ? \"Saved\" : \"Save\", \" \");\n  }\n}\nfunction TrendingCollectionsComponent_div_10_div_4_button_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 57);\n    i0.ɵɵlistener(\"click\", function TrendingCollectionsComponent_div_10_div_4_button_4_Template_button_click_0_listener() {\n      const category_r8 = i0.ɵɵrestoreView(_r7).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.onCategoryClick(category_r8));\n    });\n    i0.ɵɵelementStart(1, \"div\", 58);\n    i0.ɵɵelement(2, \"ion-icon\", 45);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 59)(4, \"h5\", 60);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"span\", 61);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const category_r8 = ctx.$implicit;\n    i0.ɵɵstyleProp(\"background\", category_r8.gradient);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"name\", category_r8.icon);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(category_r8.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", category_r8.collectionCount, \" collections\");\n  }\n}\nfunction TrendingCollectionsComponent_div_10_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 53)(1, \"h4\", 54);\n    i0.ɵɵtext(2, \"Browse by Category\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 55);\n    i0.ɵɵtemplate(4, TrendingCollectionsComponent_div_10_div_4_button_4_Template, 8, 5, \"button\", 56);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.collectionCategories)(\"ngForTrackBy\", ctx_r1.trackByCategoryName);\n  }\n}\nfunction TrendingCollectionsComponent_div_10_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 62)(1, \"div\", 63)(2, \"div\", 64);\n    i0.ɵɵelement(3, \"ion-icon\", 2);\n    i0.ɵɵelementStart(4, \"div\", 65)(5, \"span\", 66);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"span\", 67);\n    i0.ɵɵtext(8, \"Total Collections\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(9, \"div\", 64);\n    i0.ɵɵelement(10, \"ion-icon\", 68);\n    i0.ɵɵelementStart(11, \"div\", 65)(12, \"span\", 66);\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"span\", 67);\n    i0.ɵɵtext(15, \"Total Items\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(16, \"div\", 64);\n    i0.ɵɵelement(17, \"ion-icon\", 69);\n    i0.ɵɵelementStart(18, \"div\", 65)(19, \"span\", 66);\n    i0.ɵɵtext(20);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"span\", 67);\n    i0.ɵɵtext(22, \"Curators\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(23, \"div\", 64);\n    i0.ɵɵelement(24, \"ion-icon\", 70);\n    i0.ɵɵelementStart(25, \"div\", 65)(26, \"span\", 66);\n    i0.ɵɵtext(27);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(28, \"span\", 67);\n    i0.ɵɵtext(29, \"Total Likes\");\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r1.collectionStats.totalCollections);\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate(ctx_r1.formatCount(ctx_r1.collectionStats.totalItems));\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate(ctx_r1.formatCount(ctx_r1.collectionStats.totalCurators));\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate(ctx_r1.formatCount(ctx_r1.collectionStats.totalLikes));\n  }\n}\nfunction TrendingCollectionsComponent_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 14);\n    i0.ɵɵtemplate(1, TrendingCollectionsComponent_div_10_div_1_Template, 21, 8, \"div\", 15);\n    i0.ɵɵelementStart(2, \"div\", 16);\n    i0.ɵɵtemplate(3, TrendingCollectionsComponent_div_10_div_3_Template, 31, 19, \"div\", 17);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(4, TrendingCollectionsComponent_div_10_div_4_Template, 5, 2, \"div\", 18)(5, TrendingCollectionsComponent_div_10_div_5_Template, 30, 4, \"div\", 19);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.featuredCollection);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.otherCollections)(\"ngForTrackBy\", ctx_r1.trackByCollectionId);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.collectionCategories.length > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.collectionStats);\n  }\n}\nfunction TrendingCollectionsComponent_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 71);\n    i0.ɵɵelement(1, \"ion-icon\", 2);\n    i0.ɵɵelementStart(2, \"p\");\n    i0.ɵɵtext(3, \"No trending collections available\");\n    i0.ɵɵelementEnd()();\n  }\n}\nexport class TrendingCollectionsComponent {\n  constructor(router) {\n    this.router = router;\n    this.trendingCollections = [];\n    this.featuredCollection = null;\n    this.otherCollections = [];\n    this.collectionCategories = [];\n    this.collectionStats = null;\n    this.likedCollections = new Set();\n    this.savedCollections = new Set();\n    this.isLoading = true;\n    this.error = null;\n    this.subscription = new Subscription();\n  }\n  ngOnInit() {\n    this.loadTrendingCollections();\n  }\n  ngOnDestroy() {\n    this.subscription.unsubscribe();\n  }\n  loadTrendingCollections() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      try {\n        _this.isLoading = true;\n        _this.error = null;\n        // Mock data for now - replace with actual service call\n        yield _this.loadMockCollections();\n      } catch (error) {\n        console.error('Error loading trending collections:', error);\n        _this.error = 'Failed to load trending collections';\n        _this.isLoading = false;\n      }\n    })();\n  }\n  loadMockCollections() {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      // Simulate API call delay\n      yield new Promise(resolve => setTimeout(resolve, 1000));\n      // Mock trending collections\n      _this2.trendingCollections = [{\n        _id: 'col1',\n        name: 'Summer Essentials 2024',\n        description: 'Must-have pieces for the perfect summer wardrobe',\n        coverImage: 'https://images.unsplash.com/photo-1515372039744-b8f02a3ae446?w=400&h=300&fit=crop',\n        previewImages: ['https://images.unsplash.com/photo-1441986300917-64674bd600d8?w=150&h=150&fit=crop', 'https://images.unsplash.com/photo-1445205170230-053b83016050?w=150&h=150&fit=crop', 'https://images.unsplash.com/photo-1434389677669-e08b4cac3105?w=150&h=150&fit=crop'],\n        itemCount: 24,\n        priceRange: {\n          min: 1999,\n          max: 8999\n        },\n        averagePrice: 4500,\n        curator: {\n          _id: 'cur1',\n          name: 'Fashion Studio',\n          avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b47c?w=50&h=50&fit=crop&crop=face'\n        },\n        category: {\n          name: 'Summer',\n          color: '#ff6b6b'\n        },\n        tags: ['summer', 'essentials', 'trending'],\n        analytics: {\n          views: 15420,\n          likes: 2340,\n          saves: 890,\n          shares: 156\n        },\n        isActive: true,\n        isFeatured: true,\n        createdAt: new Date()\n      }, {\n        _id: 'col2',\n        name: 'Minimalist Chic',\n        description: 'Clean lines and timeless elegance',\n        coverImage: 'https://images.unsplash.com/photo-1469334031218-e382a71b716b?w=400&h=300&fit=crop',\n        previewImages: ['https://images.unsplash.com/photo-1515372039744-b8f02a3ae446?w=150&h=150&fit=crop', 'https://images.unsplash.com/photo-1441986300917-64674bd600d8?w=150&h=150&fit=crop'],\n        itemCount: 18,\n        priceRange: {\n          min: 2999,\n          max: 12999\n        },\n        averagePrice: 6500,\n        curator: {\n          _id: 'cur2',\n          name: 'Style Maven',\n          avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=50&h=50&fit=crop&crop=face'\n        },\n        category: {\n          name: 'Minimalist',\n          color: '#4ecdc4'\n        },\n        tags: ['minimalist', 'chic', 'timeless'],\n        analytics: {\n          views: 12890,\n          likes: 1890,\n          saves: 567,\n          shares: 89\n        },\n        isActive: true,\n        isFeatured: false,\n        createdAt: new Date()\n      }, {\n        _id: 'col3',\n        name: 'Street Style Icons',\n        description: 'Urban fashion that makes a statement',\n        coverImage: 'https://images.unsplash.com/photo-1441984904996-e0b6ba687e04?w=400&h=300&fit=crop',\n        previewImages: ['https://images.unsplash.com/photo-1542291026-7eec264c27ff?w=150&h=150&fit=crop', 'https://images.unsplash.com/photo-1553062407-98eeb64c6a62?w=150&h=150&fit=crop'],\n        itemCount: 32,\n        priceRange: {\n          min: 1499,\n          max: 6999\n        },\n        averagePrice: 3500,\n        curator: {\n          _id: 'cur3',\n          name: 'Urban Collective',\n          avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=50&h=50&fit=crop&crop=face'\n        },\n        category: {\n          name: 'Street Style',\n          color: '#6c5ce7'\n        },\n        tags: ['street', 'urban', 'edgy'],\n        analytics: {\n          views: 18750,\n          likes: 2890,\n          saves: 1234,\n          shares: 234\n        },\n        isActive: true,\n        isFeatured: false,\n        createdAt: new Date()\n      }, {\n        _id: 'col4',\n        name: 'Vintage Revival',\n        description: 'Classic pieces with a modern twist',\n        coverImage: 'https://images.unsplash.com/photo-1434389677669-e08b4cac3105?w=400&h=300&fit=crop',\n        previewImages: ['https://images.unsplash.com/photo-1515372039744-b8f02a3ae446?w=150&h=150&fit=crop'],\n        itemCount: 21,\n        priceRange: {\n          min: 2499,\n          max: 9999\n        },\n        averagePrice: 5200,\n        curator: {\n          _id: 'cur4',\n          name: 'Retro Boutique',\n          avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=50&h=50&fit=crop&crop=face'\n        },\n        category: {\n          name: 'Vintage',\n          color: '#f9ca24'\n        },\n        tags: ['vintage', 'classic', 'retro'],\n        analytics: {\n          views: 9870,\n          likes: 1456,\n          saves: 678,\n          shares: 123\n        },\n        isActive: true,\n        isFeatured: false,\n        createdAt: new Date()\n      }];\n      // Set featured collection (highest likes)\n      _this2.featuredCollection = _this2.trendingCollections.reduce((prev, current) => prev.analytics.likes > current.analytics.likes ? prev : current);\n      // Set other collections (excluding featured)\n      _this2.otherCollections = _this2.trendingCollections.filter(col => col._id !== _this2.featuredCollection?._id);\n      // Mock collection categories\n      _this2.collectionCategories = [{\n        name: 'Summer',\n        icon: 'sunny-outline',\n        gradient: 'linear-gradient(135deg, #ff6b6b, #feca57)',\n        collectionCount: 12\n      }, {\n        name: 'Minimalist',\n        icon: 'remove-outline',\n        gradient: 'linear-gradient(135deg, #4ecdc4, #44a08d)',\n        collectionCount: 8\n      }, {\n        name: 'Street Style',\n        icon: 'car-outline',\n        gradient: 'linear-gradient(135deg, #6c5ce7, #a29bfe)',\n        collectionCount: 15\n      }, {\n        name: 'Vintage',\n        icon: 'time-outline',\n        gradient: 'linear-gradient(135deg, #f9ca24, #f0932b)',\n        collectionCount: 6\n      }, {\n        name: 'Formal',\n        icon: 'business-outline',\n        gradient: 'linear-gradient(135deg, #2d3436, #636e72)',\n        collectionCount: 9\n      }, {\n        name: 'Casual',\n        icon: 'cafe-outline',\n        gradient: 'linear-gradient(135deg, #00b894, #00cec9)',\n        collectionCount: 18\n      }];\n      // Mock collection stats\n      _this2.collectionStats = {\n        totalCollections: _this2.trendingCollections.length,\n        totalItems: _this2.trendingCollections.reduce((sum, col) => sum + col.itemCount, 0),\n        totalCurators: new Set(_this2.trendingCollections.map(col => col.curator._id)).size,\n        totalLikes: _this2.trendingCollections.reduce((sum, col) => sum + col.analytics.likes, 0)\n      };\n      _this2.isLoading = false;\n    })();\n  }\n  onCollectionClick(collection, event) {\n    if (event) {\n      event.stopPropagation();\n    }\n    this.router.navigate(['/collection', collection._id]);\n  }\n  onCategoryClick(category) {\n    this.router.navigate(['/collections'], {\n      queryParams: {\n        category: category.name.toLowerCase()\n      }\n    });\n  }\n  toggleLike(collection, event) {\n    event.stopPropagation();\n    if (this.likedCollections.has(collection._id)) {\n      this.likedCollections.delete(collection._id);\n      collection.analytics.likes--;\n    } else {\n      this.likedCollections.add(collection._id);\n      collection.analytics.likes++;\n    }\n  }\n  toggleSave(collection, event) {\n    event.stopPropagation();\n    if (this.savedCollections.has(collection._id)) {\n      this.savedCollections.delete(collection._id);\n      collection.analytics.saves--;\n    } else {\n      this.savedCollections.add(collection._id);\n      collection.analytics.saves++;\n    }\n  }\n  isCollectionLiked(collectionId) {\n    return this.likedCollections.has(collectionId);\n  }\n  isCollectionSaved(collectionId) {\n    return this.savedCollections.has(collectionId);\n  }\n  formatPrice(price) {\n    return new Intl.NumberFormat('en-IN', {\n      style: 'currency',\n      currency: 'INR',\n      minimumFractionDigits: 0\n    }).format(price);\n  }\n  formatCount(count) {\n    if (count >= 1000000) {\n      return (count / 1000000).toFixed(1) + 'M';\n    } else if (count >= 1000) {\n      return (count / 1000).toFixed(1) + 'K';\n    }\n    return count.toString();\n  }\n  trackByCollectionId(index, collection) {\n    return collection._id;\n  }\n  trackByCategoryName(index, category) {\n    return category.name;\n  }\n  onSeeAll(event) {\n    event.preventDefault();\n    this.router.navigate(['/trending/collections']);\n  }\n  onRetry() {\n    this.loadTrendingCollections();\n  }\n  static {\n    this.ɵfac = function TrendingCollectionsComponent_Factory(t) {\n      return new (t || TrendingCollectionsComponent)(i0.ɵɵdirectiveInject(i1.Router));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: TrendingCollectionsComponent,\n      selectors: [[\"app-trending-collections\"]],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 12,\n      vars: 4,\n      consts: [[1, \"component-container\"], [1, \"component-header\"], [\"name\", \"albums-outline\"], [\"href\", \"#\", 1, \"see-all\", 3, \"click\"], [1, \"component-content\"], [\"class\", \"loading-container\", 4, \"ngIf\"], [\"class\", \"error-container\", 4, \"ngIf\"], [\"class\", \"collections-content\", 4, \"ngIf\"], [\"class\", \"empty-container\", 4, \"ngIf\"], [1, \"loading-container\"], [\"name\", \"crescent\"], [1, \"error-container\"], [\"name\", \"alert-circle-outline\"], [1, \"retry-btn\", 3, \"click\"], [1, \"collections-content\"], [\"class\", \"featured-collection\", 3, \"click\", 4, \"ngIf\"], [1, \"other-collections\"], [\"class\", \"collection-item\", 3, \"click\", 4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [\"class\", \"collection-categories\", 4, \"ngIf\"], [\"class\", \"trending-stats\", 4, \"ngIf\"], [1, \"featured-collection\", 3, \"click\"], [1, \"collection-card\"], [1, \"collection-images\"], [1, \"main-image\"], [1, \"cover-image\", 3, \"src\", \"alt\"], [1, \"collection-overlay\"], [1, \"collection-info\"], [1, \"collection-name\"], [1, \"collection-description\"], [1, \"collection-stats\"], [1, \"item-count\"], [1, \"price-range\"], [1, \"trending-badge\"], [\"name\", \"flame\"], [1, \"preview-images\"], [\"class\", \"preview-item\", 3, \"background-image\", 4, \"ngFor\", \"ngForOf\"], [1, \"preview-item\"], [1, \"collection-item\", 3, \"click\"], [1, \"collection-image-container\"], [1, \"collection-image\", 3, \"src\", \"alt\"], [1, \"collection-details\"], [1, \"collection-meta\"], [1, \"curator\"], [1, \"collection-tag\"], [1, \"like-btn\", 3, \"click\"], [3, \"name\"], [1, \"collection-footer\"], [1, \"price-info\"], [1, \"avg-price\"], [1, \"collection-actions\"], [1, \"action-btn\", \"view-btn\", 3, \"click\"], [\"name\", \"eye-outline\"], [1, \"action-btn\", \"save-btn\", 3, \"click\"], [1, \"collection-categories\"], [1, \"categories-title\"], [1, \"categories-grid\"], [\"class\", \"category-item\", 3, \"background\", \"click\", 4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [1, \"category-item\", 3, \"click\"], [1, \"category-icon\"], [1, \"category-info\"], [1, \"category-name\"], [1, \"category-count\"], [1, \"trending-stats\"], [1, \"stats-grid\"], [1, \"stat-item\"], [1, \"stat-info\"], [1, \"stat-value\"], [1, \"stat-label\"], [\"name\", \"bag-outline\"], [\"name\", \"people-outline\"], [\"name\", \"heart-outline\"], [1, \"empty-container\"]],\n      template: function TrendingCollectionsComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"h3\");\n          i0.ɵɵelement(3, \"ion-icon\", 2);\n          i0.ɵɵtext(4, \" Trending Collections \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(5, \"a\", 3);\n          i0.ɵɵlistener(\"click\", function TrendingCollectionsComponent_Template_a_click_5_listener($event) {\n            return ctx.onSeeAll($event);\n          });\n          i0.ɵɵtext(6, \"See All\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(7, \"div\", 4);\n          i0.ɵɵtemplate(8, TrendingCollectionsComponent_div_8_Template, 4, 0, \"div\", 5)(9, TrendingCollectionsComponent_div_9_Template, 6, 1, \"div\", 6)(10, TrendingCollectionsComponent_div_10_Template, 6, 5, \"div\", 7)(11, TrendingCollectionsComponent_div_11_Template, 4, 0, \"div\", 8);\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.error && !ctx.isLoading);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading && !ctx.error && ctx.trendingCollections.length > 0);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading && !ctx.error && ctx.trendingCollections.length === 0);\n        }\n      },\n      dependencies: [CommonModule, i2.NgForOf, i2.NgIf, RouterModule, IonicModule, i3.IonIcon, i3.IonSpinner],\n      styles: [\".loading-container[_ngcontent-%COMP%], .error-container[_ngcontent-%COMP%], .empty-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n  padding: 40px 20px;\\n  text-align: center;\\n}\\n.loading-container[_ngcontent-%COMP%]   ion-spinner[_ngcontent-%COMP%], .error-container[_ngcontent-%COMP%]   ion-spinner[_ngcontent-%COMP%], .empty-container[_ngcontent-%COMP%]   ion-spinner[_ngcontent-%COMP%] {\\n  margin-bottom: 16px;\\n}\\n.loading-container[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%], .error-container[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%], .empty-container[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n  font-size: 48px;\\n  margin-bottom: 16px;\\n}\\n.loading-container[_ngcontent-%COMP%]   p[_ngcontent-%COMP%], .error-container[_ngcontent-%COMP%]   p[_ngcontent-%COMP%], .empty-container[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0 0 16px 0;\\n  font-size: 14px;\\n}\\n.loading-container[_ngcontent-%COMP%]   .retry-btn[_ngcontent-%COMP%], .error-container[_ngcontent-%COMP%]   .retry-btn[_ngcontent-%COMP%], .empty-container[_ngcontent-%COMP%]   .retry-btn[_ngcontent-%COMP%] {\\n  background: #e74c3c;\\n  color: white;\\n  border: none;\\n  padding: 8px 16px;\\n  border-radius: 6px;\\n  font-size: 12px;\\n  cursor: pointer;\\n  transition: background 0.3s ease;\\n}\\n.loading-container[_ngcontent-%COMP%]   .retry-btn[_ngcontent-%COMP%]:hover, .error-container[_ngcontent-%COMP%]   .retry-btn[_ngcontent-%COMP%]:hover, .empty-container[_ngcontent-%COMP%]   .retry-btn[_ngcontent-%COMP%]:hover {\\n  background: #c0392b;\\n}\\n\\n.error-container[_ngcontent-%COMP%] {\\n  color: #e74c3c;\\n}\\n.error-container[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n  color: #e74c3c;\\n}\\n\\n.empty-container[_ngcontent-%COMP%] {\\n  color: #999;\\n}\\n.empty-container[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n  color: #ccc;\\n}\\n\\n.collections-content[_ngcontent-%COMP%]   .featured-collection[_ngcontent-%COMP%] {\\n  margin-bottom: 24px;\\n  cursor: pointer;\\n}\\n.collections-content[_ngcontent-%COMP%]   .featured-collection[_ngcontent-%COMP%]   .collection-card[_ngcontent-%COMP%] {\\n  border-radius: 16px;\\n  overflow: hidden;\\n  transition: transform 0.3s ease, box-shadow 0.3s ease;\\n}\\n.collections-content[_ngcontent-%COMP%]   .featured-collection[_ngcontent-%COMP%]   .collection-card[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-4px);\\n  box-shadow: 0 12px 30px rgba(0, 0, 0, 0.15);\\n}\\n.collections-content[_ngcontent-%COMP%]   .featured-collection[_ngcontent-%COMP%]   .collection-card[_ngcontent-%COMP%]   .collection-images[_ngcontent-%COMP%] {\\n  position: relative;\\n}\\n.collections-content[_ngcontent-%COMP%]   .featured-collection[_ngcontent-%COMP%]   .collection-card[_ngcontent-%COMP%]   .collection-images[_ngcontent-%COMP%]   .main-image[_ngcontent-%COMP%] {\\n  position: relative;\\n  width: 100%;\\n  height: 250px;\\n  overflow: hidden;\\n}\\n.collections-content[_ngcontent-%COMP%]   .featured-collection[_ngcontent-%COMP%]   .collection-card[_ngcontent-%COMP%]   .collection-images[_ngcontent-%COMP%]   .main-image[_ngcontent-%COMP%]   .cover-image[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 100%;\\n  object-fit: cover;\\n  transition: transform 0.3s ease;\\n}\\n.collections-content[_ngcontent-%COMP%]   .featured-collection[_ngcontent-%COMP%]   .collection-card[_ngcontent-%COMP%]   .collection-images[_ngcontent-%COMP%]   .main-image[_ngcontent-%COMP%]:hover   .cover-image[_ngcontent-%COMP%] {\\n  transform: scale(1.05);\\n}\\n.collections-content[_ngcontent-%COMP%]   .featured-collection[_ngcontent-%COMP%]   .collection-card[_ngcontent-%COMP%]   .collection-images[_ngcontent-%COMP%]   .main-image[_ngcontent-%COMP%]   .collection-overlay[_ngcontent-%COMP%] {\\n  position: absolute;\\n  bottom: 0;\\n  left: 0;\\n  right: 0;\\n  background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));\\n  padding: 24px;\\n  color: white;\\n}\\n.collections-content[_ngcontent-%COMP%]   .featured-collection[_ngcontent-%COMP%]   .collection-card[_ngcontent-%COMP%]   .collection-images[_ngcontent-%COMP%]   .main-image[_ngcontent-%COMP%]   .collection-overlay[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .collection-name[_ngcontent-%COMP%] {\\n  font-size: 24px;\\n  font-weight: 700;\\n  margin: 0 0 8px 0;\\n  color: white;\\n}\\n.collections-content[_ngcontent-%COMP%]   .featured-collection[_ngcontent-%COMP%]   .collection-card[_ngcontent-%COMP%]   .collection-images[_ngcontent-%COMP%]   .main-image[_ngcontent-%COMP%]   .collection-overlay[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .collection-description[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  margin: 0 0 12px 0;\\n  opacity: 0.9;\\n  line-height: 1.4;\\n}\\n.collections-content[_ngcontent-%COMP%]   .featured-collection[_ngcontent-%COMP%]   .collection-card[_ngcontent-%COMP%]   .collection-images[_ngcontent-%COMP%]   .main-image[_ngcontent-%COMP%]   .collection-overlay[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .collection-stats[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 16px;\\n  font-size: 12px;\\n  font-weight: 600;\\n}\\n.collections-content[_ngcontent-%COMP%]   .featured-collection[_ngcontent-%COMP%]   .collection-card[_ngcontent-%COMP%]   .collection-images[_ngcontent-%COMP%]   .main-image[_ngcontent-%COMP%]   .collection-overlay[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .collection-stats[_ngcontent-%COMP%]   .item-count[_ngcontent-%COMP%] {\\n  color: #ffd700;\\n}\\n.collections-content[_ngcontent-%COMP%]   .featured-collection[_ngcontent-%COMP%]   .collection-card[_ngcontent-%COMP%]   .collection-images[_ngcontent-%COMP%]   .main-image[_ngcontent-%COMP%]   .collection-overlay[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .collection-stats[_ngcontent-%COMP%]   .price-range[_ngcontent-%COMP%] {\\n  color: #4ecdc4;\\n}\\n.collections-content[_ngcontent-%COMP%]   .featured-collection[_ngcontent-%COMP%]   .collection-card[_ngcontent-%COMP%]   .collection-images[_ngcontent-%COMP%]   .main-image[_ngcontent-%COMP%]   .trending-badge[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 16px;\\n  right: 16px;\\n  background: linear-gradient(135deg, #ff6b6b, #ee5a24);\\n  color: white;\\n  padding: 8px 12px;\\n  border-radius: 20px;\\n  font-size: 12px;\\n  font-weight: 600;\\n  display: flex;\\n  align-items: center;\\n  gap: 6px;\\n  box-shadow: 0 4px 12px rgba(255, 107, 107, 0.4);\\n}\\n.collections-content[_ngcontent-%COMP%]   .featured-collection[_ngcontent-%COMP%]   .collection-card[_ngcontent-%COMP%]   .collection-images[_ngcontent-%COMP%]   .main-image[_ngcontent-%COMP%]   .trending-badge[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  animation: _ngcontent-%COMP%_flicker 1.5s infinite alternate;\\n}\\n.collections-content[_ngcontent-%COMP%]   .featured-collection[_ngcontent-%COMP%]   .collection-card[_ngcontent-%COMP%]   .collection-images[_ngcontent-%COMP%]   .preview-images[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 16px;\\n  left: 16px;\\n  display: flex;\\n  gap: 8px;\\n}\\n.collections-content[_ngcontent-%COMP%]   .featured-collection[_ngcontent-%COMP%]   .collection-card[_ngcontent-%COMP%]   .collection-images[_ngcontent-%COMP%]   .preview-images[_ngcontent-%COMP%]   .preview-item[_ngcontent-%COMP%] {\\n  width: 40px;\\n  height: 40px;\\n  border-radius: 8px;\\n  background-size: cover;\\n  background-position: center;\\n  border: 2px solid rgba(255, 255, 255, 0.8);\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);\\n}\\n.collections-content[_ngcontent-%COMP%]   .other-collections[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));\\n  gap: 20px;\\n  margin-bottom: 24px;\\n}\\n.collections-content[_ngcontent-%COMP%]   .other-collections[_ngcontent-%COMP%]   .collection-item[_ngcontent-%COMP%] {\\n  background: white;\\n  border-radius: 12px;\\n  overflow: hidden;\\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n}\\n.collections-content[_ngcontent-%COMP%]   .other-collections[_ngcontent-%COMP%]   .collection-item[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-2px);\\n  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);\\n}\\n.collections-content[_ngcontent-%COMP%]   .other-collections[_ngcontent-%COMP%]   .collection-item[_ngcontent-%COMP%]   .collection-image-container[_ngcontent-%COMP%] {\\n  position: relative;\\n  width: 100%;\\n  height: 180px;\\n  overflow: hidden;\\n}\\n.collections-content[_ngcontent-%COMP%]   .other-collections[_ngcontent-%COMP%]   .collection-item[_ngcontent-%COMP%]   .collection-image-container[_ngcontent-%COMP%]   .collection-image[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 100%;\\n  object-fit: cover;\\n  transition: transform 0.3s ease;\\n}\\n.collections-content[_ngcontent-%COMP%]   .other-collections[_ngcontent-%COMP%]   .collection-item[_ngcontent-%COMP%]   .collection-image-container[_ngcontent-%COMP%]:hover   .collection-image[_ngcontent-%COMP%] {\\n  transform: scale(1.05);\\n}\\n.collections-content[_ngcontent-%COMP%]   .other-collections[_ngcontent-%COMP%]   .collection-item[_ngcontent-%COMP%]   .collection-image-container[_ngcontent-%COMP%]   .collection-overlay[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  background: rgba(0, 0, 0, 0.6);\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  opacity: 0;\\n  transition: opacity 0.3s ease;\\n}\\n.collections-content[_ngcontent-%COMP%]   .other-collections[_ngcontent-%COMP%]   .collection-item[_ngcontent-%COMP%]   .collection-image-container[_ngcontent-%COMP%]   .collection-overlay[_ngcontent-%COMP%]   .collection-details[_ngcontent-%COMP%] {\\n  text-align: center;\\n  color: white;\\n  padding: 16px;\\n}\\n.collections-content[_ngcontent-%COMP%]   .other-collections[_ngcontent-%COMP%]   .collection-item[_ngcontent-%COMP%]   .collection-image-container[_ngcontent-%COMP%]   .collection-overlay[_ngcontent-%COMP%]   .collection-details[_ngcontent-%COMP%]   .collection-name[_ngcontent-%COMP%] {\\n  font-size: 18px;\\n  font-weight: 600;\\n  margin: 0 0 8px 0;\\n}\\n.collections-content[_ngcontent-%COMP%]   .other-collections[_ngcontent-%COMP%]   .collection-item[_ngcontent-%COMP%]   .collection-image-container[_ngcontent-%COMP%]   .collection-overlay[_ngcontent-%COMP%]   .collection-details[_ngcontent-%COMP%]   .collection-description[_ngcontent-%COMP%] {\\n  font-size: 13px;\\n  margin: 0 0 8px 0;\\n  opacity: 0.9;\\n}\\n.collections-content[_ngcontent-%COMP%]   .other-collections[_ngcontent-%COMP%]   .collection-item[_ngcontent-%COMP%]   .collection-image-container[_ngcontent-%COMP%]   .collection-overlay[_ngcontent-%COMP%]   .collection-details[_ngcontent-%COMP%]   .collection-meta[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: center;\\n  gap: 12px;\\n  font-size: 11px;\\n}\\n.collections-content[_ngcontent-%COMP%]   .other-collections[_ngcontent-%COMP%]   .collection-item[_ngcontent-%COMP%]   .collection-image-container[_ngcontent-%COMP%]   .collection-overlay[_ngcontent-%COMP%]   .collection-details[_ngcontent-%COMP%]   .collection-meta[_ngcontent-%COMP%]   .item-count[_ngcontent-%COMP%] {\\n  color: #ffd700;\\n}\\n.collections-content[_ngcontent-%COMP%]   .other-collections[_ngcontent-%COMP%]   .collection-item[_ngcontent-%COMP%]   .collection-image-container[_ngcontent-%COMP%]   .collection-overlay[_ngcontent-%COMP%]   .collection-details[_ngcontent-%COMP%]   .collection-meta[_ngcontent-%COMP%]   .curator[_ngcontent-%COMP%] {\\n  color: #4ecdc4;\\n}\\n.collections-content[_ngcontent-%COMP%]   .other-collections[_ngcontent-%COMP%]   .collection-item[_ngcontent-%COMP%]   .collection-image-container[_ngcontent-%COMP%]:hover   .collection-overlay[_ngcontent-%COMP%] {\\n  opacity: 1;\\n}\\n.collections-content[_ngcontent-%COMP%]   .other-collections[_ngcontent-%COMP%]   .collection-item[_ngcontent-%COMP%]   .collection-image-container[_ngcontent-%COMP%]   .collection-tag[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 8px;\\n  left: 8px;\\n  color: white;\\n  padding: 4px 8px;\\n  border-radius: 12px;\\n  font-size: 10px;\\n  font-weight: 600;\\n  text-transform: uppercase;\\n}\\n.collections-content[_ngcontent-%COMP%]   .other-collections[_ngcontent-%COMP%]   .collection-item[_ngcontent-%COMP%]   .collection-image-container[_ngcontent-%COMP%]   .like-btn[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 8px;\\n  right: 8px;\\n  background: rgba(255, 255, 255, 0.9);\\n  border: none;\\n  border-radius: 50%;\\n  width: 32px;\\n  height: 32px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n}\\n.collections-content[_ngcontent-%COMP%]   .other-collections[_ngcontent-%COMP%]   .collection-item[_ngcontent-%COMP%]   .collection-image-container[_ngcontent-%COMP%]   .like-btn[_ngcontent-%COMP%]:hover {\\n  background: white;\\n  transform: scale(1.1);\\n}\\n.collections-content[_ngcontent-%COMP%]   .other-collections[_ngcontent-%COMP%]   .collection-item[_ngcontent-%COMP%]   .collection-image-container[_ngcontent-%COMP%]   .like-btn.active[_ngcontent-%COMP%] {\\n  background: #ff6b6b;\\n  color: white;\\n}\\n.collections-content[_ngcontent-%COMP%]   .other-collections[_ngcontent-%COMP%]   .collection-item[_ngcontent-%COMP%]   .collection-image-container[_ngcontent-%COMP%]   .like-btn[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n}\\n.collections-content[_ngcontent-%COMP%]   .other-collections[_ngcontent-%COMP%]   .collection-item[_ngcontent-%COMP%]   .collection-footer[_ngcontent-%COMP%] {\\n  padding: 16px;\\n}\\n.collections-content[_ngcontent-%COMP%]   .other-collections[_ngcontent-%COMP%]   .collection-item[_ngcontent-%COMP%]   .collection-footer[_ngcontent-%COMP%]   .price-info[_ngcontent-%COMP%] {\\n  margin-bottom: 12px;\\n}\\n.collections-content[_ngcontent-%COMP%]   .other-collections[_ngcontent-%COMP%]   .collection-item[_ngcontent-%COMP%]   .collection-footer[_ngcontent-%COMP%]   .price-info[_ngcontent-%COMP%]   .price-range[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  font-weight: 600;\\n  color: #333;\\n  display: block;\\n}\\n.collections-content[_ngcontent-%COMP%]   .other-collections[_ngcontent-%COMP%]   .collection-item[_ngcontent-%COMP%]   .collection-footer[_ngcontent-%COMP%]   .price-info[_ngcontent-%COMP%]   .avg-price[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  color: #666;\\n}\\n.collections-content[_ngcontent-%COMP%]   .other-collections[_ngcontent-%COMP%]   .collection-item[_ngcontent-%COMP%]   .collection-footer[_ngcontent-%COMP%]   .collection-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 8px;\\n}\\n.collections-content[_ngcontent-%COMP%]   .other-collections[_ngcontent-%COMP%]   .collection-item[_ngcontent-%COMP%]   .collection-footer[_ngcontent-%COMP%]   .collection-actions[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%] {\\n  flex: 1;\\n  padding: 8px 12px;\\n  border: none;\\n  border-radius: 8px;\\n  font-size: 12px;\\n  font-weight: 600;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  gap: 4px;\\n}\\n.collections-content[_ngcontent-%COMP%]   .other-collections[_ngcontent-%COMP%]   .collection-item[_ngcontent-%COMP%]   .collection-footer[_ngcontent-%COMP%]   .collection-actions[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n}\\n.collections-content[_ngcontent-%COMP%]   .other-collections[_ngcontent-%COMP%]   .collection-item[_ngcontent-%COMP%]   .collection-footer[_ngcontent-%COMP%]   .collection-actions[_ngcontent-%COMP%]   .action-btn.view-btn[_ngcontent-%COMP%] {\\n  background: #6c5ce7;\\n  color: white;\\n}\\n.collections-content[_ngcontent-%COMP%]   .other-collections[_ngcontent-%COMP%]   .collection-item[_ngcontent-%COMP%]   .collection-footer[_ngcontent-%COMP%]   .collection-actions[_ngcontent-%COMP%]   .action-btn.view-btn[_ngcontent-%COMP%]:hover {\\n  background: #5a4fcf;\\n}\\n.collections-content[_ngcontent-%COMP%]   .other-collections[_ngcontent-%COMP%]   .collection-item[_ngcontent-%COMP%]   .collection-footer[_ngcontent-%COMP%]   .collection-actions[_ngcontent-%COMP%]   .action-btn.save-btn[_ngcontent-%COMP%] {\\n  background: #f8f9fa;\\n  color: #666;\\n  border: 1px solid #dee2e6;\\n}\\n.collections-content[_ngcontent-%COMP%]   .other-collections[_ngcontent-%COMP%]   .collection-item[_ngcontent-%COMP%]   .collection-footer[_ngcontent-%COMP%]   .collection-actions[_ngcontent-%COMP%]   .action-btn.save-btn[_ngcontent-%COMP%]:hover {\\n  background: #e9ecef;\\n}\\n.collections-content[_ngcontent-%COMP%]   .other-collections[_ngcontent-%COMP%]   .collection-item[_ngcontent-%COMP%]   .collection-footer[_ngcontent-%COMP%]   .collection-actions[_ngcontent-%COMP%]   .action-btn.save-btn.saved[_ngcontent-%COMP%] {\\n  background: #28a745;\\n  color: white;\\n  border-color: #28a745;\\n}\\n.collections-content[_ngcontent-%COMP%]   .collection-categories[_ngcontent-%COMP%] {\\n  margin-bottom: 24px;\\n}\\n.collections-content[_ngcontent-%COMP%]   .collection-categories[_ngcontent-%COMP%]   .categories-title[_ngcontent-%COMP%] {\\n  font-size: 18px;\\n  font-weight: 600;\\n  margin: 0 0 16px 0;\\n  color: #333;\\n}\\n.collections-content[_ngcontent-%COMP%]   .collection-categories[_ngcontent-%COMP%]   .categories-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\\n  gap: 12px;\\n}\\n.collections-content[_ngcontent-%COMP%]   .collection-categories[_ngcontent-%COMP%]   .categories-grid[_ngcontent-%COMP%]   .category-item[_ngcontent-%COMP%] {\\n  border: none;\\n  border-radius: 12px;\\n  padding: 16px;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n  display: flex;\\n  align-items: center;\\n  gap: 12px;\\n  color: white;\\n  text-align: left;\\n}\\n.collections-content[_ngcontent-%COMP%]   .collection-categories[_ngcontent-%COMP%]   .categories-grid[_ngcontent-%COMP%]   .category-item[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-2px);\\n  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);\\n}\\n.collections-content[_ngcontent-%COMP%]   .collection-categories[_ngcontent-%COMP%]   .categories-grid[_ngcontent-%COMP%]   .category-item[_ngcontent-%COMP%]   .category-icon[_ngcontent-%COMP%] {\\n  width: 40px;\\n  height: 40px;\\n  background: rgba(255, 255, 255, 0.2);\\n  border-radius: 50%;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n}\\n.collections-content[_ngcontent-%COMP%]   .collection-categories[_ngcontent-%COMP%]   .categories-grid[_ngcontent-%COMP%]   .category-item[_ngcontent-%COMP%]   .category-icon[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n  font-size: 20px;\\n  color: white;\\n}\\n.collections-content[_ngcontent-%COMP%]   .collection-categories[_ngcontent-%COMP%]   .categories-grid[_ngcontent-%COMP%]   .category-item[_ngcontent-%COMP%]   .category-info[_ngcontent-%COMP%]   .category-name[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  font-weight: 600;\\n  margin: 0 0 4px 0;\\n  color: white;\\n}\\n.collections-content[_ngcontent-%COMP%]   .collection-categories[_ngcontent-%COMP%]   .categories-grid[_ngcontent-%COMP%]   .category-item[_ngcontent-%COMP%]   .category-info[_ngcontent-%COMP%]   .category-count[_ngcontent-%COMP%] {\\n  font-size: 11px;\\n  opacity: 0.9;\\n}\\n.collections-content[_ngcontent-%COMP%]   .trending-stats[_ngcontent-%COMP%]   .stats-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));\\n  gap: 16px;\\n  padding: 20px;\\n  background: linear-gradient(135deg, #f8f9fa, #e9ecef);\\n  border-radius: 12px;\\n}\\n.collections-content[_ngcontent-%COMP%]   .trending-stats[_ngcontent-%COMP%]   .stats-grid[_ngcontent-%COMP%]   .stat-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 12px;\\n  padding: 12px;\\n  background: white;\\n  border-radius: 8px;\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\\n}\\n.collections-content[_ngcontent-%COMP%]   .trending-stats[_ngcontent-%COMP%]   .stats-grid[_ngcontent-%COMP%]   .stat-item[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n  font-size: 24px;\\n  color: #6c5ce7;\\n}\\n.collections-content[_ngcontent-%COMP%]   .trending-stats[_ngcontent-%COMP%]   .stats-grid[_ngcontent-%COMP%]   .stat-item[_ngcontent-%COMP%]   .stat-info[_ngcontent-%COMP%]   .stat-value[_ngcontent-%COMP%] {\\n  display: block;\\n  font-size: 18px;\\n  font-weight: 700;\\n  color: #333;\\n}\\n.collections-content[_ngcontent-%COMP%]   .trending-stats[_ngcontent-%COMP%]   .stats-grid[_ngcontent-%COMP%]   .stat-item[_ngcontent-%COMP%]   .stat-info[_ngcontent-%COMP%]   .stat-label[_ngcontent-%COMP%] {\\n  font-size: 11px;\\n  color: #666;\\n  text-transform: uppercase;\\n  letter-spacing: 0.5px;\\n}\\n\\n@keyframes _ngcontent-%COMP%_flicker {\\n  0% {\\n    opacity: 1;\\n  }\\n  100% {\\n    opacity: 0.7;\\n  }\\n}\\n@media (max-width: 768px) {\\n  .collections-content[_ngcontent-%COMP%]   .featured-collection[_ngcontent-%COMP%]   .collection-card[_ngcontent-%COMP%]   .collection-images[_ngcontent-%COMP%]   .main-image[_ngcontent-%COMP%] {\\n    height: 200px;\\n  }\\n  .collections-content[_ngcontent-%COMP%]   .featured-collection[_ngcontent-%COMP%]   .collection-card[_ngcontent-%COMP%]   .collection-images[_ngcontent-%COMP%]   .main-image[_ngcontent-%COMP%]   .collection-overlay[_ngcontent-%COMP%] {\\n    padding: 16px;\\n  }\\n  .collections-content[_ngcontent-%COMP%]   .featured-collection[_ngcontent-%COMP%]   .collection-card[_ngcontent-%COMP%]   .collection-images[_ngcontent-%COMP%]   .main-image[_ngcontent-%COMP%]   .collection-overlay[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .collection-name[_ngcontent-%COMP%] {\\n    font-size: 20px;\\n  }\\n  .collections-content[_ngcontent-%COMP%]   .featured-collection[_ngcontent-%COMP%]   .collection-card[_ngcontent-%COMP%]   .collection-images[_ngcontent-%COMP%]   .main-image[_ngcontent-%COMP%]   .collection-overlay[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .collection-description[_ngcontent-%COMP%] {\\n    font-size: 13px;\\n  }\\n  .collections-content[_ngcontent-%COMP%]   .featured-collection[_ngcontent-%COMP%]   .collection-card[_ngcontent-%COMP%]   .collection-images[_ngcontent-%COMP%]   .main-image[_ngcontent-%COMP%]   .trending-badge[_ngcontent-%COMP%] {\\n    top: 12px;\\n    right: 12px;\\n    padding: 6px 10px;\\n    font-size: 11px;\\n  }\\n  .collections-content[_ngcontent-%COMP%]   .featured-collection[_ngcontent-%COMP%]   .collection-card[_ngcontent-%COMP%]   .collection-images[_ngcontent-%COMP%]   .preview-images[_ngcontent-%COMP%] {\\n    top: 12px;\\n    left: 12px;\\n  }\\n  .collections-content[_ngcontent-%COMP%]   .featured-collection[_ngcontent-%COMP%]   .collection-card[_ngcontent-%COMP%]   .collection-images[_ngcontent-%COMP%]   .preview-images[_ngcontent-%COMP%]   .preview-item[_ngcontent-%COMP%] {\\n    width: 35px;\\n    height: 35px;\\n  }\\n  .collections-content[_ngcontent-%COMP%]   .other-collections[_ngcontent-%COMP%] {\\n    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\\n    gap: 16px;\\n  }\\n  .collections-content[_ngcontent-%COMP%]   .other-collections[_ngcontent-%COMP%]   .collection-item[_ngcontent-%COMP%]   .collection-image-container[_ngcontent-%COMP%] {\\n    height: 150px;\\n  }\\n  .collections-content[_ngcontent-%COMP%]   .other-collections[_ngcontent-%COMP%]   .collection-item[_ngcontent-%COMP%]   .collection-image-container[_ngcontent-%COMP%]   .collection-overlay[_ngcontent-%COMP%]   .collection-details[_ngcontent-%COMP%] {\\n    padding: 12px;\\n  }\\n  .collections-content[_ngcontent-%COMP%]   .other-collections[_ngcontent-%COMP%]   .collection-item[_ngcontent-%COMP%]   .collection-image-container[_ngcontent-%COMP%]   .collection-overlay[_ngcontent-%COMP%]   .collection-details[_ngcontent-%COMP%]   .collection-name[_ngcontent-%COMP%] {\\n    font-size: 16px;\\n  }\\n  .collections-content[_ngcontent-%COMP%]   .other-collections[_ngcontent-%COMP%]   .collection-item[_ngcontent-%COMP%]   .collection-image-container[_ngcontent-%COMP%]   .collection-overlay[_ngcontent-%COMP%]   .collection-details[_ngcontent-%COMP%]   .collection-description[_ngcontent-%COMP%] {\\n    font-size: 12px;\\n  }\\n  .collections-content[_ngcontent-%COMP%]   .other-collections[_ngcontent-%COMP%]   .collection-item[_ngcontent-%COMP%]   .collection-footer[_ngcontent-%COMP%] {\\n    padding: 12px;\\n  }\\n  .collections-content[_ngcontent-%COMP%]   .other-collections[_ngcontent-%COMP%]   .collection-item[_ngcontent-%COMP%]   .collection-footer[_ngcontent-%COMP%]   .collection-actions[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%] {\\n    padding: 6px 10px;\\n    font-size: 11px;\\n  }\\n  .collections-content[_ngcontent-%COMP%]   .collection-categories[_ngcontent-%COMP%]   .categories-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));\\n  }\\n  .collections-content[_ngcontent-%COMP%]   .collection-categories[_ngcontent-%COMP%]   .categories-grid[_ngcontent-%COMP%]   .category-item[_ngcontent-%COMP%] {\\n    padding: 12px;\\n  }\\n  .collections-content[_ngcontent-%COMP%]   .collection-categories[_ngcontent-%COMP%]   .categories-grid[_ngcontent-%COMP%]   .category-item[_ngcontent-%COMP%]   .category-icon[_ngcontent-%COMP%] {\\n    width: 35px;\\n    height: 35px;\\n  }\\n  .collections-content[_ngcontent-%COMP%]   .collection-categories[_ngcontent-%COMP%]   .categories-grid[_ngcontent-%COMP%]   .category-item[_ngcontent-%COMP%]   .category-icon[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n    font-size: 18px;\\n  }\\n  .collections-content[_ngcontent-%COMP%]   .collection-categories[_ngcontent-%COMP%]   .categories-grid[_ngcontent-%COMP%]   .category-item[_ngcontent-%COMP%]   .category-info[_ngcontent-%COMP%]   .category-name[_ngcontent-%COMP%] {\\n    font-size: 13px;\\n  }\\n  .collections-content[_ngcontent-%COMP%]   .collection-categories[_ngcontent-%COMP%]   .categories-grid[_ngcontent-%COMP%]   .category-item[_ngcontent-%COMP%]   .category-info[_ngcontent-%COMP%]   .category-count[_ngcontent-%COMP%] {\\n    font-size: 10px;\\n  }\\n  .collections-content[_ngcontent-%COMP%]   .trending-stats[_ngcontent-%COMP%]   .stats-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));\\n    gap: 12px;\\n    padding: 16px;\\n  }\\n  .collections-content[_ngcontent-%COMP%]   .trending-stats[_ngcontent-%COMP%]   .stats-grid[_ngcontent-%COMP%]   .stat-item[_ngcontent-%COMP%] {\\n    padding: 10px;\\n  }\\n  .collections-content[_ngcontent-%COMP%]   .trending-stats[_ngcontent-%COMP%]   .stats-grid[_ngcontent-%COMP%]   .stat-item[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n    font-size: 20px;\\n  }\\n  .collections-content[_ngcontent-%COMP%]   .trending-stats[_ngcontent-%COMP%]   .stats-grid[_ngcontent-%COMP%]   .stat-item[_ngcontent-%COMP%]   .stat-info[_ngcontent-%COMP%]   .stat-value[_ngcontent-%COMP%] {\\n    font-size: 16px;\\n  }\\n  .collections-content[_ngcontent-%COMP%]   .trending-stats[_ngcontent-%COMP%]   .stats-grid[_ngcontent-%COMP%]   .stat-item[_ngcontent-%COMP%]   .stat-info[_ngcontent-%COMP%]   .stat-label[_ngcontent-%COMP%] {\\n    font-size: 10px;\\n  }\\n}\\n@media (max-width: 425px) {\\n  .collections-content[_ngcontent-%COMP%]   .featured-collection[_ngcontent-%COMP%]   .collection-card[_ngcontent-%COMP%]   .collection-images[_ngcontent-%COMP%]   .main-image[_ngcontent-%COMP%] {\\n    height: 160px;\\n  }\\n  .collections-content[_ngcontent-%COMP%]   .featured-collection[_ngcontent-%COMP%]   .collection-card[_ngcontent-%COMP%]   .collection-images[_ngcontent-%COMP%]   .main-image[_ngcontent-%COMP%]   .collection-overlay[_ngcontent-%COMP%] {\\n    padding: 12px;\\n  }\\n  .collections-content[_ngcontent-%COMP%]   .featured-collection[_ngcontent-%COMP%]   .collection-card[_ngcontent-%COMP%]   .collection-images[_ngcontent-%COMP%]   .main-image[_ngcontent-%COMP%]   .collection-overlay[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .collection-name[_ngcontent-%COMP%] {\\n    font-size: 18px;\\n  }\\n  .collections-content[_ngcontent-%COMP%]   .featured-collection[_ngcontent-%COMP%]   .collection-card[_ngcontent-%COMP%]   .collection-images[_ngcontent-%COMP%]   .main-image[_ngcontent-%COMP%]   .collection-overlay[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .collection-description[_ngcontent-%COMP%] {\\n    font-size: 12px;\\n  }\\n  .collections-content[_ngcontent-%COMP%]   .featured-collection[_ngcontent-%COMP%]   .collection-card[_ngcontent-%COMP%]   .collection-images[_ngcontent-%COMP%]   .main-image[_ngcontent-%COMP%]   .collection-overlay[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .collection-stats[_ngcontent-%COMP%] {\\n    font-size: 11px;\\n  }\\n  .collections-content[_ngcontent-%COMP%]   .featured-collection[_ngcontent-%COMP%]   .collection-card[_ngcontent-%COMP%]   .collection-images[_ngcontent-%COMP%]   .preview-images[_ngcontent-%COMP%]   .preview-item[_ngcontent-%COMP%] {\\n    width: 30px;\\n    height: 30px;\\n  }\\n  .collections-content[_ngcontent-%COMP%]   .other-collections[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n    gap: 12px;\\n  }\\n  .collections-content[_ngcontent-%COMP%]   .other-collections[_ngcontent-%COMP%]   .collection-item[_ngcontent-%COMP%]   .collection-image-container[_ngcontent-%COMP%] {\\n    height: 120px;\\n  }\\n  .collections-content[_ngcontent-%COMP%]   .other-collections[_ngcontent-%COMP%]   .collection-item[_ngcontent-%COMP%]   .collection-footer[_ngcontent-%COMP%] {\\n    padding: 10px;\\n  }\\n  .collections-content[_ngcontent-%COMP%]   .other-collections[_ngcontent-%COMP%]   .collection-item[_ngcontent-%COMP%]   .collection-footer[_ngcontent-%COMP%]   .price-info[_ngcontent-%COMP%] {\\n    margin-bottom: 8px;\\n  }\\n  .collections-content[_ngcontent-%COMP%]   .other-collections[_ngcontent-%COMP%]   .collection-item[_ngcontent-%COMP%]   .collection-footer[_ngcontent-%COMP%]   .price-info[_ngcontent-%COMP%]   .price-range[_ngcontent-%COMP%] {\\n    font-size: 13px;\\n  }\\n  .collections-content[_ngcontent-%COMP%]   .other-collections[_ngcontent-%COMP%]   .collection-item[_ngcontent-%COMP%]   .collection-footer[_ngcontent-%COMP%]   .price-info[_ngcontent-%COMP%]   .avg-price[_ngcontent-%COMP%] {\\n    font-size: 11px;\\n  }\\n  .collections-content[_ngcontent-%COMP%]   .other-collections[_ngcontent-%COMP%]   .collection-item[_ngcontent-%COMP%]   .collection-footer[_ngcontent-%COMP%]   .collection-actions[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%] {\\n    padding: 5px 8px;\\n    font-size: 10px;\\n  }\\n  .collections-content[_ngcontent-%COMP%]   .collection-categories[_ngcontent-%COMP%]   .categories-title[_ngcontent-%COMP%] {\\n    font-size: 16px;\\n  }\\n  .collections-content[_ngcontent-%COMP%]   .collection-categories[_ngcontent-%COMP%]   .categories-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n  }\\n  .collections-content[_ngcontent-%COMP%]   .collection-categories[_ngcontent-%COMP%]   .categories-grid[_ngcontent-%COMP%]   .category-item[_ngcontent-%COMP%] {\\n    padding: 10px;\\n  }\\n  .collections-content[_ngcontent-%COMP%]   .collection-categories[_ngcontent-%COMP%]   .categories-grid[_ngcontent-%COMP%]   .category-item[_ngcontent-%COMP%]   .category-icon[_ngcontent-%COMP%] {\\n    width: 30px;\\n    height: 30px;\\n  }\\n  .collections-content[_ngcontent-%COMP%]   .collection-categories[_ngcontent-%COMP%]   .categories-grid[_ngcontent-%COMP%]   .category-item[_ngcontent-%COMP%]   .category-icon[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n    font-size: 16px;\\n  }\\n  .collections-content[_ngcontent-%COMP%]   .collection-categories[_ngcontent-%COMP%]   .categories-grid[_ngcontent-%COMP%]   .category-item[_ngcontent-%COMP%]   .category-info[_ngcontent-%COMP%]   .category-name[_ngcontent-%COMP%] {\\n    font-size: 12px;\\n  }\\n  .collections-content[_ngcontent-%COMP%]   .collection-categories[_ngcontent-%COMP%]   .categories-grid[_ngcontent-%COMP%]   .category-item[_ngcontent-%COMP%]   .category-info[_ngcontent-%COMP%]   .category-count[_ngcontent-%COMP%] {\\n    font-size: 9px;\\n  }\\n  .collections-content[_ngcontent-%COMP%]   .trending-stats[_ngcontent-%COMP%]   .stats-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: repeat(2, 1fr);\\n    gap: 8px;\\n    padding: 12px;\\n  }\\n  .collections-content[_ngcontent-%COMP%]   .trending-stats[_ngcontent-%COMP%]   .stats-grid[_ngcontent-%COMP%]   .stat-item[_ngcontent-%COMP%] {\\n    padding: 8px;\\n    flex-direction: column;\\n    text-align: center;\\n    gap: 6px;\\n  }\\n  .collections-content[_ngcontent-%COMP%]   .trending-stats[_ngcontent-%COMP%]   .stats-grid[_ngcontent-%COMP%]   .stat-item[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n    font-size: 18px;\\n  }\\n  .collections-content[_ngcontent-%COMP%]   .trending-stats[_ngcontent-%COMP%]   .stats-grid[_ngcontent-%COMP%]   .stat-item[_ngcontent-%COMP%]   .stat-info[_ngcontent-%COMP%]   .stat-value[_ngcontent-%COMP%] {\\n    font-size: 14px;\\n  }\\n  .collections-content[_ngcontent-%COMP%]   .trending-stats[_ngcontent-%COMP%]   .stats-grid[_ngcontent-%COMP%]   .stat-item[_ngcontent-%COMP%]   .stat-info[_ngcontent-%COMP%]   .stat-label[_ngcontent-%COMP%] {\\n    font-size: 9px;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "RouterModule", "IonicModule", "Subscription", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵtext", "ɵɵelementEnd", "ɵɵlistener", "TrendingCollectionsComponent_div_9_Template_button_click_4_listener", "ɵɵrestoreView", "_r1", "ctx_r1", "ɵɵnextContext", "ɵɵresetView", "onRetry", "ɵɵadvance", "ɵɵtextInterpolate", "error", "ɵɵstyleProp", "image_r4", "TrendingCollectionsComponent_div_10_div_1_Template_div_click_0_listener", "_r3", "onCollectionClick", "featuredCollection", "ɵɵtemplate", "TrendingCollectionsComponent_div_10_div_1_div_20_Template", "ɵɵproperty", "coverImage", "ɵɵsanitizeUrl", "name", "description", "ɵɵtextInterpolate1", "itemCount", "ɵɵtextInterpolate2", "formatPrice", "priceRange", "min", "max", "previewImages", "slice", "TrendingCollectionsComponent_div_10_div_3_Template_div_click_0_listener", "collection_r6", "_r5", "$implicit", "TrendingCollectionsComponent_div_10_div_3_Template_div_click_16_listener", "$event", "toggleLike", "TrendingCollectionsComponent_div_10_div_3_Template_button_click_25_listener", "TrendingCollectionsComponent_div_10_div_3_Template_button_click_28_listener", "toggleSave", "curator", "category", "color", "ɵɵclassProp", "isCollectionLiked", "_id", "averagePrice", "isCollectionSaved", "TrendingCollectionsComponent_div_10_div_4_button_4_Template_button_click_0_listener", "category_r8", "_r7", "onCategoryClick", "gradient", "icon", "collectionCount", "TrendingCollectionsComponent_div_10_div_4_button_4_Template", "collectionCategories", "trackByCategoryName", "collectionStats", "totalCollections", "formatCount", "totalItems", "totalCurators", "totalLikes", "TrendingCollectionsComponent_div_10_div_1_Template", "TrendingCollectionsComponent_div_10_div_3_Template", "TrendingCollectionsComponent_div_10_div_4_Template", "TrendingCollectionsComponent_div_10_div_5_Template", "otherCollections", "trackByCollectionId", "length", "TrendingCollectionsComponent", "constructor", "router", "trendingCollections", "likedCollections", "Set", "savedCollections", "isLoading", "subscription", "ngOnInit", "loadTrendingCollections", "ngOnDestroy", "unsubscribe", "_this", "_asyncToGenerator", "loadMockCollections", "console", "_this2", "Promise", "resolve", "setTimeout", "avatar", "tags", "analytics", "views", "likes", "saves", "shares", "isActive", "isFeatured", "createdAt", "Date", "reduce", "prev", "current", "filter", "col", "sum", "map", "size", "collection", "event", "stopPropagation", "navigate", "queryParams", "toLowerCase", "has", "delete", "add", "collectionId", "price", "Intl", "NumberFormat", "style", "currency", "minimumFractionDigits", "format", "count", "toFixed", "toString", "index", "onSeeAll", "preventDefault", "ɵɵdirectiveInject", "i1", "Router", "selectors", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "TrendingCollectionsComponent_Template", "rf", "ctx", "TrendingCollectionsComponent_Template_a_click_5_listener", "TrendingCollectionsComponent_div_8_Template", "TrendingCollectionsComponent_div_9_Template", "TrendingCollectionsComponent_div_10_Template", "TrendingCollectionsComponent_div_11_Template", "i2", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "i3", "IonIcon", "Ion<PERSON><PERSON><PERSON>", "styles"], "sources": ["E:\\Fashion\\DFashionFrontend\\frontend\\src\\app\\features\\home\\components\\trending-now\\trending-collections\\trending-collections.component.ts", "E:\\Fashion\\DFashionFrontend\\frontend\\src\\app\\features\\home\\components\\trending-now\\trending-collections\\trending-collections.component.html"], "sourcesContent": ["import { Component, OnInit, OnDestroy } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\nimport { IonicModule } from '@ionic/angular';\nimport { Router } from '@angular/router';\nimport { Subscription } from 'rxjs';\n\ninterface TrendingCollection {\n  _id: string;\n  name: string;\n  description: string;\n  coverImage: string;\n  previewImages: string[];\n  itemCount: number;\n  priceRange: {\n    min: number;\n    max: number;\n  };\n  averagePrice: number;\n  curator: {\n    _id: string;\n    name: string;\n    avatar: string;\n  };\n  category: {\n    name: string;\n    color: string;\n  };\n  tags: string[];\n  analytics: {\n    views: number;\n    likes: number;\n    saves: number;\n    shares: number;\n  };\n  isActive: boolean;\n  isFeatured: boolean;\n  createdAt: Date;\n}\n\ninterface CollectionCategory {\n  name: string;\n  icon: string;\n  gradient: string;\n  collectionCount: number;\n}\n\ninterface CollectionStats {\n  totalCollections: number;\n  totalItems: number;\n  totalCurators: number;\n  totalLikes: number;\n}\n\n@Component({\n  selector: 'app-trending-collections',\n  standalone: true,\n  imports: [\n    CommonModule,\n    RouterModule,\n    IonicModule\n  ],\n  templateUrl: './trending-collections.component.html',\n  styleUrls: ['./trending-collections.component.scss']\n})\nexport class TrendingCollectionsComponent implements OnInit, OnDestroy {\n  trendingCollections: TrendingCollection[] = [];\n  featuredCollection: TrendingCollection | null = null;\n  otherCollections: TrendingCollection[] = [];\n  collectionCategories: CollectionCategory[] = [];\n  collectionStats: CollectionStats | null = null;\n  likedCollections = new Set<string>();\n  savedCollections = new Set<string>();\n  isLoading = true;\n  error: string | null = null;\n  private subscription: Subscription = new Subscription();\n\n  constructor(private router: Router) {}\n\n  ngOnInit() {\n    this.loadTrendingCollections();\n  }\n\n  ngOnDestroy() {\n    this.subscription.unsubscribe();\n  }\n\n  private async loadTrendingCollections() {\n    try {\n      this.isLoading = true;\n      this.error = null;\n      \n      // Mock data for now - replace with actual service call\n      await this.loadMockCollections();\n      \n    } catch (error) {\n      console.error('Error loading trending collections:', error);\n      this.error = 'Failed to load trending collections';\n      this.isLoading = false;\n    }\n  }\n\n  private async loadMockCollections() {\n    // Simulate API call delay\n    await new Promise(resolve => setTimeout(resolve, 1000));\n\n    // Mock trending collections\n    this.trendingCollections = [\n      {\n        _id: 'col1',\n        name: 'Summer Essentials 2024',\n        description: 'Must-have pieces for the perfect summer wardrobe',\n        coverImage: 'https://images.unsplash.com/photo-1515372039744-b8f02a3ae446?w=400&h=300&fit=crop',\n        previewImages: [\n          'https://images.unsplash.com/photo-1441986300917-64674bd600d8?w=150&h=150&fit=crop',\n          'https://images.unsplash.com/photo-1445205170230-053b83016050?w=150&h=150&fit=crop',\n          'https://images.unsplash.com/photo-1434389677669-e08b4cac3105?w=150&h=150&fit=crop'\n        ],\n        itemCount: 24,\n        priceRange: { min: 1999, max: 8999 },\n        averagePrice: 4500,\n        curator: {\n          _id: 'cur1',\n          name: 'Fashion Studio',\n          avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b47c?w=50&h=50&fit=crop&crop=face'\n        },\n        category: { name: 'Summer', color: '#ff6b6b' },\n        tags: ['summer', 'essentials', 'trending'],\n        analytics: { views: 15420, likes: 2340, saves: 890, shares: 156 },\n        isActive: true,\n        isFeatured: true,\n        createdAt: new Date()\n      },\n      {\n        _id: 'col2',\n        name: 'Minimalist Chic',\n        description: 'Clean lines and timeless elegance',\n        coverImage: 'https://images.unsplash.com/photo-1469334031218-e382a71b716b?w=400&h=300&fit=crop',\n        previewImages: [\n          'https://images.unsplash.com/photo-1515372039744-b8f02a3ae446?w=150&h=150&fit=crop',\n          'https://images.unsplash.com/photo-1441986300917-64674bd600d8?w=150&h=150&fit=crop'\n        ],\n        itemCount: 18,\n        priceRange: { min: 2999, max: 12999 },\n        averagePrice: 6500,\n        curator: {\n          _id: 'cur2',\n          name: 'Style Maven',\n          avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=50&h=50&fit=crop&crop=face'\n        },\n        category: { name: 'Minimalist', color: '#4ecdc4' },\n        tags: ['minimalist', 'chic', 'timeless'],\n        analytics: { views: 12890, likes: 1890, saves: 567, shares: 89 },\n        isActive: true,\n        isFeatured: false,\n        createdAt: new Date()\n      },\n      {\n        _id: 'col3',\n        name: 'Street Style Icons',\n        description: 'Urban fashion that makes a statement',\n        coverImage: 'https://images.unsplash.com/photo-1441984904996-e0b6ba687e04?w=400&h=300&fit=crop',\n        previewImages: [\n          'https://images.unsplash.com/photo-1542291026-7eec264c27ff?w=150&h=150&fit=crop',\n          'https://images.unsplash.com/photo-1553062407-98eeb64c6a62?w=150&h=150&fit=crop'\n        ],\n        itemCount: 32,\n        priceRange: { min: 1499, max: 6999 },\n        averagePrice: 3500,\n        curator: {\n          _id: 'cur3',\n          name: 'Urban Collective',\n          avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=50&h=50&fit=crop&crop=face'\n        },\n        category: { name: 'Street Style', color: '#6c5ce7' },\n        tags: ['street', 'urban', 'edgy'],\n        analytics: { views: 18750, likes: 2890, saves: 1234, shares: 234 },\n        isActive: true,\n        isFeatured: false,\n        createdAt: new Date()\n      },\n      {\n        _id: 'col4',\n        name: 'Vintage Revival',\n        description: 'Classic pieces with a modern twist',\n        coverImage: 'https://images.unsplash.com/photo-1434389677669-e08b4cac3105?w=400&h=300&fit=crop',\n        previewImages: [\n          'https://images.unsplash.com/photo-1515372039744-b8f02a3ae446?w=150&h=150&fit=crop'\n        ],\n        itemCount: 21,\n        priceRange: { min: 2499, max: 9999 },\n        averagePrice: 5200,\n        curator: {\n          _id: 'cur4',\n          name: 'Retro Boutique',\n          avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=50&h=50&fit=crop&crop=face'\n        },\n        category: { name: 'Vintage', color: '#f9ca24' },\n        tags: ['vintage', 'classic', 'retro'],\n        analytics: { views: 9870, likes: 1456, saves: 678, shares: 123 },\n        isActive: true,\n        isFeatured: false,\n        createdAt: new Date()\n      }\n    ];\n\n    // Set featured collection (highest likes)\n    this.featuredCollection = this.trendingCollections.reduce((prev, current) => \n      (prev.analytics.likes > current.analytics.likes) ? prev : current\n    );\n\n    // Set other collections (excluding featured)\n    this.otherCollections = this.trendingCollections.filter(col => col._id !== this.featuredCollection?._id);\n\n    // Mock collection categories\n    this.collectionCategories = [\n      {\n        name: 'Summer',\n        icon: 'sunny-outline',\n        gradient: 'linear-gradient(135deg, #ff6b6b, #feca57)',\n        collectionCount: 12\n      },\n      {\n        name: 'Minimalist',\n        icon: 'remove-outline',\n        gradient: 'linear-gradient(135deg, #4ecdc4, #44a08d)',\n        collectionCount: 8\n      },\n      {\n        name: 'Street Style',\n        icon: 'car-outline',\n        gradient: 'linear-gradient(135deg, #6c5ce7, #a29bfe)',\n        collectionCount: 15\n      },\n      {\n        name: 'Vintage',\n        icon: 'time-outline',\n        gradient: 'linear-gradient(135deg, #f9ca24, #f0932b)',\n        collectionCount: 6\n      },\n      {\n        name: 'Formal',\n        icon: 'business-outline',\n        gradient: 'linear-gradient(135deg, #2d3436, #636e72)',\n        collectionCount: 9\n      },\n      {\n        name: 'Casual',\n        icon: 'cafe-outline',\n        gradient: 'linear-gradient(135deg, #00b894, #00cec9)',\n        collectionCount: 18\n      }\n    ];\n\n    // Mock collection stats\n    this.collectionStats = {\n      totalCollections: this.trendingCollections.length,\n      totalItems: this.trendingCollections.reduce((sum, col) => sum + col.itemCount, 0),\n      totalCurators: new Set(this.trendingCollections.map(col => col.curator._id)).size,\n      totalLikes: this.trendingCollections.reduce((sum, col) => sum + col.analytics.likes, 0)\n    };\n\n    this.isLoading = false;\n  }\n\n  onCollectionClick(collection: TrendingCollection, event?: Event) {\n    if (event) {\n      event.stopPropagation();\n    }\n    this.router.navigate(['/collection', collection._id]);\n  }\n\n  onCategoryClick(category: CollectionCategory) {\n    this.router.navigate(['/collections'], {\n      queryParams: { category: category.name.toLowerCase() }\n    });\n  }\n\n  toggleLike(collection: TrendingCollection, event: Event) {\n    event.stopPropagation();\n    \n    if (this.likedCollections.has(collection._id)) {\n      this.likedCollections.delete(collection._id);\n      collection.analytics.likes--;\n    } else {\n      this.likedCollections.add(collection._id);\n      collection.analytics.likes++;\n    }\n  }\n\n  toggleSave(collection: TrendingCollection, event: Event) {\n    event.stopPropagation();\n    \n    if (this.savedCollections.has(collection._id)) {\n      this.savedCollections.delete(collection._id);\n      collection.analytics.saves--;\n    } else {\n      this.savedCollections.add(collection._id);\n      collection.analytics.saves++;\n    }\n  }\n\n  isCollectionLiked(collectionId: string): boolean {\n    return this.likedCollections.has(collectionId);\n  }\n\n  isCollectionSaved(collectionId: string): boolean {\n    return this.savedCollections.has(collectionId);\n  }\n\n  formatPrice(price: number): string {\n    return new Intl.NumberFormat('en-IN', {\n      style: 'currency',\n      currency: 'INR',\n      minimumFractionDigits: 0\n    }).format(price);\n  }\n\n  formatCount(count: number): string {\n    if (count >= 1000000) {\n      return (count / 1000000).toFixed(1) + 'M';\n    } else if (count >= 1000) {\n      return (count / 1000).toFixed(1) + 'K';\n    }\n    return count.toString();\n  }\n\n  trackByCollectionId(index: number, collection: TrendingCollection): string {\n    return collection._id;\n  }\n\n  trackByCategoryName(index: number, category: CollectionCategory): string {\n    return category.name;\n  }\n\n  onSeeAll(event: Event) {\n    event.preventDefault();\n    this.router.navigate(['/trending/collections']);\n  }\n\n  onRetry() {\n    this.loadTrendingCollections();\n  }\n}\n", "<div class=\"component-container\">\n  <!-- Component Header -->\n  <div class=\"component-header\">\n    <h3>\n      <ion-icon name=\"albums-outline\"></ion-icon>\n      Trending Collections\n    </h3>\n    <a href=\"#\" class=\"see-all\" (click)=\"onSeeAll($event)\">See All</a>\n  </div>\n\n  <!-- Component Content -->\n  <div class=\"component-content\">\n    <!-- Loading State -->\n    <div *ngIf=\"isLoading\" class=\"loading-container\">\n      <ion-spinner name=\"crescent\"></ion-spinner>\n      <p>Loading trending collections...</p>\n    </div>\n\n    <!-- Error State -->\n    <div *ngIf=\"error && !isLoading\" class=\"error-container\">\n      <ion-icon name=\"alert-circle-outline\"></ion-icon>\n      <p>{{ error }}</p>\n      <button class=\"retry-btn\" (click)=\"onRetry()\">Try Again</button>\n    </div>\n\n    <!-- Collections Content -->\n    <div *ngIf=\"!isLoading && !error && trendingCollections.length > 0\" class=\"collections-content\">\n      <!-- Featured Collection -->\n      <div class=\"featured-collection\" *ngIf=\"featuredCollection\" (click)=\"onCollectionClick(featuredCollection)\">\n        <div class=\"collection-card\">\n          <div class=\"collection-images\">\n            <div class=\"main-image\">\n              <img [src]=\"featuredCollection.coverImage\" [alt]=\"featuredCollection.name\" class=\"cover-image\">\n              <div class=\"collection-overlay\">\n                <div class=\"collection-info\">\n                  <h4 class=\"collection-name\">{{ featuredCollection.name }}</h4>\n                  <p class=\"collection-description\">{{ featuredCollection.description }}</p>\n                  <div class=\"collection-stats\">\n                    <span class=\"item-count\">{{ featuredCollection.itemCount }} items</span>\n                    <span class=\"price-range\">{{ formatPrice(featuredCollection.priceRange.min) }} - {{ formatPrice(featuredCollection.priceRange.max) }}</span>\n                  </div>\n                </div>\n              </div>\n              <div class=\"trending-badge\">\n                <ion-icon name=\"flame\"></ion-icon>\n                Hot Collection\n              </div>\n            </div>\n            <div class=\"preview-images\">\n              <div \n                *ngFor=\"let image of featuredCollection.previewImages.slice(0, 3); let i = index\"\n                class=\"preview-item\"\n                [style.background-image]=\"'url(' + image + ')'\"\n              ></div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <!-- Other Collections Grid -->\n      <div class=\"other-collections\">\n        <div \n          *ngFor=\"let collection of otherCollections; trackBy: trackByCollectionId\" \n          class=\"collection-item\"\n          (click)=\"onCollectionClick(collection)\"\n        >\n          <div class=\"collection-image-container\">\n            <img [src]=\"collection.coverImage\" [alt]=\"collection.name\" class=\"collection-image\">\n            <div class=\"collection-overlay\">\n              <div class=\"collection-details\">\n                <h5 class=\"collection-name\">{{ collection.name }}</h5>\n                <p class=\"collection-description\">{{ collection.description }}</p>\n                <div class=\"collection-meta\">\n                  <span class=\"item-count\">{{ collection.itemCount }} items</span>\n                  <span class=\"curator\">by {{ collection.curator.name }}</span>\n                </div>\n              </div>\n            </div>\n            <div class=\"collection-tag\" [style.background-color]=\"collection.category.color\">\n              {{ collection.category.name }}\n            </div>\n            <div class=\"like-btn\" [class.active]=\"isCollectionLiked(collection._id)\" (click)=\"toggleLike(collection, $event)\">\n              <ion-icon [name]=\"isCollectionLiked(collection._id) ? 'heart' : 'heart-outline'\"></ion-icon>\n            </div>\n          </div>\n          <div class=\"collection-footer\">\n            <div class=\"price-info\">\n              <span class=\"price-range\">{{ formatPrice(collection.priceRange.min) }} - {{ formatPrice(collection.priceRange.max) }}</span>\n              <span class=\"avg-price\">Avg: {{ formatPrice(collection.averagePrice) }}</span>\n            </div>\n            <div class=\"collection-actions\">\n              <button class=\"action-btn view-btn\" (click)=\"onCollectionClick(collection, $event)\">\n                <ion-icon name=\"eye-outline\"></ion-icon>\n                View\n              </button>\n              <button class=\"action-btn save-btn\" [class.saved]=\"isCollectionSaved(collection._id)\" (click)=\"toggleSave(collection, $event)\">\n                <ion-icon [name]=\"isCollectionSaved(collection._id) ? 'bookmark' : 'bookmark-outline'\"></ion-icon>\n                {{ isCollectionSaved(collection._id) ? 'Saved' : 'Save' }}\n              </button>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <!-- Collection Categories -->\n      <div class=\"collection-categories\" *ngIf=\"collectionCategories.length > 0\">\n        <h4 class=\"categories-title\">Browse by Category</h4>\n        <div class=\"categories-grid\">\n          <button \n            *ngFor=\"let category of collectionCategories; trackBy: trackByCategoryName\"\n            class=\"category-item\"\n            [style.background]=\"category.gradient\"\n            (click)=\"onCategoryClick(category)\"\n          >\n            <div class=\"category-icon\">\n              <ion-icon [name]=\"category.icon\"></ion-icon>\n            </div>\n            <div class=\"category-info\">\n              <h5 class=\"category-name\">{{ category.name }}</h5>\n              <span class=\"category-count\">{{ category.collectionCount }} collections</span>\n            </div>\n          </button>\n        </div>\n      </div>\n\n      <!-- Trending Stats -->\n      <div class=\"trending-stats\" *ngIf=\"collectionStats\">\n        <div class=\"stats-grid\">\n          <div class=\"stat-item\">\n            <ion-icon name=\"albums-outline\"></ion-icon>\n            <div class=\"stat-info\">\n              <span class=\"stat-value\">{{ collectionStats.totalCollections }}</span>\n              <span class=\"stat-label\">Total Collections</span>\n            </div>\n          </div>\n          <div class=\"stat-item\">\n            <ion-icon name=\"bag-outline\"></ion-icon>\n            <div class=\"stat-info\">\n              <span class=\"stat-value\">{{ formatCount(collectionStats.totalItems) }}</span>\n              <span class=\"stat-label\">Total Items</span>\n            </div>\n          </div>\n          <div class=\"stat-item\">\n            <ion-icon name=\"people-outline\"></ion-icon>\n            <div class=\"stat-info\">\n              <span class=\"stat-value\">{{ formatCount(collectionStats.totalCurators) }}</span>\n              <span class=\"stat-label\">Curators</span>\n            </div>\n          </div>\n          <div class=\"stat-item\">\n            <ion-icon name=\"heart-outline\"></ion-icon>\n            <div class=\"stat-info\">\n              <span class=\"stat-value\">{{ formatCount(collectionStats.totalLikes) }}</span>\n              <span class=\"stat-label\">Total Likes</span>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- Empty State -->\n    <div *ngIf=\"!isLoading && !error && trendingCollections.length === 0\" class=\"empty-container\">\n      <ion-icon name=\"albums-outline\"></ion-icon>\n      <p>No trending collections available</p>\n    </div>\n  </div>\n</div>\n"], "mappings": ";AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,QAAQ,gBAAgB;AAE5C,SAASC,YAAY,QAAQ,MAAM;;;;;;;ICQ/BC,EAAA,CAAAC,cAAA,aAAiD;IAC/CD,EAAA,CAAAE,SAAA,sBAA2C;IAC3CF,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAG,MAAA,sCAA+B;IACpCH,EADoC,CAAAI,YAAA,EAAI,EAClC;;;;;;IAGNJ,EAAA,CAAAC,cAAA,cAAyD;IACvDD,EAAA,CAAAE,SAAA,mBAAiD;IACjDF,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAG,MAAA,GAAW;IAAAH,EAAA,CAAAI,YAAA,EAAI;IAClBJ,EAAA,CAAAC,cAAA,iBAA8C;IAApBD,EAAA,CAAAK,UAAA,mBAAAC,oEAAA;MAAAN,EAAA,CAAAO,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAG,OAAA,EAAS;IAAA,EAAC;IAACZ,EAAA,CAAAG,MAAA,gBAAS;IACzDH,EADyD,CAAAI,YAAA,EAAS,EAC5D;;;;IAFDJ,EAAA,CAAAa,SAAA,GAAW;IAAXb,EAAA,CAAAc,iBAAA,CAAAL,MAAA,CAAAM,KAAA,CAAW;;;;;IA4BNf,EAAA,CAAAE,SAAA,cAIO;;;;IADLF,EAAA,CAAAgB,WAAA,8BAAAC,QAAA,OAA+C;;;;;;IAxBzDjB,EAAA,CAAAC,cAAA,cAA4G;IAAhDD,EAAA,CAAAK,UAAA,mBAAAa,wEAAA;MAAAlB,EAAA,CAAAO,aAAA,CAAAY,GAAA;MAAA,MAAAV,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAW,iBAAA,CAAAX,MAAA,CAAAY,kBAAA,CAAqC;IAAA,EAAC;IAGrGrB,EAFJ,CAAAC,cAAA,cAA6B,cACI,cACL;IACtBD,EAAA,CAAAE,SAAA,cAA+F;IAG3FF,EAFJ,CAAAC,cAAA,cAAgC,cACD,aACC;IAAAD,EAAA,CAAAG,MAAA,GAA6B;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAC9DJ,EAAA,CAAAC,cAAA,YAAkC;IAAAD,EAAA,CAAAG,MAAA,IAAoC;IAAAH,EAAA,CAAAI,YAAA,EAAI;IAExEJ,EADF,CAAAC,cAAA,eAA8B,gBACH;IAAAD,EAAA,CAAAG,MAAA,IAAwC;IAAAH,EAAA,CAAAI,YAAA,EAAO;IACxEJ,EAAA,CAAAC,cAAA,gBAA0B;IAAAD,EAAA,CAAAG,MAAA,IAA2G;IAG3IH,EAH2I,CAAAI,YAAA,EAAO,EACxI,EACF,EACF;IACNJ,EAAA,CAAAC,cAAA,eAA4B;IAC1BD,EAAA,CAAAE,SAAA,oBAAkC;IAClCF,EAAA,CAAAG,MAAA,wBACF;IACFH,EADE,CAAAI,YAAA,EAAM,EACF;IACNJ,EAAA,CAAAC,cAAA,eAA4B;IAC1BD,EAAA,CAAAsB,UAAA,KAAAC,yDAAA,kBAIC;IAITvB,EAHM,CAAAI,YAAA,EAAM,EACF,EACF,EACF;;;;IAzBOJ,EAAA,CAAAa,SAAA,GAAqC;IAACb,EAAtC,CAAAwB,UAAA,QAAAf,MAAA,CAAAY,kBAAA,CAAAI,UAAA,EAAAzB,EAAA,CAAA0B,aAAA,CAAqC,QAAAjB,MAAA,CAAAY,kBAAA,CAAAM,IAAA,CAAgC;IAG1C3B,EAAA,CAAAa,SAAA,GAA6B;IAA7Bb,EAAA,CAAAc,iBAAA,CAAAL,MAAA,CAAAY,kBAAA,CAAAM,IAAA,CAA6B;IACvB3B,EAAA,CAAAa,SAAA,GAAoC;IAApCb,EAAA,CAAAc,iBAAA,CAAAL,MAAA,CAAAY,kBAAA,CAAAO,WAAA,CAAoC;IAE3C5B,EAAA,CAAAa,SAAA,GAAwC;IAAxCb,EAAA,CAAA6B,kBAAA,KAAApB,MAAA,CAAAY,kBAAA,CAAAS,SAAA,WAAwC;IACvC9B,EAAA,CAAAa,SAAA,GAA2G;IAA3Gb,EAAA,CAAA+B,kBAAA,KAAAtB,MAAA,CAAAuB,WAAA,CAAAvB,MAAA,CAAAY,kBAAA,CAAAY,UAAA,CAAAC,GAAA,UAAAzB,MAAA,CAAAuB,WAAA,CAAAvB,MAAA,CAAAY,kBAAA,CAAAY,UAAA,CAAAE,GAAA,MAA2G;IAWvHnC,EAAA,CAAAa,SAAA,GAAiD;IAAjDb,EAAA,CAAAwB,UAAA,YAAAf,MAAA,CAAAY,kBAAA,CAAAe,aAAA,CAAAC,KAAA,OAAiD;;;;;;IAW3ErC,EAAA,CAAAC,cAAA,cAIC;IADCD,EAAA,CAAAK,UAAA,mBAAAiC,wEAAA;MAAA,MAAAC,aAAA,GAAAvC,EAAA,CAAAO,aAAA,CAAAiC,GAAA,EAAAC,SAAA;MAAA,MAAAhC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAW,iBAAA,CAAAmB,aAAA,CAA6B;IAAA,EAAC;IAEvCvC,EAAA,CAAAC,cAAA,cAAwC;IACtCD,EAAA,CAAAE,SAAA,cAAoF;IAGhFF,EAFJ,CAAAC,cAAA,cAAgC,cACE,aACF;IAAAD,EAAA,CAAAG,MAAA,GAAqB;IAAAH,EAAA,CAAAI,YAAA,EAAK;IACtDJ,EAAA,CAAAC,cAAA,YAAkC;IAAAD,EAAA,CAAAG,MAAA,GAA4B;IAAAH,EAAA,CAAAI,YAAA,EAAI;IAEhEJ,EADF,CAAAC,cAAA,cAA6B,gBACF;IAAAD,EAAA,CAAAG,MAAA,IAAgC;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAChEJ,EAAA,CAAAC,cAAA,gBAAsB;IAAAD,EAAA,CAAAG,MAAA,IAAgC;IAG5DH,EAH4D,CAAAI,YAAA,EAAO,EACzD,EACF,EACF;IACNJ,EAAA,CAAAC,cAAA,eAAiF;IAC/ED,EAAA,CAAAG,MAAA,IACF;IAAAH,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAC,cAAA,eAAkH;IAAzCD,EAAA,CAAAK,UAAA,mBAAAqC,yEAAAC,MAAA;MAAA,MAAAJ,aAAA,GAAAvC,EAAA,CAAAO,aAAA,CAAAiC,GAAA,EAAAC,SAAA;MAAA,MAAAhC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAmC,UAAA,CAAAL,aAAA,EAAAI,MAAA,CAA8B;IAAA,EAAC;IAC/G3C,EAAA,CAAAE,SAAA,oBAA4F;IAEhGF,EADE,CAAAI,YAAA,EAAM,EACF;IAGFJ,EAFJ,CAAAC,cAAA,eAA+B,eACL,gBACI;IAAAD,EAAA,CAAAG,MAAA,IAA2F;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAC5HJ,EAAA,CAAAC,cAAA,gBAAwB;IAAAD,EAAA,CAAAG,MAAA,IAA+C;IACzEH,EADyE,CAAAI,YAAA,EAAO,EAC1E;IAEJJ,EADF,CAAAC,cAAA,eAAgC,kBACsD;IAAhDD,EAAA,CAAAK,UAAA,mBAAAwC,4EAAAF,MAAA;MAAA,MAAAJ,aAAA,GAAAvC,EAAA,CAAAO,aAAA,CAAAiC,GAAA,EAAAC,SAAA;MAAA,MAAAhC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAW,iBAAA,CAAAmB,aAAA,EAAAI,MAAA,CAAqC;IAAA,EAAC;IACjF3C,EAAA,CAAAE,SAAA,oBAAwC;IACxCF,EAAA,CAAAG,MAAA,cACF;IAAAH,EAAA,CAAAI,YAAA,EAAS;IACTJ,EAAA,CAAAC,cAAA,kBAA+H;IAAzCD,EAAA,CAAAK,UAAA,mBAAAyC,4EAAAH,MAAA;MAAA,MAAAJ,aAAA,GAAAvC,EAAA,CAAAO,aAAA,CAAAiC,GAAA,EAAAC,SAAA;MAAA,MAAAhC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAsC,UAAA,CAAAR,aAAA,EAAAI,MAAA,CAA8B;IAAA,EAAC;IAC5H3C,EAAA,CAAAE,SAAA,oBAAkG;IAClGF,EAAA,CAAAG,MAAA,IACF;IAGNH,EAHM,CAAAI,YAAA,EAAS,EACL,EACF,EACF;;;;;IAlCGJ,EAAA,CAAAa,SAAA,GAA6B;IAACb,EAA9B,CAAAwB,UAAA,QAAAe,aAAA,CAAAd,UAAA,EAAAzB,EAAA,CAAA0B,aAAA,CAA6B,QAAAa,aAAA,CAAAZ,IAAA,CAAwB;IAG1B3B,EAAA,CAAAa,SAAA,GAAqB;IAArBb,EAAA,CAAAc,iBAAA,CAAAyB,aAAA,CAAAZ,IAAA,CAAqB;IACf3B,EAAA,CAAAa,SAAA,GAA4B;IAA5Bb,EAAA,CAAAc,iBAAA,CAAAyB,aAAA,CAAAX,WAAA,CAA4B;IAEnC5B,EAAA,CAAAa,SAAA,GAAgC;IAAhCb,EAAA,CAAA6B,kBAAA,KAAAU,aAAA,CAAAT,SAAA,WAAgC;IACnC9B,EAAA,CAAAa,SAAA,GAAgC;IAAhCb,EAAA,CAAA6B,kBAAA,QAAAU,aAAA,CAAAS,OAAA,CAAArB,IAAA,KAAgC;IAIhC3B,EAAA,CAAAa,SAAA,EAAoD;IAApDb,EAAA,CAAAgB,WAAA,qBAAAuB,aAAA,CAAAU,QAAA,CAAAC,KAAA,CAAoD;IAC9ElD,EAAA,CAAAa,SAAA,EACF;IADEb,EAAA,CAAA6B,kBAAA,MAAAU,aAAA,CAAAU,QAAA,CAAAtB,IAAA,MACF;IACsB3B,EAAA,CAAAa,SAAA,EAAkD;IAAlDb,EAAA,CAAAmD,WAAA,WAAA1C,MAAA,CAAA2C,iBAAA,CAAAb,aAAA,CAAAc,GAAA,EAAkD;IAC5DrD,EAAA,CAAAa,SAAA,EAAsE;IAAtEb,EAAA,CAAAwB,UAAA,SAAAf,MAAA,CAAA2C,iBAAA,CAAAb,aAAA,CAAAc,GAAA,8BAAsE;IAKtDrD,EAAA,CAAAa,SAAA,GAA2F;IAA3Fb,EAAA,CAAA+B,kBAAA,KAAAtB,MAAA,CAAAuB,WAAA,CAAAO,aAAA,CAAAN,UAAA,CAAAC,GAAA,UAAAzB,MAAA,CAAAuB,WAAA,CAAAO,aAAA,CAAAN,UAAA,CAAAE,GAAA,MAA2F;IAC7FnC,EAAA,CAAAa,SAAA,GAA+C;IAA/Cb,EAAA,CAAA6B,kBAAA,UAAApB,MAAA,CAAAuB,WAAA,CAAAO,aAAA,CAAAe,YAAA,MAA+C;IAOnCtD,EAAA,CAAAa,SAAA,GAAiD;IAAjDb,EAAA,CAAAmD,WAAA,UAAA1C,MAAA,CAAA8C,iBAAA,CAAAhB,aAAA,CAAAc,GAAA,EAAiD;IACzErD,EAAA,CAAAa,SAAA,EAA4E;IAA5Eb,EAAA,CAAAwB,UAAA,SAAAf,MAAA,CAAA8C,iBAAA,CAAAhB,aAAA,CAAAc,GAAA,oCAA4E;IACtFrD,EAAA,CAAAa,SAAA,EACF;IADEb,EAAA,CAAA6B,kBAAA,MAAApB,MAAA,CAAA8C,iBAAA,CAAAhB,aAAA,CAAAc,GAAA,0BACF;;;;;;IAUJrD,EAAA,CAAAC,cAAA,iBAKC;IADCD,EAAA,CAAAK,UAAA,mBAAAmD,oFAAA;MAAA,MAAAC,WAAA,GAAAzD,EAAA,CAAAO,aAAA,CAAAmD,GAAA,EAAAjB,SAAA;MAAA,MAAAhC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAkD,eAAA,CAAAF,WAAA,CAAyB;IAAA,EAAC;IAEnCzD,EAAA,CAAAC,cAAA,cAA2B;IACzBD,EAAA,CAAAE,SAAA,mBAA4C;IAC9CF,EAAA,CAAAI,YAAA,EAAM;IAEJJ,EADF,CAAAC,cAAA,cAA2B,aACC;IAAAD,EAAA,CAAAG,MAAA,GAAmB;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAClDJ,EAAA,CAAAC,cAAA,eAA6B;IAAAD,EAAA,CAAAG,MAAA,GAA0C;IAE3EH,EAF2E,CAAAI,YAAA,EAAO,EAC1E,EACC;;;;IAVPJ,EAAA,CAAAgB,WAAA,eAAAyC,WAAA,CAAAG,QAAA,CAAsC;IAI1B5D,EAAA,CAAAa,SAAA,GAAsB;IAAtBb,EAAA,CAAAwB,UAAA,SAAAiC,WAAA,CAAAI,IAAA,CAAsB;IAGN7D,EAAA,CAAAa,SAAA,GAAmB;IAAnBb,EAAA,CAAAc,iBAAA,CAAA2C,WAAA,CAAA9B,IAAA,CAAmB;IAChB3B,EAAA,CAAAa,SAAA,GAA0C;IAA1Cb,EAAA,CAAA6B,kBAAA,KAAA4B,WAAA,CAAAK,eAAA,iBAA0C;;;;;IAb7E9D,EADF,CAAAC,cAAA,cAA2E,aAC5C;IAAAD,EAAA,CAAAG,MAAA,yBAAkB;IAAAH,EAAA,CAAAI,YAAA,EAAK;IACpDJ,EAAA,CAAAC,cAAA,cAA6B;IAC3BD,EAAA,CAAAsB,UAAA,IAAAyC,2DAAA,qBAKC;IAUL/D,EADE,CAAAI,YAAA,EAAM,EACF;;;;IAdqBJ,EAAA,CAAAa,SAAA,GAAyB;IAAAb,EAAzB,CAAAwB,UAAA,YAAAf,MAAA,CAAAuD,oBAAA,CAAyB,iBAAAvD,MAAA,CAAAwD,mBAAA,CAA4B;;;;;IAmB5EjE,EAFJ,CAAAC,cAAA,cAAoD,cAC1B,cACC;IACrBD,EAAA,CAAAE,SAAA,kBAA2C;IAEzCF,EADF,CAAAC,cAAA,cAAuB,eACI;IAAAD,EAAA,CAAAG,MAAA,GAAsC;IAAAH,EAAA,CAAAI,YAAA,EAAO;IACtEJ,EAAA,CAAAC,cAAA,eAAyB;IAAAD,EAAA,CAAAG,MAAA,wBAAiB;IAE9CH,EAF8C,CAAAI,YAAA,EAAO,EAC7C,EACF;IACNJ,EAAA,CAAAC,cAAA,cAAuB;IACrBD,EAAA,CAAAE,SAAA,oBAAwC;IAEtCF,EADF,CAAAC,cAAA,eAAuB,gBACI;IAAAD,EAAA,CAAAG,MAAA,IAA6C;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAC7EJ,EAAA,CAAAC,cAAA,gBAAyB;IAAAD,EAAA,CAAAG,MAAA,mBAAW;IAExCH,EAFwC,CAAAI,YAAA,EAAO,EACvC,EACF;IACNJ,EAAA,CAAAC,cAAA,eAAuB;IACrBD,EAAA,CAAAE,SAAA,oBAA2C;IAEzCF,EADF,CAAAC,cAAA,eAAuB,gBACI;IAAAD,EAAA,CAAAG,MAAA,IAAgD;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAChFJ,EAAA,CAAAC,cAAA,gBAAyB;IAAAD,EAAA,CAAAG,MAAA,gBAAQ;IAErCH,EAFqC,CAAAI,YAAA,EAAO,EACpC,EACF;IACNJ,EAAA,CAAAC,cAAA,eAAuB;IACrBD,EAAA,CAAAE,SAAA,oBAA0C;IAExCF,EADF,CAAAC,cAAA,eAAuB,gBACI;IAAAD,EAAA,CAAAG,MAAA,IAA6C;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAC7EJ,EAAA,CAAAC,cAAA,gBAAyB;IAAAD,EAAA,CAAAG,MAAA,mBAAW;IAI5CH,EAJ4C,CAAAI,YAAA,EAAO,EACvC,EACF,EACF,EACF;;;;IA1B2BJ,EAAA,CAAAa,SAAA,GAAsC;IAAtCb,EAAA,CAAAc,iBAAA,CAAAL,MAAA,CAAAyD,eAAA,CAAAC,gBAAA,CAAsC;IAOtCnE,EAAA,CAAAa,SAAA,GAA6C;IAA7Cb,EAAA,CAAAc,iBAAA,CAAAL,MAAA,CAAA2D,WAAA,CAAA3D,MAAA,CAAAyD,eAAA,CAAAG,UAAA,EAA6C;IAO7CrE,EAAA,CAAAa,SAAA,GAAgD;IAAhDb,EAAA,CAAAc,iBAAA,CAAAL,MAAA,CAAA2D,WAAA,CAAA3D,MAAA,CAAAyD,eAAA,CAAAI,aAAA,EAAgD;IAOhDtE,EAAA,CAAAa,SAAA,GAA6C;IAA7Cb,EAAA,CAAAc,iBAAA,CAAAL,MAAA,CAAA2D,WAAA,CAAA3D,MAAA,CAAAyD,eAAA,CAAAK,UAAA,EAA6C;;;;;IA9HhFvE,EAAA,CAAAC,cAAA,cAAgG;IAE9FD,EAAA,CAAAsB,UAAA,IAAAkD,kDAAA,mBAA4G;IAgC5GxE,EAAA,CAAAC,cAAA,cAA+B;IAC7BD,EAAA,CAAAsB,UAAA,IAAAmD,kDAAA,oBAIC;IAqCHzE,EAAA,CAAAI,YAAA,EAAM;IAwBNJ,EArBA,CAAAsB,UAAA,IAAAoD,kDAAA,kBAA2E,IAAAC,kDAAA,mBAqBvB;IAgCtD3E,EAAA,CAAAI,YAAA,EAAM;;;;IAlI8BJ,EAAA,CAAAa,SAAA,EAAwB;IAAxBb,EAAA,CAAAwB,UAAA,SAAAf,MAAA,CAAAY,kBAAA,CAAwB;IAkC/BrB,EAAA,CAAAa,SAAA,GAAqB;IAAAb,EAArB,CAAAwB,UAAA,YAAAf,MAAA,CAAAmE,gBAAA,CAAqB,iBAAAnE,MAAA,CAAAoE,mBAAA,CAA4B;IA2CxC7E,EAAA,CAAAa,SAAA,EAAqC;IAArCb,EAAA,CAAAwB,UAAA,SAAAf,MAAA,CAAAuD,oBAAA,CAAAc,MAAA,KAAqC;IAqB5C9E,EAAA,CAAAa,SAAA,EAAqB;IAArBb,EAAA,CAAAwB,UAAA,SAAAf,MAAA,CAAAyD,eAAA,CAAqB;;;;;IAmCpDlE,EAAA,CAAAC,cAAA,cAA8F;IAC5FD,EAAA,CAAAE,SAAA,kBAA2C;IAC3CF,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAG,MAAA,wCAAiC;IACtCH,EADsC,CAAAI,YAAA,EAAI,EACpC;;;ADnGV,OAAM,MAAO2E,4BAA4B;EAYvCC,YAAoBC,MAAc;IAAd,KAAAA,MAAM,GAANA,MAAM;IAX1B,KAAAC,mBAAmB,GAAyB,EAAE;IAC9C,KAAA7D,kBAAkB,GAA8B,IAAI;IACpD,KAAAuD,gBAAgB,GAAyB,EAAE;IAC3C,KAAAZ,oBAAoB,GAAyB,EAAE;IAC/C,KAAAE,eAAe,GAA2B,IAAI;IAC9C,KAAAiB,gBAAgB,GAAG,IAAIC,GAAG,EAAU;IACpC,KAAAC,gBAAgB,GAAG,IAAID,GAAG,EAAU;IACpC,KAAAE,SAAS,GAAG,IAAI;IAChB,KAAAvE,KAAK,GAAkB,IAAI;IACnB,KAAAwE,YAAY,GAAiB,IAAIxF,YAAY,EAAE;EAElB;EAErCyF,QAAQA,CAAA;IACN,IAAI,CAACC,uBAAuB,EAAE;EAChC;EAEAC,WAAWA,CAAA;IACT,IAAI,CAACH,YAAY,CAACI,WAAW,EAAE;EACjC;EAEcF,uBAAuBA,CAAA;IAAA,IAAAG,KAAA;IAAA,OAAAC,iBAAA;MACnC,IAAI;QACFD,KAAI,CAACN,SAAS,GAAG,IAAI;QACrBM,KAAI,CAAC7E,KAAK,GAAG,IAAI;QAEjB;QACA,MAAM6E,KAAI,CAACE,mBAAmB,EAAE;OAEjC,CAAC,OAAO/E,KAAK,EAAE;QACdgF,OAAO,CAAChF,KAAK,CAAC,qCAAqC,EAAEA,KAAK,CAAC;QAC3D6E,KAAI,CAAC7E,KAAK,GAAG,qCAAqC;QAClD6E,KAAI,CAACN,SAAS,GAAG,KAAK;;IACvB;EACH;EAEcQ,mBAAmBA,CAAA;IAAA,IAAAE,MAAA;IAAA,OAAAH,iBAAA;MAC/B;MACA,MAAM,IAAII,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,IAAI,CAAC,CAAC;MAEvD;MACAF,MAAI,CAACd,mBAAmB,GAAG,CACzB;QACE7B,GAAG,EAAE,MAAM;QACX1B,IAAI,EAAE,wBAAwB;QAC9BC,WAAW,EAAE,kDAAkD;QAC/DH,UAAU,EAAE,mFAAmF;QAC/FW,aAAa,EAAE,CACb,mFAAmF,EACnF,mFAAmF,EACnF,mFAAmF,CACpF;QACDN,SAAS,EAAE,EAAE;QACbG,UAAU,EAAE;UAAEC,GAAG,EAAE,IAAI;UAAEC,GAAG,EAAE;QAAI,CAAE;QACpCmB,YAAY,EAAE,IAAI;QAClBN,OAAO,EAAE;UACPK,GAAG,EAAE,MAAM;UACX1B,IAAI,EAAE,gBAAgB;UACtByE,MAAM,EAAE;SACT;QACDnD,QAAQ,EAAE;UAAEtB,IAAI,EAAE,QAAQ;UAAEuB,KAAK,EAAE;QAAS,CAAE;QAC9CmD,IAAI,EAAE,CAAC,QAAQ,EAAE,YAAY,EAAE,UAAU,CAAC;QAC1CC,SAAS,EAAE;UAAEC,KAAK,EAAE,KAAK;UAAEC,KAAK,EAAE,IAAI;UAAEC,KAAK,EAAE,GAAG;UAAEC,MAAM,EAAE;QAAG,CAAE;QACjEC,QAAQ,EAAE,IAAI;QACdC,UAAU,EAAE,IAAI;QAChBC,SAAS,EAAE,IAAIC,IAAI;OACpB,EACD;QACEzD,GAAG,EAAE,MAAM;QACX1B,IAAI,EAAE,iBAAiB;QACvBC,WAAW,EAAE,mCAAmC;QAChDH,UAAU,EAAE,mFAAmF;QAC/FW,aAAa,EAAE,CACb,mFAAmF,EACnF,mFAAmF,CACpF;QACDN,SAAS,EAAE,EAAE;QACbG,UAAU,EAAE;UAAEC,GAAG,EAAE,IAAI;UAAEC,GAAG,EAAE;QAAK,CAAE;QACrCmB,YAAY,EAAE,IAAI;QAClBN,OAAO,EAAE;UACPK,GAAG,EAAE,MAAM;UACX1B,IAAI,EAAE,aAAa;UACnByE,MAAM,EAAE;SACT;QACDnD,QAAQ,EAAE;UAAEtB,IAAI,EAAE,YAAY;UAAEuB,KAAK,EAAE;QAAS,CAAE;QAClDmD,IAAI,EAAE,CAAC,YAAY,EAAE,MAAM,EAAE,UAAU,CAAC;QACxCC,SAAS,EAAE;UAAEC,KAAK,EAAE,KAAK;UAAEC,KAAK,EAAE,IAAI;UAAEC,KAAK,EAAE,GAAG;UAAEC,MAAM,EAAE;QAAE,CAAE;QAChEC,QAAQ,EAAE,IAAI;QACdC,UAAU,EAAE,KAAK;QACjBC,SAAS,EAAE,IAAIC,IAAI;OACpB,EACD;QACEzD,GAAG,EAAE,MAAM;QACX1B,IAAI,EAAE,oBAAoB;QAC1BC,WAAW,EAAE,sCAAsC;QACnDH,UAAU,EAAE,mFAAmF;QAC/FW,aAAa,EAAE,CACb,gFAAgF,EAChF,gFAAgF,CACjF;QACDN,SAAS,EAAE,EAAE;QACbG,UAAU,EAAE;UAAEC,GAAG,EAAE,IAAI;UAAEC,GAAG,EAAE;QAAI,CAAE;QACpCmB,YAAY,EAAE,IAAI;QAClBN,OAAO,EAAE;UACPK,GAAG,EAAE,MAAM;UACX1B,IAAI,EAAE,kBAAkB;UACxByE,MAAM,EAAE;SACT;QACDnD,QAAQ,EAAE;UAAEtB,IAAI,EAAE,cAAc;UAAEuB,KAAK,EAAE;QAAS,CAAE;QACpDmD,IAAI,EAAE,CAAC,QAAQ,EAAE,OAAO,EAAE,MAAM,CAAC;QACjCC,SAAS,EAAE;UAAEC,KAAK,EAAE,KAAK;UAAEC,KAAK,EAAE,IAAI;UAAEC,KAAK,EAAE,IAAI;UAAEC,MAAM,EAAE;QAAG,CAAE;QAClEC,QAAQ,EAAE,IAAI;QACdC,UAAU,EAAE,KAAK;QACjBC,SAAS,EAAE,IAAIC,IAAI;OACpB,EACD;QACEzD,GAAG,EAAE,MAAM;QACX1B,IAAI,EAAE,iBAAiB;QACvBC,WAAW,EAAE,oCAAoC;QACjDH,UAAU,EAAE,mFAAmF;QAC/FW,aAAa,EAAE,CACb,mFAAmF,CACpF;QACDN,SAAS,EAAE,EAAE;QACbG,UAAU,EAAE;UAAEC,GAAG,EAAE,IAAI;UAAEC,GAAG,EAAE;QAAI,CAAE;QACpCmB,YAAY,EAAE,IAAI;QAClBN,OAAO,EAAE;UACPK,GAAG,EAAE,MAAM;UACX1B,IAAI,EAAE,gBAAgB;UACtByE,MAAM,EAAE;SACT;QACDnD,QAAQ,EAAE;UAAEtB,IAAI,EAAE,SAAS;UAAEuB,KAAK,EAAE;QAAS,CAAE;QAC/CmD,IAAI,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,OAAO,CAAC;QACrCC,SAAS,EAAE;UAAEC,KAAK,EAAE,IAAI;UAAEC,KAAK,EAAE,IAAI;UAAEC,KAAK,EAAE,GAAG;UAAEC,MAAM,EAAE;QAAG,CAAE;QAChEC,QAAQ,EAAE,IAAI;QACdC,UAAU,EAAE,KAAK;QACjBC,SAAS,EAAE,IAAIC,IAAI;OACpB,CACF;MAED;MACAd,MAAI,CAAC3E,kBAAkB,GAAG2E,MAAI,CAACd,mBAAmB,CAAC6B,MAAM,CAAC,CAACC,IAAI,EAAEC,OAAO,KACrED,IAAI,CAACV,SAAS,CAACE,KAAK,GAAGS,OAAO,CAACX,SAAS,CAACE,KAAK,GAAIQ,IAAI,GAAGC,OAAO,CAClE;MAED;MACAjB,MAAI,CAACpB,gBAAgB,GAAGoB,MAAI,CAACd,mBAAmB,CAACgC,MAAM,CAACC,GAAG,IAAIA,GAAG,CAAC9D,GAAG,KAAK2C,MAAI,CAAC3E,kBAAkB,EAAEgC,GAAG,CAAC;MAExG;MACA2C,MAAI,CAAChC,oBAAoB,GAAG,CAC1B;QACErC,IAAI,EAAE,QAAQ;QACdkC,IAAI,EAAE,eAAe;QACrBD,QAAQ,EAAE,2CAA2C;QACrDE,eAAe,EAAE;OAClB,EACD;QACEnC,IAAI,EAAE,YAAY;QAClBkC,IAAI,EAAE,gBAAgB;QACtBD,QAAQ,EAAE,2CAA2C;QACrDE,eAAe,EAAE;OAClB,EACD;QACEnC,IAAI,EAAE,cAAc;QACpBkC,IAAI,EAAE,aAAa;QACnBD,QAAQ,EAAE,2CAA2C;QACrDE,eAAe,EAAE;OAClB,EACD;QACEnC,IAAI,EAAE,SAAS;QACfkC,IAAI,EAAE,cAAc;QACpBD,QAAQ,EAAE,2CAA2C;QACrDE,eAAe,EAAE;OAClB,EACD;QACEnC,IAAI,EAAE,QAAQ;QACdkC,IAAI,EAAE,kBAAkB;QACxBD,QAAQ,EAAE,2CAA2C;QACrDE,eAAe,EAAE;OAClB,EACD;QACEnC,IAAI,EAAE,QAAQ;QACdkC,IAAI,EAAE,cAAc;QACpBD,QAAQ,EAAE,2CAA2C;QACrDE,eAAe,EAAE;OAClB,CACF;MAED;MACAkC,MAAI,CAAC9B,eAAe,GAAG;QACrBC,gBAAgB,EAAE6B,MAAI,CAACd,mBAAmB,CAACJ,MAAM;QACjDT,UAAU,EAAE2B,MAAI,CAACd,mBAAmB,CAAC6B,MAAM,CAAC,CAACK,GAAG,EAAED,GAAG,KAAKC,GAAG,GAAGD,GAAG,CAACrF,SAAS,EAAE,CAAC,CAAC;QACjFwC,aAAa,EAAE,IAAIc,GAAG,CAACY,MAAI,CAACd,mBAAmB,CAACmC,GAAG,CAACF,GAAG,IAAIA,GAAG,CAACnE,OAAO,CAACK,GAAG,CAAC,CAAC,CAACiE,IAAI;QACjF/C,UAAU,EAAEyB,MAAI,CAACd,mBAAmB,CAAC6B,MAAM,CAAC,CAACK,GAAG,EAAED,GAAG,KAAKC,GAAG,GAAGD,GAAG,CAACb,SAAS,CAACE,KAAK,EAAE,CAAC;OACvF;MAEDR,MAAI,CAACV,SAAS,GAAG,KAAK;IAAC;EACzB;EAEAlE,iBAAiBA,CAACmG,UAA8B,EAAEC,KAAa;IAC7D,IAAIA,KAAK,EAAE;MACTA,KAAK,CAACC,eAAe,EAAE;;IAEzB,IAAI,CAACxC,MAAM,CAACyC,QAAQ,CAAC,CAAC,aAAa,EAAEH,UAAU,CAAClE,GAAG,CAAC,CAAC;EACvD;EAEAM,eAAeA,CAACV,QAA4B;IAC1C,IAAI,CAACgC,MAAM,CAACyC,QAAQ,CAAC,CAAC,cAAc,CAAC,EAAE;MACrCC,WAAW,EAAE;QAAE1E,QAAQ,EAAEA,QAAQ,CAACtB,IAAI,CAACiG,WAAW;MAAE;KACrD,CAAC;EACJ;EAEAhF,UAAUA,CAAC2E,UAA8B,EAAEC,KAAY;IACrDA,KAAK,CAACC,eAAe,EAAE;IAEvB,IAAI,IAAI,CAACtC,gBAAgB,CAAC0C,GAAG,CAACN,UAAU,CAAClE,GAAG,CAAC,EAAE;MAC7C,IAAI,CAAC8B,gBAAgB,CAAC2C,MAAM,CAACP,UAAU,CAAClE,GAAG,CAAC;MAC5CkE,UAAU,CAACjB,SAAS,CAACE,KAAK,EAAE;KAC7B,MAAM;MACL,IAAI,CAACrB,gBAAgB,CAAC4C,GAAG,CAACR,UAAU,CAAClE,GAAG,CAAC;MACzCkE,UAAU,CAACjB,SAAS,CAACE,KAAK,EAAE;;EAEhC;EAEAzD,UAAUA,CAACwE,UAA8B,EAAEC,KAAY;IACrDA,KAAK,CAACC,eAAe,EAAE;IAEvB,IAAI,IAAI,CAACpC,gBAAgB,CAACwC,GAAG,CAACN,UAAU,CAAClE,GAAG,CAAC,EAAE;MAC7C,IAAI,CAACgC,gBAAgB,CAACyC,MAAM,CAACP,UAAU,CAAClE,GAAG,CAAC;MAC5CkE,UAAU,CAACjB,SAAS,CAACG,KAAK,EAAE;KAC7B,MAAM;MACL,IAAI,CAACpB,gBAAgB,CAAC0C,GAAG,CAACR,UAAU,CAAClE,GAAG,CAAC;MACzCkE,UAAU,CAACjB,SAAS,CAACG,KAAK,EAAE;;EAEhC;EAEArD,iBAAiBA,CAAC4E,YAAoB;IACpC,OAAO,IAAI,CAAC7C,gBAAgB,CAAC0C,GAAG,CAACG,YAAY,CAAC;EAChD;EAEAzE,iBAAiBA,CAACyE,YAAoB;IACpC,OAAO,IAAI,CAAC3C,gBAAgB,CAACwC,GAAG,CAACG,YAAY,CAAC;EAChD;EAEAhG,WAAWA,CAACiG,KAAa;IACvB,OAAO,IAAIC,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;MACpCC,KAAK,EAAE,UAAU;MACjBC,QAAQ,EAAE,KAAK;MACfC,qBAAqB,EAAE;KACxB,CAAC,CAACC,MAAM,CAACN,KAAK,CAAC;EAClB;EAEA7D,WAAWA,CAACoE,KAAa;IACvB,IAAIA,KAAK,IAAI,OAAO,EAAE;MACpB,OAAO,CAACA,KAAK,GAAG,OAAO,EAAEC,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG;KAC1C,MAAM,IAAID,KAAK,IAAI,IAAI,EAAE;MACxB,OAAO,CAACA,KAAK,GAAG,IAAI,EAAEC,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG;;IAExC,OAAOD,KAAK,CAACE,QAAQ,EAAE;EACzB;EAEA7D,mBAAmBA,CAAC8D,KAAa,EAAEpB,UAA8B;IAC/D,OAAOA,UAAU,CAAClE,GAAG;EACvB;EAEAY,mBAAmBA,CAAC0E,KAAa,EAAE1F,QAA4B;IAC7D,OAAOA,QAAQ,CAACtB,IAAI;EACtB;EAEAiH,QAAQA,CAACpB,KAAY;IACnBA,KAAK,CAACqB,cAAc,EAAE;IACtB,IAAI,CAAC5D,MAAM,CAACyC,QAAQ,CAAC,CAAC,uBAAuB,CAAC,CAAC;EACjD;EAEA9G,OAAOA,CAAA;IACL,IAAI,CAAC6E,uBAAuB,EAAE;EAChC;;;uBArRWV,4BAA4B,EAAA/E,EAAA,CAAA8I,iBAAA,CAAAC,EAAA,CAAAC,MAAA;IAAA;EAAA;;;YAA5BjE,4BAA4B;MAAAkE,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAAnJ,EAAA,CAAAoJ,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,sCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UC9DrC1J,EAHJ,CAAAC,cAAA,aAAiC,aAED,SACxB;UACFD,EAAA,CAAAE,SAAA,kBAA2C;UAC3CF,EAAA,CAAAG,MAAA,6BACF;UAAAH,EAAA,CAAAI,YAAA,EAAK;UACLJ,EAAA,CAAAC,cAAA,WAAuD;UAA3BD,EAAA,CAAAK,UAAA,mBAAAuJ,yDAAAjH,MAAA;YAAA,OAASgH,GAAA,CAAAf,QAAA,CAAAjG,MAAA,CAAgB;UAAA,EAAC;UAAC3C,EAAA,CAAAG,MAAA,cAAO;UAChEH,EADgE,CAAAI,YAAA,EAAI,EAC9D;UAGNJ,EAAA,CAAAC,cAAA,aAA+B;UAsJ7BD,EApJA,CAAAsB,UAAA,IAAAuI,2CAAA,iBAAiD,IAAAC,2CAAA,iBAMQ,KAAAC,4CAAA,iBAOuC,KAAAC,4CAAA,iBAuIF;UAKlGhK,EADE,CAAAI,YAAA,EAAM,EACF;;;UAzJIJ,EAAA,CAAAa,SAAA,GAAe;UAAfb,EAAA,CAAAwB,UAAA,SAAAmI,GAAA,CAAArE,SAAA,CAAe;UAMftF,EAAA,CAAAa,SAAA,EAAyB;UAAzBb,EAAA,CAAAwB,UAAA,SAAAmI,GAAA,CAAA5I,KAAA,KAAA4I,GAAA,CAAArE,SAAA,CAAyB;UAOzBtF,EAAA,CAAAa,SAAA,EAA4D;UAA5Db,EAAA,CAAAwB,UAAA,UAAAmI,GAAA,CAAArE,SAAA,KAAAqE,GAAA,CAAA5I,KAAA,IAAA4I,GAAA,CAAAzE,mBAAA,CAAAJ,MAAA,KAA4D;UAuI5D9E,EAAA,CAAAa,SAAA,EAA8D;UAA9Db,EAAA,CAAAwB,UAAA,UAAAmI,GAAA,CAAArE,SAAA,KAAAqE,GAAA,CAAA5I,KAAA,IAAA4I,GAAA,CAAAzE,mBAAA,CAAAJ,MAAA,OAA8D;;;qBDvGpElF,YAAY,EAAAqK,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EACZtK,YAAY,EACZC,WAAW,EAAAsK,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,UAAA;MAAAC,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}