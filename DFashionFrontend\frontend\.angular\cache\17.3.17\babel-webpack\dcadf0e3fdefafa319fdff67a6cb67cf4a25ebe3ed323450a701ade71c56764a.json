{"ast": null, "code": "import _asyncToGenerator from \"E:/Fashion/DFashionFrontend/frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\nimport { IonicModule } from '@ionic/angular';\nimport { Subscription } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"@ionic/angular\";\nfunction TrendingReelsComponent_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 8);\n    i0.ɵɵelement(1, \"ion-spinner\", 9);\n    i0.ɵɵelementStart(2, \"p\");\n    i0.ɵɵtext(3, \"Loading trending reels...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction TrendingReelsComponent_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 10);\n    i0.ɵɵelement(1, \"ion-icon\", 11);\n    i0.ɵɵelementStart(2, \"p\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"button\", 12);\n    i0.ɵɵlistener(\"click\", function TrendingReelsComponent_div_9_Template_button_click_4_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onRetry());\n    });\n    i0.ɵɵtext(5, \"Try Again\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r1.error);\n  }\n}\nfunction TrendingReelsComponent_div_10_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 16);\n    i0.ɵɵlistener(\"click\", function TrendingReelsComponent_div_10_div_2_Template_div_click_0_listener() {\n      const reel_r4 = i0.ɵɵrestoreView(_r3).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onReelClick(reel_r4));\n    });\n    i0.ɵɵelementStart(1, \"div\", 17);\n    i0.ɵɵelement(2, \"img\", 18);\n    i0.ɵɵelementStart(3, \"div\", 19);\n    i0.ɵɵelement(4, \"ion-icon\", 20);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 21);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\", 22);\n    i0.ɵɵelement(8, \"ion-icon\", 23);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"div\", 24)(11, \"div\", 25);\n    i0.ɵɵelement(12, \"img\", 26);\n    i0.ɵɵelementStart(13, \"div\", 27)(14, \"span\", 28);\n    i0.ɵɵtext(15);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"span\", 29);\n    i0.ɵɵtext(17);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(18, \"h5\", 30);\n    i0.ɵɵtext(19);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"div\", 31)(21, \"div\", 32);\n    i0.ɵɵelement(22, \"ion-icon\", 33);\n    i0.ɵɵelementStart(23, \"span\");\n    i0.ɵɵtext(24);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(25, \"div\", 32);\n    i0.ɵɵelement(26, \"ion-icon\", 34);\n    i0.ɵɵelementStart(27, \"span\");\n    i0.ɵɵtext(28);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(29, \"div\", 32);\n    i0.ɵɵelement(30, \"ion-icon\", 35);\n    i0.ɵɵelementStart(31, \"span\");\n    i0.ɵɵtext(32);\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const reel_r4 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"src\", reel_r4.thumbnail, i0.ɵɵsanitizeUrl)(\"alt\", reel_r4.title);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r1.formatDuration(reel_r4.duration));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.formatCount(reel_r4.views), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"src\", reel_r4.creator.avatar, i0.ɵɵsanitizeUrl)(\"alt\", reel_r4.creator.name);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(reel_r4.creator.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(\"@\" + reel_r4.creator.username);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(reel_r4.title);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r1.formatCount(reel_r4.likes));\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r1.formatCount(reel_r4.comments));\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r1.formatCount(reel_r4.shares));\n  }\n}\nfunction TrendingReelsComponent_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 13)(1, \"div\", 14);\n    i0.ɵɵtemplate(2, TrendingReelsComponent_div_10_div_2_Template, 33, 12, \"div\", 15);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.trendingReels)(\"ngForTrackBy\", ctx_r1.trackByReelId);\n  }\n}\nexport class TrendingReelsComponent {\n  constructor(router) {\n    this.router = router;\n    this.trendingReels = [];\n    this.isLoading = true;\n    this.error = null;\n    this.subscription = new Subscription();\n  }\n  ngOnInit() {\n    this.loadTrendingReels();\n  }\n  ngOnDestroy() {\n    this.subscription.unsubscribe();\n  }\n  loadTrendingReels() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      try {\n        _this.isLoading = true;\n        _this.error = null;\n        yield _this.loadMockReels();\n      } catch (error) {\n        console.error('Error loading trending reels:', error);\n        _this.error = 'Failed to load trending reels';\n        _this.isLoading = false;\n      }\n    })();\n  }\n  loadMockReels() {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      yield new Promise(resolve => setTimeout(resolve, 700));\n      _this2.trendingReels = [{\n        _id: 'reel1',\n        title: 'Summer Outfit Transformation',\n        thumbnail: 'https://images.unsplash.com/photo-1515372039744-b8f02a3ae446?w=300&h=400&fit=crop',\n        duration: 45,\n        views: 125000,\n        likes: 8900,\n        comments: 456,\n        shares: 234,\n        creator: {\n          _id: 'creator1',\n          name: 'Maya Sharma',\n          username: 'fashionista_maya',\n          avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b47c?w=50&h=50&fit=crop&crop=face'\n        },\n        createdAt: new Date()\n      }, {\n        _id: 'reel2',\n        title: 'Street Style Inspiration',\n        thumbnail: 'https://images.unsplash.com/photo-1441986300917-64674bd600d8?w=300&h=400&fit=crop',\n        duration: 32,\n        views: 89000,\n        likes: 6700,\n        comments: 289,\n        shares: 156,\n        creator: {\n          _id: 'creator2',\n          name: 'Raj Patel',\n          username: 'style_guru_raj',\n          avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=50&h=50&fit=crop&crop=face'\n        },\n        createdAt: new Date()\n      }, {\n        _id: 'reel3',\n        title: 'Minimalist Wardrobe Essentials',\n        thumbnail: 'https://images.unsplash.com/photo-1445205170230-053b83016050?w=300&h=400&fit=crop',\n        duration: 58,\n        views: 67000,\n        likes: 4500,\n        comments: 178,\n        shares: 89,\n        creator: {\n          _id: 'creator3',\n          name: 'Priya Singh',\n          username: 'trendy_priya',\n          avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=50&h=50&fit=crop&crop=face'\n        },\n        createdAt: new Date()\n      }, {\n        _id: 'reel4',\n        title: 'Vintage Fashion Haul',\n        thumbnail: 'https://images.unsplash.com/photo-1434389677669-e08b4cac3105?w=300&h=400&fit=crop',\n        duration: 72,\n        views: 54000,\n        likes: 3200,\n        comments: 145,\n        shares: 67,\n        creator: {\n          _id: 'creator4',\n          name: 'Arjun Mehta',\n          username: 'luxury_lifestyle_arjun',\n          avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=50&h=50&fit=crop&crop=face'\n        },\n        createdAt: new Date()\n      }, {\n        _id: 'reel5',\n        title: 'Quick Styling Tips',\n        thumbnail: 'https://images.unsplash.com/photo-1469334031218-e382a71b716b?w=300&h=400&fit=crop',\n        duration: 28,\n        views: 98000,\n        likes: 7800,\n        comments: 345,\n        shares: 189,\n        creator: {\n          _id: 'creator1',\n          name: 'Maya Sharma',\n          username: 'fashionista_maya',\n          avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b47c?w=50&h=50&fit=crop&crop=face'\n        },\n        createdAt: new Date()\n      }, {\n        _id: 'reel6',\n        title: 'Accessory Styling Guide',\n        thumbnail: 'https://images.unsplash.com/photo-1553062407-98eeb64c6a62?w=300&h=400&fit=crop',\n        duration: 41,\n        views: 76000,\n        likes: 5600,\n        comments: 234,\n        shares: 123,\n        creator: {\n          _id: 'creator3',\n          name: 'Priya Singh',\n          username: 'trendy_priya',\n          avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=50&h=50&fit=crop&crop=face'\n        },\n        createdAt: new Date()\n      }];\n      _this2.isLoading = false;\n    })();\n  }\n  onReelClick(reel) {\n    this.router.navigate(['/reel', reel._id]);\n  }\n  formatDuration(seconds) {\n    const minutes = Math.floor(seconds / 60);\n    const remainingSeconds = seconds % 60;\n    return minutes > 0 ? `${minutes}:${remainingSeconds.toString().padStart(2, '0')}` : `0:${remainingSeconds.toString().padStart(2, '0')}`;\n  }\n  formatCount(count) {\n    if (count >= 1000000) {\n      return (count / 1000000).toFixed(1) + 'M';\n    } else if (count >= 1000) {\n      return (count / 1000).toFixed(1) + 'K';\n    }\n    return count.toString();\n  }\n  trackByReelId(index, reel) {\n    return reel._id;\n  }\n  onSeeAll(event) {\n    event.preventDefault();\n    this.router.navigate(['/trending/reels']);\n  }\n  onRetry() {\n    this.loadTrendingReels();\n  }\n  static {\n    this.ɵfac = function TrendingReelsComponent_Factory(t) {\n      return new (t || TrendingReelsComponent)(i0.ɵɵdirectiveInject(i1.Router));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: TrendingReelsComponent,\n      selectors: [[\"app-trending-reels\"]],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 11,\n      vars: 3,\n      consts: [[1, \"component-container\"], [1, \"component-header\"], [\"name\", \"play-circle-outline\"], [\"href\", \"#\", 1, \"see-all\", 3, \"click\"], [1, \"component-content\"], [\"class\", \"loading-container\", 4, \"ngIf\"], [\"class\", \"error-container\", 4, \"ngIf\"], [\"class\", \"reels-content\", 4, \"ngIf\"], [1, \"loading-container\"], [\"name\", \"crescent\"], [1, \"error-container\"], [\"name\", \"alert-circle-outline\"], [1, \"retry-btn\", 3, \"click\"], [1, \"reels-content\"], [1, \"reels-grid\"], [\"class\", \"reel-item\", 3, \"click\", 4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [1, \"reel-item\", 3, \"click\"], [1, \"reel-thumbnail\"], [1, \"thumbnail-image\", 3, \"src\", \"alt\"], [1, \"play-overlay\"], [\"name\", \"play\", 1, \"play-icon\"], [1, \"reel-duration\"], [1, \"reel-views\"], [\"name\", \"eye\"], [1, \"reel-info\"], [1, \"creator-info\"], [1, \"creator-avatar\", 3, \"src\", \"alt\"], [1, \"creator-details\"], [1, \"creator-name\"], [1, \"creator-username\"], [1, \"reel-title\"], [1, \"reel-stats\"], [1, \"stat-item\"], [\"name\", \"heart\"], [\"name\", \"chatbubble\"], [\"name\", \"share\"]],\n      template: function TrendingReelsComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"h3\");\n          i0.ɵɵelement(3, \"ion-icon\", 2);\n          i0.ɵɵtext(4, \" Trending Reels \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(5, \"a\", 3);\n          i0.ɵɵlistener(\"click\", function TrendingReelsComponent_Template_a_click_5_listener($event) {\n            return ctx.onSeeAll($event);\n          });\n          i0.ɵɵtext(6, \"See All\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(7, \"div\", 4);\n          i0.ɵɵtemplate(8, TrendingReelsComponent_div_8_Template, 4, 0, \"div\", 5)(9, TrendingReelsComponent_div_9_Template, 6, 1, \"div\", 6)(10, TrendingReelsComponent_div_10_Template, 3, 2, \"div\", 7);\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.error && !ctx.isLoading);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading && !ctx.error);\n        }\n      },\n      dependencies: [CommonModule, i2.NgForOf, i2.NgIf, RouterModule, IonicModule, i3.IonIcon, i3.IonSpinner],\n      styles: [\".loading-container[_ngcontent-%COMP%], .error-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n  padding: 40px 20px;\\n  text-align: center;\\n}\\n.loading-container[_ngcontent-%COMP%]   ion-spinner[_ngcontent-%COMP%], .loading-container[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%], .error-container[_ngcontent-%COMP%]   ion-spinner[_ngcontent-%COMP%], .error-container[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n  margin-bottom: 16px;\\n  font-size: 48px;\\n}\\n.loading-container[_ngcontent-%COMP%]   p[_ngcontent-%COMP%], .error-container[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0 0 16px 0;\\n  font-size: 14px;\\n}\\n.loading-container[_ngcontent-%COMP%]   .retry-btn[_ngcontent-%COMP%], .error-container[_ngcontent-%COMP%]   .retry-btn[_ngcontent-%COMP%] {\\n  background: #e74c3c;\\n  color: white;\\n  border: none;\\n  padding: 8px 16px;\\n  border-radius: 6px;\\n  font-size: 12px;\\n  cursor: pointer;\\n  transition: background 0.3s ease;\\n}\\n.loading-container[_ngcontent-%COMP%]   .retry-btn[_ngcontent-%COMP%]:hover, .error-container[_ngcontent-%COMP%]   .retry-btn[_ngcontent-%COMP%]:hover {\\n  background: #c0392b;\\n}\\n\\n.error-container[_ngcontent-%COMP%] {\\n  color: #e74c3c;\\n}\\n.error-container[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n  color: #e74c3c;\\n}\\n\\n.reels-content[_ngcontent-%COMP%]   .reels-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\\n  gap: 16px;\\n}\\n.reels-content[_ngcontent-%COMP%]   .reels-grid[_ngcontent-%COMP%]   .reel-item[_ngcontent-%COMP%] {\\n  background: white;\\n  border-radius: 12px;\\n  overflow: hidden;\\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n}\\n.reels-content[_ngcontent-%COMP%]   .reels-grid[_ngcontent-%COMP%]   .reel-item[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-4px);\\n  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);\\n}\\n.reels-content[_ngcontent-%COMP%]   .reels-grid[_ngcontent-%COMP%]   .reel-item[_ngcontent-%COMP%]   .reel-thumbnail[_ngcontent-%COMP%] {\\n  position: relative;\\n  width: 100%;\\n  height: 250px;\\n  overflow: hidden;\\n}\\n.reels-content[_ngcontent-%COMP%]   .reels-grid[_ngcontent-%COMP%]   .reel-item[_ngcontent-%COMP%]   .reel-thumbnail[_ngcontent-%COMP%]   .thumbnail-image[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 100%;\\n  object-fit: cover;\\n  transition: transform 0.3s ease;\\n}\\n.reels-content[_ngcontent-%COMP%]   .reels-grid[_ngcontent-%COMP%]   .reel-item[_ngcontent-%COMP%]   .reel-thumbnail[_ngcontent-%COMP%]:hover   .thumbnail-image[_ngcontent-%COMP%] {\\n  transform: scale(1.05);\\n}\\n.reels-content[_ngcontent-%COMP%]   .reels-grid[_ngcontent-%COMP%]   .reel-item[_ngcontent-%COMP%]   .reel-thumbnail[_ngcontent-%COMP%]   .play-overlay[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 50%;\\n  left: 50%;\\n  transform: translate(-50%, -50%);\\n  background: rgba(0, 0, 0, 0.7);\\n  border-radius: 50%;\\n  width: 60px;\\n  height: 60px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  opacity: 0;\\n  transition: all 0.3s ease;\\n}\\n.reels-content[_ngcontent-%COMP%]   .reels-grid[_ngcontent-%COMP%]   .reel-item[_ngcontent-%COMP%]   .reel-thumbnail[_ngcontent-%COMP%]   .play-overlay[_ngcontent-%COMP%]   .play-icon[_ngcontent-%COMP%] {\\n  font-size: 24px;\\n  color: white;\\n  margin-left: 3px;\\n}\\n.reels-content[_ngcontent-%COMP%]   .reels-grid[_ngcontent-%COMP%]   .reel-item[_ngcontent-%COMP%]   .reel-thumbnail[_ngcontent-%COMP%]:hover   .play-overlay[_ngcontent-%COMP%] {\\n  opacity: 1;\\n  transform: translate(-50%, -50%) scale(1.1);\\n}\\n.reels-content[_ngcontent-%COMP%]   .reels-grid[_ngcontent-%COMP%]   .reel-item[_ngcontent-%COMP%]   .reel-thumbnail[_ngcontent-%COMP%]   .reel-duration[_ngcontent-%COMP%] {\\n  position: absolute;\\n  bottom: 8px;\\n  right: 8px;\\n  background: rgba(0, 0, 0, 0.8);\\n  color: white;\\n  padding: 4px 8px;\\n  border-radius: 12px;\\n  font-size: 11px;\\n  font-weight: 600;\\n}\\n.reels-content[_ngcontent-%COMP%]   .reels-grid[_ngcontent-%COMP%]   .reel-item[_ngcontent-%COMP%]   .reel-thumbnail[_ngcontent-%COMP%]   .reel-views[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 8px;\\n  right: 8px;\\n  background: rgba(0, 0, 0, 0.7);\\n  color: white;\\n  padding: 4px 8px;\\n  border-radius: 12px;\\n  font-size: 10px;\\n  font-weight: 600;\\n  display: flex;\\n  align-items: center;\\n  gap: 4px;\\n}\\n.reels-content[_ngcontent-%COMP%]   .reels-grid[_ngcontent-%COMP%]   .reel-item[_ngcontent-%COMP%]   .reel-thumbnail[_ngcontent-%COMP%]   .reel-views[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n}\\n.reels-content[_ngcontent-%COMP%]   .reels-grid[_ngcontent-%COMP%]   .reel-item[_ngcontent-%COMP%]   .reel-info[_ngcontent-%COMP%] {\\n  padding: 12px;\\n}\\n.reels-content[_ngcontent-%COMP%]   .reels-grid[_ngcontent-%COMP%]   .reel-item[_ngcontent-%COMP%]   .reel-info[_ngcontent-%COMP%]   .creator-info[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  margin-bottom: 8px;\\n}\\n.reels-content[_ngcontent-%COMP%]   .reels-grid[_ngcontent-%COMP%]   .reel-item[_ngcontent-%COMP%]   .reel-info[_ngcontent-%COMP%]   .creator-info[_ngcontent-%COMP%]   .creator-avatar[_ngcontent-%COMP%] {\\n  width: 32px;\\n  height: 32px;\\n  border-radius: 50%;\\n  object-fit: cover;\\n  border: 2px solid #f0f0f0;\\n}\\n.reels-content[_ngcontent-%COMP%]   .reels-grid[_ngcontent-%COMP%]   .reel-item[_ngcontent-%COMP%]   .reel-info[_ngcontent-%COMP%]   .creator-info[_ngcontent-%COMP%]   .creator-details[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n.reels-content[_ngcontent-%COMP%]   .reels-grid[_ngcontent-%COMP%]   .reel-item[_ngcontent-%COMP%]   .reel-info[_ngcontent-%COMP%]   .creator-info[_ngcontent-%COMP%]   .creator-details[_ngcontent-%COMP%]   .creator-name[_ngcontent-%COMP%] {\\n  display: block;\\n  font-size: 12px;\\n  font-weight: 600;\\n  color: #333;\\n  line-height: 1.2;\\n}\\n.reels-content[_ngcontent-%COMP%]   .reels-grid[_ngcontent-%COMP%]   .reel-item[_ngcontent-%COMP%]   .reel-info[_ngcontent-%COMP%]   .creator-info[_ngcontent-%COMP%]   .creator-details[_ngcontent-%COMP%]   .creator-username[_ngcontent-%COMP%] {\\n  font-size: 10px;\\n  color: #666;\\n}\\n.reels-content[_ngcontent-%COMP%]   .reels-grid[_ngcontent-%COMP%]   .reel-item[_ngcontent-%COMP%]   .reel-info[_ngcontent-%COMP%]   .reel-title[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  font-weight: 600;\\n  margin: 0 0 8px 0;\\n  color: #333;\\n  line-height: 1.3;\\n  display: -webkit-box;\\n  -webkit-line-clamp: 2;\\n  -webkit-box-orient: vertical;\\n  overflow: hidden;\\n}\\n.reels-content[_ngcontent-%COMP%]   .reels-grid[_ngcontent-%COMP%]   .reel-item[_ngcontent-%COMP%]   .reel-info[_ngcontent-%COMP%]   .reel-stats[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n}\\n.reels-content[_ngcontent-%COMP%]   .reels-grid[_ngcontent-%COMP%]   .reel-item[_ngcontent-%COMP%]   .reel-info[_ngcontent-%COMP%]   .reel-stats[_ngcontent-%COMP%]   .stat-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 4px;\\n  font-size: 10px;\\n  color: #666;\\n}\\n.reels-content[_ngcontent-%COMP%]   .reels-grid[_ngcontent-%COMP%]   .reel-item[_ngcontent-%COMP%]   .reel-info[_ngcontent-%COMP%]   .reel-stats[_ngcontent-%COMP%]   .stat-item[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  color: #6c5ce7;\\n}\\n.reels-content[_ngcontent-%COMP%]   .reels-grid[_ngcontent-%COMP%]   .reel-item[_ngcontent-%COMP%]   .reel-info[_ngcontent-%COMP%]   .reel-stats[_ngcontent-%COMP%]   .stat-item[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n}\\n\\n@media (max-width: 768px) {\\n  .reels-content[_ngcontent-%COMP%]   .reels-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));\\n    gap: 12px;\\n  }\\n  .reels-content[_ngcontent-%COMP%]   .reels-grid[_ngcontent-%COMP%]   .reel-item[_ngcontent-%COMP%]   .reel-thumbnail[_ngcontent-%COMP%] {\\n    height: 220px;\\n  }\\n  .reels-content[_ngcontent-%COMP%]   .reels-grid[_ngcontent-%COMP%]   .reel-item[_ngcontent-%COMP%]   .reel-thumbnail[_ngcontent-%COMP%]   .play-overlay[_ngcontent-%COMP%] {\\n    width: 50px;\\n    height: 50px;\\n  }\\n  .reels-content[_ngcontent-%COMP%]   .reels-grid[_ngcontent-%COMP%]   .reel-item[_ngcontent-%COMP%]   .reel-thumbnail[_ngcontent-%COMP%]   .play-overlay[_ngcontent-%COMP%]   .play-icon[_ngcontent-%COMP%] {\\n    font-size: 20px;\\n  }\\n  .reels-content[_ngcontent-%COMP%]   .reels-grid[_ngcontent-%COMP%]   .reel-item[_ngcontent-%COMP%]   .reel-thumbnail[_ngcontent-%COMP%]   .reel-duration[_ngcontent-%COMP%] {\\n    font-size: 10px;\\n    padding: 3px 6px;\\n  }\\n  .reels-content[_ngcontent-%COMP%]   .reels-grid[_ngcontent-%COMP%]   .reel-item[_ngcontent-%COMP%]   .reel-thumbnail[_ngcontent-%COMP%]   .reel-views[_ngcontent-%COMP%] {\\n    font-size: 9px;\\n    padding: 3px 6px;\\n  }\\n  .reels-content[_ngcontent-%COMP%]   .reels-grid[_ngcontent-%COMP%]   .reel-item[_ngcontent-%COMP%]   .reel-thumbnail[_ngcontent-%COMP%]   .reel-views[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n    font-size: 10px;\\n  }\\n  .reels-content[_ngcontent-%COMP%]   .reels-grid[_ngcontent-%COMP%]   .reel-item[_ngcontent-%COMP%]   .reel-info[_ngcontent-%COMP%] {\\n    padding: 10px;\\n  }\\n  .reels-content[_ngcontent-%COMP%]   .reels-grid[_ngcontent-%COMP%]   .reel-item[_ngcontent-%COMP%]   .reel-info[_ngcontent-%COMP%]   .creator-info[_ngcontent-%COMP%]   .creator-avatar[_ngcontent-%COMP%] {\\n    width: 28px;\\n    height: 28px;\\n  }\\n  .reels-content[_ngcontent-%COMP%]   .reels-grid[_ngcontent-%COMP%]   .reel-item[_ngcontent-%COMP%]   .reel-info[_ngcontent-%COMP%]   .creator-info[_ngcontent-%COMP%]   .creator-details[_ngcontent-%COMP%]   .creator-name[_ngcontent-%COMP%] {\\n    font-size: 11px;\\n  }\\n  .reels-content[_ngcontent-%COMP%]   .reels-grid[_ngcontent-%COMP%]   .reel-item[_ngcontent-%COMP%]   .reel-info[_ngcontent-%COMP%]   .creator-info[_ngcontent-%COMP%]   .creator-details[_ngcontent-%COMP%]   .creator-username[_ngcontent-%COMP%] {\\n    font-size: 9px;\\n  }\\n  .reels-content[_ngcontent-%COMP%]   .reels-grid[_ngcontent-%COMP%]   .reel-item[_ngcontent-%COMP%]   .reel-info[_ngcontent-%COMP%]   .reel-title[_ngcontent-%COMP%] {\\n    font-size: 13px;\\n  }\\n  .reels-content[_ngcontent-%COMP%]   .reels-grid[_ngcontent-%COMP%]   .reel-item[_ngcontent-%COMP%]   .reel-info[_ngcontent-%COMP%]   .reel-stats[_ngcontent-%COMP%]   .stat-item[_ngcontent-%COMP%] {\\n    font-size: 9px;\\n  }\\n  .reels-content[_ngcontent-%COMP%]   .reels-grid[_ngcontent-%COMP%]   .reel-item[_ngcontent-%COMP%]   .reel-info[_ngcontent-%COMP%]   .reel-stats[_ngcontent-%COMP%]   .stat-item[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n    font-size: 11px;\\n  }\\n}\\n@media (max-width: 425px) {\\n  .reels-content[_ngcontent-%COMP%]   .reels-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: repeat(auto-fit, minmax(160px, 1fr));\\n    gap: 10px;\\n  }\\n  .reels-content[_ngcontent-%COMP%]   .reels-grid[_ngcontent-%COMP%]   .reel-item[_ngcontent-%COMP%]   .reel-thumbnail[_ngcontent-%COMP%] {\\n    height: 200px;\\n  }\\n  .reels-content[_ngcontent-%COMP%]   .reels-grid[_ngcontent-%COMP%]   .reel-item[_ngcontent-%COMP%]   .reel-thumbnail[_ngcontent-%COMP%]   .play-overlay[_ngcontent-%COMP%] {\\n    width: 45px;\\n    height: 45px;\\n  }\\n  .reels-content[_ngcontent-%COMP%]   .reels-grid[_ngcontent-%COMP%]   .reel-item[_ngcontent-%COMP%]   .reel-thumbnail[_ngcontent-%COMP%]   .play-overlay[_ngcontent-%COMP%]   .play-icon[_ngcontent-%COMP%] {\\n    font-size: 18px;\\n  }\\n  .reels-content[_ngcontent-%COMP%]   .reels-grid[_ngcontent-%COMP%]   .reel-item[_ngcontent-%COMP%]   .reel-info[_ngcontent-%COMP%] {\\n    padding: 8px;\\n  }\\n  .reels-content[_ngcontent-%COMP%]   .reels-grid[_ngcontent-%COMP%]   .reel-item[_ngcontent-%COMP%]   .reel-info[_ngcontent-%COMP%]   .creator-info[_ngcontent-%COMP%] {\\n    gap: 6px;\\n    margin-bottom: 6px;\\n  }\\n  .reels-content[_ngcontent-%COMP%]   .reels-grid[_ngcontent-%COMP%]   .reel-item[_ngcontent-%COMP%]   .reel-info[_ngcontent-%COMP%]   .creator-info[_ngcontent-%COMP%]   .creator-avatar[_ngcontent-%COMP%] {\\n    width: 24px;\\n    height: 24px;\\n  }\\n  .reels-content[_ngcontent-%COMP%]   .reels-grid[_ngcontent-%COMP%]   .reel-item[_ngcontent-%COMP%]   .reel-info[_ngcontent-%COMP%]   .creator-info[_ngcontent-%COMP%]   .creator-details[_ngcontent-%COMP%]   .creator-name[_ngcontent-%COMP%] {\\n    font-size: 10px;\\n  }\\n  .reels-content[_ngcontent-%COMP%]   .reels-grid[_ngcontent-%COMP%]   .reel-item[_ngcontent-%COMP%]   .reel-info[_ngcontent-%COMP%]   .creator-info[_ngcontent-%COMP%]   .creator-details[_ngcontent-%COMP%]   .creator-username[_ngcontent-%COMP%] {\\n    font-size: 8px;\\n  }\\n  .reels-content[_ngcontent-%COMP%]   .reels-grid[_ngcontent-%COMP%]   .reel-item[_ngcontent-%COMP%]   .reel-info[_ngcontent-%COMP%]   .reel-title[_ngcontent-%COMP%] {\\n    font-size: 12px;\\n    margin-bottom: 6px;\\n  }\\n  .reels-content[_ngcontent-%COMP%]   .reels-grid[_ngcontent-%COMP%]   .reel-item[_ngcontent-%COMP%]   .reel-info[_ngcontent-%COMP%]   .reel-stats[_ngcontent-%COMP%]   .stat-item[_ngcontent-%COMP%] {\\n    font-size: 8px;\\n  }\\n  .reels-content[_ngcontent-%COMP%]   .reels-grid[_ngcontent-%COMP%]   .reel-item[_ngcontent-%COMP%]   .reel-info[_ngcontent-%COMP%]   .reel-stats[_ngcontent-%COMP%]   .stat-item[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n    font-size: 10px;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "RouterModule", "IonicModule", "Subscription", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵtext", "ɵɵelementEnd", "ɵɵlistener", "TrendingReelsComponent_div_9_Template_button_click_4_listener", "ɵɵrestoreView", "_r1", "ctx_r1", "ɵɵnextContext", "ɵɵresetView", "onRetry", "ɵɵadvance", "ɵɵtextInterpolate", "error", "TrendingReelsComponent_div_10_div_2_Template_div_click_0_listener", "reel_r4", "_r3", "$implicit", "onReelClick", "ɵɵproperty", "thumbnail", "ɵɵsanitizeUrl", "title", "formatDuration", "duration", "ɵɵtextInterpolate1", "formatCount", "views", "creator", "avatar", "name", "username", "likes", "comments", "shares", "ɵɵtemplate", "TrendingReelsComponent_div_10_div_2_Template", "trendingReels", "trackByReelId", "TrendingReelsComponent", "constructor", "router", "isLoading", "subscription", "ngOnInit", "loadTrendingReels", "ngOnDestroy", "unsubscribe", "_this", "_asyncToGenerator", "loadMockReels", "console", "_this2", "Promise", "resolve", "setTimeout", "_id", "createdAt", "Date", "reel", "navigate", "seconds", "minutes", "Math", "floor", "remainingSeconds", "toString", "padStart", "count", "toFixed", "index", "onSeeAll", "event", "preventDefault", "ɵɵdirectiveInject", "i1", "Router", "selectors", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "TrendingReelsComponent_Template", "rf", "ctx", "TrendingReelsComponent_Template_a_click_5_listener", "$event", "TrendingReelsComponent_div_8_Template", "TrendingReelsComponent_div_9_Template", "TrendingReelsComponent_div_10_Template", "i2", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "i3", "IonIcon", "Ion<PERSON><PERSON><PERSON>", "styles"], "sources": ["E:\\Fashion\\DFashionFrontend\\frontend\\src\\app\\features\\home\\components\\trending-now\\trending-reels\\trending-reels.component.ts", "E:\\Fashion\\DFashionFrontend\\frontend\\src\\app\\features\\home\\components\\trending-now\\trending-reels\\trending-reels.component.html"], "sourcesContent": ["import { Component, OnInit, On<PERSON><PERSON>roy } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\nimport { IonicModule } from '@ionic/angular';\nimport { Router } from '@angular/router';\nimport { Subscription } from 'rxjs';\n\ninterface TrendingReel {\n  _id: string;\n  title: string;\n  thumbnail: string;\n  duration: number; // in seconds\n  views: number;\n  likes: number;\n  comments: number;\n  shares: number;\n  creator: {\n    _id: string;\n    name: string;\n    username: string;\n    avatar: string;\n  };\n  createdAt: Date;\n}\n\n@Component({\n  selector: 'app-trending-reels',\n  standalone: true,\n  imports: [CommonModule, RouterModule, IonicModule],\n  templateUrl: './trending-reels.component.html',\n  styleUrls: ['./trending-reels.component.scss']\n})\nexport class TrendingReelsComponent implements OnInit, OnDestroy {\n  trendingReels: TrendingReel[] = [];\n  isLoading = true;\n  error: string | null = null;\n  private subscription: Subscription = new Subscription();\n\n  constructor(private router: Router) {}\n\n  ngOnInit() {\n    this.loadTrendingReels();\n  }\n\n  ngOnDestroy() {\n    this.subscription.unsubscribe();\n  }\n\n  private async loadTrendingReels() {\n    try {\n      this.isLoading = true;\n      this.error = null;\n      await this.loadMockReels();\n    } catch (error) {\n      console.error('Error loading trending reels:', error);\n      this.error = 'Failed to load trending reels';\n      this.isLoading = false;\n    }\n  }\n\n  private async loadMockReels() {\n    await new Promise(resolve => setTimeout(resolve, 700));\n\n    this.trendingReels = [\n      {\n        _id: 'reel1',\n        title: 'Summer Outfit Transformation',\n        thumbnail: 'https://images.unsplash.com/photo-1515372039744-b8f02a3ae446?w=300&h=400&fit=crop',\n        duration: 45,\n        views: 125000,\n        likes: 8900,\n        comments: 456,\n        shares: 234,\n        creator: {\n          _id: 'creator1',\n          name: 'Maya Sharma',\n          username: 'fashionista_maya',\n          avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b47c?w=50&h=50&fit=crop&crop=face'\n        },\n        createdAt: new Date()\n      },\n      {\n        _id: 'reel2',\n        title: 'Street Style Inspiration',\n        thumbnail: 'https://images.unsplash.com/photo-1441986300917-64674bd600d8?w=300&h=400&fit=crop',\n        duration: 32,\n        views: 89000,\n        likes: 6700,\n        comments: 289,\n        shares: 156,\n        creator: {\n          _id: 'creator2',\n          name: 'Raj Patel',\n          username: 'style_guru_raj',\n          avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=50&h=50&fit=crop&crop=face'\n        },\n        createdAt: new Date()\n      },\n      {\n        _id: 'reel3',\n        title: 'Minimalist Wardrobe Essentials',\n        thumbnail: 'https://images.unsplash.com/photo-1445205170230-053b83016050?w=300&h=400&fit=crop',\n        duration: 58,\n        views: 67000,\n        likes: 4500,\n        comments: 178,\n        shares: 89,\n        creator: {\n          _id: 'creator3',\n          name: 'Priya Singh',\n          username: 'trendy_priya',\n          avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=50&h=50&fit=crop&crop=face'\n        },\n        createdAt: new Date()\n      },\n      {\n        _id: 'reel4',\n        title: 'Vintage Fashion Haul',\n        thumbnail: 'https://images.unsplash.com/photo-1434389677669-e08b4cac3105?w=300&h=400&fit=crop',\n        duration: 72,\n        views: 54000,\n        likes: 3200,\n        comments: 145,\n        shares: 67,\n        creator: {\n          _id: 'creator4',\n          name: 'Arjun Mehta',\n          username: 'luxury_lifestyle_arjun',\n          avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=50&h=50&fit=crop&crop=face'\n        },\n        createdAt: new Date()\n      },\n      {\n        _id: 'reel5',\n        title: 'Quick Styling Tips',\n        thumbnail: 'https://images.unsplash.com/photo-1469334031218-e382a71b716b?w=300&h=400&fit=crop',\n        duration: 28,\n        views: 98000,\n        likes: 7800,\n        comments: 345,\n        shares: 189,\n        creator: {\n          _id: 'creator1',\n          name: 'Maya Sharma',\n          username: 'fashionista_maya',\n          avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b47c?w=50&h=50&fit=crop&crop=face'\n        },\n        createdAt: new Date()\n      },\n      {\n        _id: 'reel6',\n        title: 'Accessory Styling Guide',\n        thumbnail: 'https://images.unsplash.com/photo-1553062407-98eeb64c6a62?w=300&h=400&fit=crop',\n        duration: 41,\n        views: 76000,\n        likes: 5600,\n        comments: 234,\n        shares: 123,\n        creator: {\n          _id: 'creator3',\n          name: 'Priya Singh',\n          username: 'trendy_priya',\n          avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=50&h=50&fit=crop&crop=face'\n        },\n        createdAt: new Date()\n      }\n    ];\n\n    this.isLoading = false;\n  }\n\n  onReelClick(reel: TrendingReel) {\n    this.router.navigate(['/reel', reel._id]);\n  }\n\n  formatDuration(seconds: number): string {\n    const minutes = Math.floor(seconds / 60);\n    const remainingSeconds = seconds % 60;\n    return minutes > 0 ? `${minutes}:${remainingSeconds.toString().padStart(2, '0')}` : `0:${remainingSeconds.toString().padStart(2, '0')}`;\n  }\n\n  formatCount(count: number): string {\n    if (count >= 1000000) {\n      return (count / 1000000).toFixed(1) + 'M';\n    } else if (count >= 1000) {\n      return (count / 1000).toFixed(1) + 'K';\n    }\n    return count.toString();\n  }\n\n  trackByReelId(index: number, reel: TrendingReel): string {\n    return reel._id;\n  }\n\n  onSeeAll(event: Event) {\n    event.preventDefault();\n    this.router.navigate(['/trending/reels']);\n  }\n\n  onRetry() {\n    this.loadTrendingReels();\n  }\n}\n", "<div class=\"component-container\">\n  <div class=\"component-header\">\n    <h3>\n      <ion-icon name=\"play-circle-outline\"></ion-icon>\n      Trending Reels\n    </h3>\n    <a href=\"#\" class=\"see-all\" (click)=\"onSeeAll($event)\">See All</a>\n  </div>\n\n  <div class=\"component-content\">\n    <div *ngIf=\"isLoading\" class=\"loading-container\">\n      <ion-spinner name=\"crescent\"></ion-spinner>\n      <p>Loading trending reels...</p>\n    </div>\n\n    <div *ngIf=\"error && !isLoading\" class=\"error-container\">\n      <ion-icon name=\"alert-circle-outline\"></ion-icon>\n      <p>{{ error }}</p>\n      <button class=\"retry-btn\" (click)=\"onRetry()\">Try Again</button>\n    </div>\n\n    <div *ngIf=\"!isLoading && !error\" class=\"reels-content\">\n      <div class=\"reels-grid\">\n        <div \n          *ngFor=\"let reel of trendingReels; trackBy: trackByReelId\" \n          class=\"reel-item\"\n          (click)=\"onReelClick(reel)\"\n        >\n          <div class=\"reel-thumbnail\">\n            <img [src]=\"reel.thumbnail\" [alt]=\"reel.title\" class=\"thumbnail-image\">\n            <div class=\"play-overlay\">\n              <ion-icon name=\"play\" class=\"play-icon\"></ion-icon>\n            </div>\n            <div class=\"reel-duration\">{{ formatDuration(reel.duration) }}</div>\n            <div class=\"reel-views\">\n              <ion-icon name=\"eye\"></ion-icon>\n              {{ formatCount(reel.views) }}\n            </div>\n          </div>\n          <div class=\"reel-info\">\n            <div class=\"creator-info\">\n              <img [src]=\"reel.creator.avatar\" [alt]=\"reel.creator.name\" class=\"creator-avatar\">\n              <div class=\"creator-details\">\n                <span class=\"creator-name\">{{ reel.creator.name }}</span>\n                <span class=\"creator-username\">{{ '@' + reel.creator.username }}</span>\n              </div>\n            </div>\n            <h5 class=\"reel-title\">{{ reel.title }}</h5>\n            <div class=\"reel-stats\">\n              <div class=\"stat-item\">\n                <ion-icon name=\"heart\"></ion-icon>\n                <span>{{ formatCount(reel.likes) }}</span>\n              </div>\n              <div class=\"stat-item\">\n                <ion-icon name=\"chatbubble\"></ion-icon>\n                <span>{{ formatCount(reel.comments) }}</span>\n              </div>\n              <div class=\"stat-item\">\n                <ion-icon name=\"share\"></ion-icon>\n                <span>{{ formatCount(reel.shares) }}</span>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n</div>\n"], "mappings": ";AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,QAAQ,gBAAgB;AAE5C,SAASC,YAAY,QAAQ,MAAM;;;;;;;ICK/BC,EAAA,CAAAC,cAAA,aAAiD;IAC/CD,EAAA,CAAAE,SAAA,qBAA2C;IAC3CF,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAG,MAAA,gCAAyB;IAC9BH,EAD8B,CAAAI,YAAA,EAAI,EAC5B;;;;;;IAENJ,EAAA,CAAAC,cAAA,cAAyD;IACvDD,EAAA,CAAAE,SAAA,mBAAiD;IACjDF,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAG,MAAA,GAAW;IAAAH,EAAA,CAAAI,YAAA,EAAI;IAClBJ,EAAA,CAAAC,cAAA,iBAA8C;IAApBD,EAAA,CAAAK,UAAA,mBAAAC,8DAAA;MAAAN,EAAA,CAAAO,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAG,OAAA,EAAS;IAAA,EAAC;IAACZ,EAAA,CAAAG,MAAA,gBAAS;IACzDH,EADyD,CAAAI,YAAA,EAAS,EAC5D;;;;IAFDJ,EAAA,CAAAa,SAAA,GAAW;IAAXb,EAAA,CAAAc,iBAAA,CAAAL,MAAA,CAAAM,KAAA,CAAW;;;;;;IAMZf,EAAA,CAAAC,cAAA,cAIC;IADCD,EAAA,CAAAK,UAAA,mBAAAW,kEAAA;MAAA,MAAAC,OAAA,GAAAjB,EAAA,CAAAO,aAAA,CAAAW,GAAA,EAAAC,SAAA;MAAA,MAAAV,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAW,WAAA,CAAAH,OAAA,CAAiB;IAAA,EAAC;IAE3BjB,EAAA,CAAAC,cAAA,cAA4B;IAC1BD,EAAA,CAAAE,SAAA,cAAuE;IACvEF,EAAA,CAAAC,cAAA,cAA0B;IACxBD,EAAA,CAAAE,SAAA,mBAAmD;IACrDF,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAC,cAAA,cAA2B;IAAAD,EAAA,CAAAG,MAAA,GAAmC;IAAAH,EAAA,CAAAI,YAAA,EAAM;IACpEJ,EAAA,CAAAC,cAAA,cAAwB;IACtBD,EAAA,CAAAE,SAAA,mBAAgC;IAChCF,EAAA,CAAAG,MAAA,GACF;IACFH,EADE,CAAAI,YAAA,EAAM,EACF;IAEJJ,EADF,CAAAC,cAAA,eAAuB,eACK;IACxBD,EAAA,CAAAE,SAAA,eAAkF;IAEhFF,EADF,CAAAC,cAAA,eAA6B,gBACA;IAAAD,EAAA,CAAAG,MAAA,IAAuB;IAAAH,EAAA,CAAAI,YAAA,EAAO;IACzDJ,EAAA,CAAAC,cAAA,gBAA+B;IAAAD,EAAA,CAAAG,MAAA,IAAiC;IAEpEH,EAFoE,CAAAI,YAAA,EAAO,EACnE,EACF;IACNJ,EAAA,CAAAC,cAAA,cAAuB;IAAAD,EAAA,CAAAG,MAAA,IAAgB;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAE1CJ,EADF,CAAAC,cAAA,eAAwB,eACC;IACrBD,EAAA,CAAAE,SAAA,oBAAkC;IAClCF,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAG,MAAA,IAA6B;IACrCH,EADqC,CAAAI,YAAA,EAAO,EACtC;IACNJ,EAAA,CAAAC,cAAA,eAAuB;IACrBD,EAAA,CAAAE,SAAA,oBAAuC;IACvCF,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAG,MAAA,IAAgC;IACxCH,EADwC,CAAAI,YAAA,EAAO,EACzC;IACNJ,EAAA,CAAAC,cAAA,eAAuB;IACrBD,EAAA,CAAAE,SAAA,oBAAkC;IAClCF,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAG,MAAA,IAA8B;IAI5CH,EAJ4C,CAAAI,YAAA,EAAO,EACvC,EACF,EACF,EACF;;;;;IAlCGJ,EAAA,CAAAa,SAAA,GAAsB;IAACb,EAAvB,CAAAqB,UAAA,QAAAJ,OAAA,CAAAK,SAAA,EAAAtB,EAAA,CAAAuB,aAAA,CAAsB,QAAAN,OAAA,CAAAO,KAAA,CAAmB;IAInBxB,EAAA,CAAAa,SAAA,GAAmC;IAAnCb,EAAA,CAAAc,iBAAA,CAAAL,MAAA,CAAAgB,cAAA,CAAAR,OAAA,CAAAS,QAAA,EAAmC;IAG5D1B,EAAA,CAAAa,SAAA,GACF;IADEb,EAAA,CAAA2B,kBAAA,MAAAlB,MAAA,CAAAmB,WAAA,CAAAX,OAAA,CAAAY,KAAA,OACF;IAIO7B,EAAA,CAAAa,SAAA,GAA2B;IAACb,EAA5B,CAAAqB,UAAA,QAAAJ,OAAA,CAAAa,OAAA,CAAAC,MAAA,EAAA/B,EAAA,CAAAuB,aAAA,CAA2B,QAAAN,OAAA,CAAAa,OAAA,CAAAE,IAAA,CAA0B;IAE7BhC,EAAA,CAAAa,SAAA,GAAuB;IAAvBb,EAAA,CAAAc,iBAAA,CAAAG,OAAA,CAAAa,OAAA,CAAAE,IAAA,CAAuB;IACnBhC,EAAA,CAAAa,SAAA,GAAiC;IAAjCb,EAAA,CAAAc,iBAAA,OAAAG,OAAA,CAAAa,OAAA,CAAAG,QAAA,CAAiC;IAG7CjC,EAAA,CAAAa,SAAA,GAAgB;IAAhBb,EAAA,CAAAc,iBAAA,CAAAG,OAAA,CAAAO,KAAA,CAAgB;IAI7BxB,EAAA,CAAAa,SAAA,GAA6B;IAA7Bb,EAAA,CAAAc,iBAAA,CAAAL,MAAA,CAAAmB,WAAA,CAAAX,OAAA,CAAAiB,KAAA,EAA6B;IAI7BlC,EAAA,CAAAa,SAAA,GAAgC;IAAhCb,EAAA,CAAAc,iBAAA,CAAAL,MAAA,CAAAmB,WAAA,CAAAX,OAAA,CAAAkB,QAAA,EAAgC;IAIhCnC,EAAA,CAAAa,SAAA,GAA8B;IAA9Bb,EAAA,CAAAc,iBAAA,CAAAL,MAAA,CAAAmB,WAAA,CAAAX,OAAA,CAAAmB,MAAA,EAA8B;;;;;IArC9CpC,EADF,CAAAC,cAAA,cAAwD,cAC9B;IACtBD,EAAA,CAAAqC,UAAA,IAAAC,4CAAA,oBAIC;IAsCLtC,EADE,CAAAI,YAAA,EAAM,EACF;;;;IAzCiBJ,EAAA,CAAAa,SAAA,GAAkB;IAAAb,EAAlB,CAAAqB,UAAA,YAAAZ,MAAA,CAAA8B,aAAA,CAAkB,iBAAA9B,MAAA,CAAA+B,aAAA,CAAsB;;;ADQnE,OAAM,MAAOC,sBAAsB;EAMjCC,YAAoBC,MAAc;IAAd,KAAAA,MAAM,GAANA,MAAM;IAL1B,KAAAJ,aAAa,GAAmB,EAAE;IAClC,KAAAK,SAAS,GAAG,IAAI;IAChB,KAAA7B,KAAK,GAAkB,IAAI;IACnB,KAAA8B,YAAY,GAAiB,IAAI9C,YAAY,EAAE;EAElB;EAErC+C,QAAQA,CAAA;IACN,IAAI,CAACC,iBAAiB,EAAE;EAC1B;EAEAC,WAAWA,CAAA;IACT,IAAI,CAACH,YAAY,CAACI,WAAW,EAAE;EACjC;EAEcF,iBAAiBA,CAAA;IAAA,IAAAG,KAAA;IAAA,OAAAC,iBAAA;MAC7B,IAAI;QACFD,KAAI,CAACN,SAAS,GAAG,IAAI;QACrBM,KAAI,CAACnC,KAAK,GAAG,IAAI;QACjB,MAAMmC,KAAI,CAACE,aAAa,EAAE;OAC3B,CAAC,OAAOrC,KAAK,EAAE;QACdsC,OAAO,CAACtC,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;QACrDmC,KAAI,CAACnC,KAAK,GAAG,+BAA+B;QAC5CmC,KAAI,CAACN,SAAS,GAAG,KAAK;;IACvB;EACH;EAEcQ,aAAaA,CAAA;IAAA,IAAAE,MAAA;IAAA,OAAAH,iBAAA;MACzB,MAAM,IAAII,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,GAAG,CAAC,CAAC;MAEtDF,MAAI,CAACf,aAAa,GAAG,CACnB;QACEmB,GAAG,EAAE,OAAO;QACZlC,KAAK,EAAE,8BAA8B;QACrCF,SAAS,EAAE,mFAAmF;QAC9FI,QAAQ,EAAE,EAAE;QACZG,KAAK,EAAE,MAAM;QACbK,KAAK,EAAE,IAAI;QACXC,QAAQ,EAAE,GAAG;QACbC,MAAM,EAAE,GAAG;QACXN,OAAO,EAAE;UACP4B,GAAG,EAAE,UAAU;UACf1B,IAAI,EAAE,aAAa;UACnBC,QAAQ,EAAE,kBAAkB;UAC5BF,MAAM,EAAE;SACT;QACD4B,SAAS,EAAE,IAAIC,IAAI;OACpB,EACD;QACEF,GAAG,EAAE,OAAO;QACZlC,KAAK,EAAE,0BAA0B;QACjCF,SAAS,EAAE,mFAAmF;QAC9FI,QAAQ,EAAE,EAAE;QACZG,KAAK,EAAE,KAAK;QACZK,KAAK,EAAE,IAAI;QACXC,QAAQ,EAAE,GAAG;QACbC,MAAM,EAAE,GAAG;QACXN,OAAO,EAAE;UACP4B,GAAG,EAAE,UAAU;UACf1B,IAAI,EAAE,WAAW;UACjBC,QAAQ,EAAE,gBAAgB;UAC1BF,MAAM,EAAE;SACT;QACD4B,SAAS,EAAE,IAAIC,IAAI;OACpB,EACD;QACEF,GAAG,EAAE,OAAO;QACZlC,KAAK,EAAE,gCAAgC;QACvCF,SAAS,EAAE,mFAAmF;QAC9FI,QAAQ,EAAE,EAAE;QACZG,KAAK,EAAE,KAAK;QACZK,KAAK,EAAE,IAAI;QACXC,QAAQ,EAAE,GAAG;QACbC,MAAM,EAAE,EAAE;QACVN,OAAO,EAAE;UACP4B,GAAG,EAAE,UAAU;UACf1B,IAAI,EAAE,aAAa;UACnBC,QAAQ,EAAE,cAAc;UACxBF,MAAM,EAAE;SACT;QACD4B,SAAS,EAAE,IAAIC,IAAI;OACpB,EACD;QACEF,GAAG,EAAE,OAAO;QACZlC,KAAK,EAAE,sBAAsB;QAC7BF,SAAS,EAAE,mFAAmF;QAC9FI,QAAQ,EAAE,EAAE;QACZG,KAAK,EAAE,KAAK;QACZK,KAAK,EAAE,IAAI;QACXC,QAAQ,EAAE,GAAG;QACbC,MAAM,EAAE,EAAE;QACVN,OAAO,EAAE;UACP4B,GAAG,EAAE,UAAU;UACf1B,IAAI,EAAE,aAAa;UACnBC,QAAQ,EAAE,wBAAwB;UAClCF,MAAM,EAAE;SACT;QACD4B,SAAS,EAAE,IAAIC,IAAI;OACpB,EACD;QACEF,GAAG,EAAE,OAAO;QACZlC,KAAK,EAAE,oBAAoB;QAC3BF,SAAS,EAAE,mFAAmF;QAC9FI,QAAQ,EAAE,EAAE;QACZG,KAAK,EAAE,KAAK;QACZK,KAAK,EAAE,IAAI;QACXC,QAAQ,EAAE,GAAG;QACbC,MAAM,EAAE,GAAG;QACXN,OAAO,EAAE;UACP4B,GAAG,EAAE,UAAU;UACf1B,IAAI,EAAE,aAAa;UACnBC,QAAQ,EAAE,kBAAkB;UAC5BF,MAAM,EAAE;SACT;QACD4B,SAAS,EAAE,IAAIC,IAAI;OACpB,EACD;QACEF,GAAG,EAAE,OAAO;QACZlC,KAAK,EAAE,yBAAyB;QAChCF,SAAS,EAAE,gFAAgF;QAC3FI,QAAQ,EAAE,EAAE;QACZG,KAAK,EAAE,KAAK;QACZK,KAAK,EAAE,IAAI;QACXC,QAAQ,EAAE,GAAG;QACbC,MAAM,EAAE,GAAG;QACXN,OAAO,EAAE;UACP4B,GAAG,EAAE,UAAU;UACf1B,IAAI,EAAE,aAAa;UACnBC,QAAQ,EAAE,cAAc;UACxBF,MAAM,EAAE;SACT;QACD4B,SAAS,EAAE,IAAIC,IAAI;OACpB,CACF;MAEDN,MAAI,CAACV,SAAS,GAAG,KAAK;IAAC;EACzB;EAEAxB,WAAWA,CAACyC,IAAkB;IAC5B,IAAI,CAAClB,MAAM,CAACmB,QAAQ,CAAC,CAAC,OAAO,EAAED,IAAI,CAACH,GAAG,CAAC,CAAC;EAC3C;EAEAjC,cAAcA,CAACsC,OAAe;IAC5B,MAAMC,OAAO,GAAGC,IAAI,CAACC,KAAK,CAACH,OAAO,GAAG,EAAE,CAAC;IACxC,MAAMI,gBAAgB,GAAGJ,OAAO,GAAG,EAAE;IACrC,OAAOC,OAAO,GAAG,CAAC,GAAG,GAAGA,OAAO,IAAIG,gBAAgB,CAACC,QAAQ,EAAE,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE,GAAG,KAAKF,gBAAgB,CAACC,QAAQ,EAAE,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE;EACzI;EAEAzC,WAAWA,CAAC0C,KAAa;IACvB,IAAIA,KAAK,IAAI,OAAO,EAAE;MACpB,OAAO,CAACA,KAAK,GAAG,OAAO,EAAEC,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG;KAC1C,MAAM,IAAID,KAAK,IAAI,IAAI,EAAE;MACxB,OAAO,CAACA,KAAK,GAAG,IAAI,EAAEC,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG;;IAExC,OAAOD,KAAK,CAACF,QAAQ,EAAE;EACzB;EAEA5B,aAAaA,CAACgC,KAAa,EAAEX,IAAkB;IAC7C,OAAOA,IAAI,CAACH,GAAG;EACjB;EAEAe,QAAQA,CAACC,KAAY;IACnBA,KAAK,CAACC,cAAc,EAAE;IACtB,IAAI,CAAChC,MAAM,CAACmB,QAAQ,CAAC,CAAC,iBAAiB,CAAC,CAAC;EAC3C;EAEAlD,OAAOA,CAAA;IACL,IAAI,CAACmC,iBAAiB,EAAE;EAC1B;;;uBAzKWN,sBAAsB,EAAAzC,EAAA,CAAA4E,iBAAA,CAAAC,EAAA,CAAAC,MAAA;IAAA;EAAA;;;YAAtBrC,sBAAsB;MAAAsC,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAAjF,EAAA,CAAAkF,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,gCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UC9B/BxF,EAFJ,CAAAC,cAAA,aAAiC,aACD,SACxB;UACFD,EAAA,CAAAE,SAAA,kBAAgD;UAChDF,EAAA,CAAAG,MAAA,uBACF;UAAAH,EAAA,CAAAI,YAAA,EAAK;UACLJ,EAAA,CAAAC,cAAA,WAAuD;UAA3BD,EAAA,CAAAK,UAAA,mBAAAqF,mDAAAC,MAAA;YAAA,OAASF,GAAA,CAAAhB,QAAA,CAAAkB,MAAA,CAAgB;UAAA,EAAC;UAAC3F,EAAA,CAAAG,MAAA,cAAO;UAChEH,EADgE,CAAAI,YAAA,EAAI,EAC9D;UAENJ,EAAA,CAAAC,cAAA,aAA+B;UAY7BD,EAXA,CAAAqC,UAAA,IAAAuD,qCAAA,iBAAiD,IAAAC,qCAAA,iBAKQ,KAAAC,sCAAA,iBAMD;UA8C5D9F,EADE,CAAAI,YAAA,EAAM,EACF;;;UAzDIJ,EAAA,CAAAa,SAAA,GAAe;UAAfb,EAAA,CAAAqB,UAAA,SAAAoE,GAAA,CAAA7C,SAAA,CAAe;UAKf5C,EAAA,CAAAa,SAAA,EAAyB;UAAzBb,EAAA,CAAAqB,UAAA,SAAAoE,GAAA,CAAA1E,KAAA,KAAA0E,GAAA,CAAA7C,SAAA,CAAyB;UAMzB5C,EAAA,CAAAa,SAAA,EAA0B;UAA1Bb,EAAA,CAAAqB,UAAA,UAAAoE,GAAA,CAAA7C,SAAA,KAAA6C,GAAA,CAAA1E,KAAA,CAA0B;;;qBDOxBnB,YAAY,EAAAmG,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EAAEpG,YAAY,EAAEC,WAAW,EAAAoG,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,UAAA;MAAAC,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}