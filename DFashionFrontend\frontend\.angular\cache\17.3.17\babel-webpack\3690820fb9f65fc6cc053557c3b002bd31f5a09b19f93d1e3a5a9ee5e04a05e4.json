{"ast": null, "code": "import _asyncToGenerator from \"E:/Fashion/DFashionFrontend/frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { __decorate } from \"tslib\";\nimport { Component } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\nimport { IonicModule } from '@ionic/angular';\nimport { Subscription } from 'rxjs';\nlet TrendingReelsComponent = class TrendingReelsComponent {\n  constructor(router) {\n    this.router = router;\n    this.trendingReels = [];\n    this.isLoading = true;\n    this.error = null;\n    this.subscription = new Subscription();\n  }\n  ngOnInit() {\n    this.loadTrendingReels();\n  }\n  ngOnDestroy() {\n    this.subscription.unsubscribe();\n  }\n  loadTrendingReels() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      try {\n        _this.isLoading = true;\n        _this.error = null;\n        yield _this.loadMockReels();\n      } catch (error) {\n        console.error('Error loading trending reels:', error);\n        _this.error = 'Failed to load trending reels';\n        _this.isLoading = false;\n      }\n    })();\n  }\n  loadMockReels() {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      yield new Promise(resolve => setTimeout(resolve, 700));\n      _this2.trendingReels = [{\n        _id: 'reel1',\n        title: 'Summer Outfit Transformation',\n        thumbnail: 'https://images.unsplash.com/photo-1515372039744-b8f02a3ae446?w=300&h=400&fit=crop',\n        duration: 45,\n        views: 125000,\n        likes: 8900,\n        comments: 456,\n        shares: 234,\n        creator: {\n          _id: 'creator1',\n          name: 'Maya Sharma',\n          username: 'fashionista_maya',\n          avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b47c?w=50&h=50&fit=crop&crop=face'\n        },\n        createdAt: new Date()\n      }, {\n        _id: 'reel2',\n        title: 'Street Style Inspiration',\n        thumbnail: 'https://images.unsplash.com/photo-1441986300917-64674bd600d8?w=300&h=400&fit=crop',\n        duration: 32,\n        views: 89000,\n        likes: 6700,\n        comments: 289,\n        shares: 156,\n        creator: {\n          _id: 'creator2',\n          name: 'Raj Patel',\n          username: 'style_guru_raj',\n          avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=50&h=50&fit=crop&crop=face'\n        },\n        createdAt: new Date()\n      }, {\n        _id: 'reel3',\n        title: 'Minimalist Wardrobe Essentials',\n        thumbnail: 'https://images.unsplash.com/photo-1445205170230-053b83016050?w=300&h=400&fit=crop',\n        duration: 58,\n        views: 67000,\n        likes: 4500,\n        comments: 178,\n        shares: 89,\n        creator: {\n          _id: 'creator3',\n          name: 'Priya Singh',\n          username: 'trendy_priya',\n          avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=50&h=50&fit=crop&crop=face'\n        },\n        createdAt: new Date()\n      }, {\n        _id: 'reel4',\n        title: 'Vintage Fashion Haul',\n        thumbnail: 'https://images.unsplash.com/photo-1434389677669-e08b4cac3105?w=300&h=400&fit=crop',\n        duration: 72,\n        views: 54000,\n        likes: 3200,\n        comments: 145,\n        shares: 67,\n        creator: {\n          _id: 'creator4',\n          name: 'Arjun Mehta',\n          username: 'luxury_lifestyle_arjun',\n          avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=50&h=50&fit=crop&crop=face'\n        },\n        createdAt: new Date()\n      }, {\n        _id: 'reel5',\n        title: 'Quick Styling Tips',\n        thumbnail: 'https://images.unsplash.com/photo-1469334031218-e382a71b716b?w=300&h=400&fit=crop',\n        duration: 28,\n        views: 98000,\n        likes: 7800,\n        comments: 345,\n        shares: 189,\n        creator: {\n          _id: 'creator1',\n          name: 'Maya Sharma',\n          username: 'fashionista_maya',\n          avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b47c?w=50&h=50&fit=crop&crop=face'\n        },\n        createdAt: new Date()\n      }, {\n        _id: 'reel6',\n        title: 'Accessory Styling Guide',\n        thumbnail: 'https://images.unsplash.com/photo-1553062407-98eeb64c6a62?w=300&h=400&fit=crop',\n        duration: 41,\n        views: 76000,\n        likes: 5600,\n        comments: 234,\n        shares: 123,\n        creator: {\n          _id: 'creator3',\n          name: 'Priya Singh',\n          username: 'trendy_priya',\n          avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=50&h=50&fit=crop&crop=face'\n        },\n        createdAt: new Date()\n      }];\n      _this2.isLoading = false;\n    })();\n  }\n  onReelClick(reel) {\n    this.router.navigate(['/reel', reel._id]);\n  }\n  formatDuration(seconds) {\n    const minutes = Math.floor(seconds / 60);\n    const remainingSeconds = seconds % 60;\n    return minutes > 0 ? `${minutes}:${remainingSeconds.toString().padStart(2, '0')}` : `0:${remainingSeconds.toString().padStart(2, '0')}`;\n  }\n  formatCount(count) {\n    if (count >= 1000000) {\n      return (count / 1000000).toFixed(1) + 'M';\n    } else if (count >= 1000) {\n      return (count / 1000).toFixed(1) + 'K';\n    }\n    return count.toString();\n  }\n  trackByReelId(index, reel) {\n    return reel._id;\n  }\n  onSeeAll(event) {\n    event.preventDefault();\n    this.router.navigate(['/trending/reels']);\n  }\n  onRetry() {\n    this.loadTrendingReels();\n  }\n};\nTrendingReelsComponent = __decorate([Component({\n  selector: 'app-trending-reels',\n  standalone: true,\n  imports: [CommonModule, RouterModule, IonicModule],\n  templateUrl: './trending-reels.component.html',\n  styleUrls: ['./trending-reels.component.scss']\n})], TrendingReelsComponent);\nexport { TrendingReelsComponent };", "map": {"version": 3, "names": ["Component", "CommonModule", "RouterModule", "IonicModule", "Subscription", "TrendingReelsComponent", "constructor", "router", "trendingReels", "isLoading", "error", "subscription", "ngOnInit", "loadTrendingReels", "ngOnDestroy", "unsubscribe", "_this", "_asyncToGenerator", "loadMockReels", "console", "_this2", "Promise", "resolve", "setTimeout", "_id", "title", "thumbnail", "duration", "views", "likes", "comments", "shares", "creator", "name", "username", "avatar", "createdAt", "Date", "onReelClick", "reel", "navigate", "formatDuration", "seconds", "minutes", "Math", "floor", "remainingSeconds", "toString", "padStart", "formatCount", "count", "toFixed", "trackByReelId", "index", "onSeeAll", "event", "preventDefault", "onRetry", "__decorate", "selector", "standalone", "imports", "templateUrl", "styleUrls"], "sources": ["E:\\Fashion\\DFashionFrontend\\frontend\\src\\app\\features\\home\\components\\trending-now\\trending-reels\\trending-reels.component.ts"], "sourcesContent": ["import { Component, OnInit, On<PERSON><PERSON>roy } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\nimport { IonicModule } from '@ionic/angular';\nimport { Router } from '@angular/router';\nimport { Subscription } from 'rxjs';\n\ninterface TrendingReel {\n  _id: string;\n  title: string;\n  thumbnail: string;\n  duration: number; // in seconds\n  views: number;\n  likes: number;\n  comments: number;\n  shares: number;\n  creator: {\n    _id: string;\n    name: string;\n    username: string;\n    avatar: string;\n  };\n  createdAt: Date;\n}\n\n@Component({\n  selector: 'app-trending-reels',\n  standalone: true,\n  imports: [CommonModule, RouterModule, IonicModule],\n  templateUrl: './trending-reels.component.html',\n  styleUrls: ['./trending-reels.component.scss']\n})\nexport class TrendingReelsComponent implements OnInit, OnDestroy {\n  trendingReels: TrendingReel[] = [];\n  isLoading = true;\n  error: string | null = null;\n  private subscription: Subscription = new Subscription();\n\n  constructor(private router: Router) {}\n\n  ngOnInit() {\n    this.loadTrendingReels();\n  }\n\n  ngOnDestroy() {\n    this.subscription.unsubscribe();\n  }\n\n  private async loadTrendingReels() {\n    try {\n      this.isLoading = true;\n      this.error = null;\n      await this.loadMockReels();\n    } catch (error) {\n      console.error('Error loading trending reels:', error);\n      this.error = 'Failed to load trending reels';\n      this.isLoading = false;\n    }\n  }\n\n  private async loadMockReels() {\n    await new Promise(resolve => setTimeout(resolve, 700));\n\n    this.trendingReels = [\n      {\n        _id: 'reel1',\n        title: 'Summer Outfit Transformation',\n        thumbnail: 'https://images.unsplash.com/photo-1515372039744-b8f02a3ae446?w=300&h=400&fit=crop',\n        duration: 45,\n        views: 125000,\n        likes: 8900,\n        comments: 456,\n        shares: 234,\n        creator: {\n          _id: 'creator1',\n          name: 'Maya Sharma',\n          username: 'fashionista_maya',\n          avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b47c?w=50&h=50&fit=crop&crop=face'\n        },\n        createdAt: new Date()\n      },\n      {\n        _id: 'reel2',\n        title: 'Street Style Inspiration',\n        thumbnail: 'https://images.unsplash.com/photo-1441986300917-64674bd600d8?w=300&h=400&fit=crop',\n        duration: 32,\n        views: 89000,\n        likes: 6700,\n        comments: 289,\n        shares: 156,\n        creator: {\n          _id: 'creator2',\n          name: 'Raj Patel',\n          username: 'style_guru_raj',\n          avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=50&h=50&fit=crop&crop=face'\n        },\n        createdAt: new Date()\n      },\n      {\n        _id: 'reel3',\n        title: 'Minimalist Wardrobe Essentials',\n        thumbnail: 'https://images.unsplash.com/photo-1445205170230-053b83016050?w=300&h=400&fit=crop',\n        duration: 58,\n        views: 67000,\n        likes: 4500,\n        comments: 178,\n        shares: 89,\n        creator: {\n          _id: 'creator3',\n          name: 'Priya Singh',\n          username: 'trendy_priya',\n          avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=50&h=50&fit=crop&crop=face'\n        },\n        createdAt: new Date()\n      },\n      {\n        _id: 'reel4',\n        title: 'Vintage Fashion Haul',\n        thumbnail: 'https://images.unsplash.com/photo-1434389677669-e08b4cac3105?w=300&h=400&fit=crop',\n        duration: 72,\n        views: 54000,\n        likes: 3200,\n        comments: 145,\n        shares: 67,\n        creator: {\n          _id: 'creator4',\n          name: 'Arjun Mehta',\n          username: 'luxury_lifestyle_arjun',\n          avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=50&h=50&fit=crop&crop=face'\n        },\n        createdAt: new Date()\n      },\n      {\n        _id: 'reel5',\n        title: 'Quick Styling Tips',\n        thumbnail: 'https://images.unsplash.com/photo-1469334031218-e382a71b716b?w=300&h=400&fit=crop',\n        duration: 28,\n        views: 98000,\n        likes: 7800,\n        comments: 345,\n        shares: 189,\n        creator: {\n          _id: 'creator1',\n          name: 'Maya Sharma',\n          username: 'fashionista_maya',\n          avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b47c?w=50&h=50&fit=crop&crop=face'\n        },\n        createdAt: new Date()\n      },\n      {\n        _id: 'reel6',\n        title: 'Accessory Styling Guide',\n        thumbnail: 'https://images.unsplash.com/photo-1553062407-98eeb64c6a62?w=300&h=400&fit=crop',\n        duration: 41,\n        views: 76000,\n        likes: 5600,\n        comments: 234,\n        shares: 123,\n        creator: {\n          _id: 'creator3',\n          name: 'Priya Singh',\n          username: 'trendy_priya',\n          avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=50&h=50&fit=crop&crop=face'\n        },\n        createdAt: new Date()\n      }\n    ];\n\n    this.isLoading = false;\n  }\n\n  onReelClick(reel: TrendingReel) {\n    this.router.navigate(['/reel', reel._id]);\n  }\n\n  formatDuration(seconds: number): string {\n    const minutes = Math.floor(seconds / 60);\n    const remainingSeconds = seconds % 60;\n    return minutes > 0 ? `${minutes}:${remainingSeconds.toString().padStart(2, '0')}` : `0:${remainingSeconds.toString().padStart(2, '0')}`;\n  }\n\n  formatCount(count: number): string {\n    if (count >= 1000000) {\n      return (count / 1000000).toFixed(1) + 'M';\n    } else if (count >= 1000) {\n      return (count / 1000).toFixed(1) + 'K';\n    }\n    return count.toString();\n  }\n\n  trackByReelId(index: number, reel: TrendingReel): string {\n    return reel._id;\n  }\n\n  onSeeAll(event: Event) {\n    event.preventDefault();\n    this.router.navigate(['/trending/reels']);\n  }\n\n  onRetry() {\n    this.loadTrendingReels();\n  }\n}\n"], "mappings": ";;AAAA,SAASA,SAAS,QAA2B,eAAe;AAC5D,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,QAAQ,gBAAgB;AAE5C,SAASC,YAAY,QAAQ,MAAM;AA2B5B,IAAMC,sBAAsB,GAA5B,MAAMA,sBAAsB;EAMjCC,YAAoBC,MAAc;IAAd,KAAAA,MAAM,GAANA,MAAM;IAL1B,KAAAC,aAAa,GAAmB,EAAE;IAClC,KAAAC,SAAS,GAAG,IAAI;IAChB,KAAAC,KAAK,GAAkB,IAAI;IACnB,KAAAC,YAAY,GAAiB,IAAIP,YAAY,EAAE;EAElB;EAErCQ,QAAQA,CAAA;IACN,IAAI,CAACC,iBAAiB,EAAE;EAC1B;EAEAC,WAAWA,CAAA;IACT,IAAI,CAACH,YAAY,CAACI,WAAW,EAAE;EACjC;EAEcF,iBAAiBA,CAAA;IAAA,IAAAG,KAAA;IAAA,OAAAC,iBAAA;MAC7B,IAAI;QACFD,KAAI,CAACP,SAAS,GAAG,IAAI;QACrBO,KAAI,CAACN,KAAK,GAAG,IAAI;QACjB,MAAMM,KAAI,CAACE,aAAa,EAAE;OAC3B,CAAC,OAAOR,KAAK,EAAE;QACdS,OAAO,CAACT,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;QACrDM,KAAI,CAACN,KAAK,GAAG,+BAA+B;QAC5CM,KAAI,CAACP,SAAS,GAAG,KAAK;;IACvB;EACH;EAEcS,aAAaA,CAAA;IAAA,IAAAE,MAAA;IAAA,OAAAH,iBAAA;MACzB,MAAM,IAAII,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,GAAG,CAAC,CAAC;MAEtDF,MAAI,CAACZ,aAAa,GAAG,CACnB;QACEgB,GAAG,EAAE,OAAO;QACZC,KAAK,EAAE,8BAA8B;QACrCC,SAAS,EAAE,mFAAmF;QAC9FC,QAAQ,EAAE,EAAE;QACZC,KAAK,EAAE,MAAM;QACbC,KAAK,EAAE,IAAI;QACXC,QAAQ,EAAE,GAAG;QACbC,MAAM,EAAE,GAAG;QACXC,OAAO,EAAE;UACPR,GAAG,EAAE,UAAU;UACfS,IAAI,EAAE,aAAa;UACnBC,QAAQ,EAAE,kBAAkB;UAC5BC,MAAM,EAAE;SACT;QACDC,SAAS,EAAE,IAAIC,IAAI;OACpB,EACD;QACEb,GAAG,EAAE,OAAO;QACZC,KAAK,EAAE,0BAA0B;QACjCC,SAAS,EAAE,mFAAmF;QAC9FC,QAAQ,EAAE,EAAE;QACZC,KAAK,EAAE,KAAK;QACZC,KAAK,EAAE,IAAI;QACXC,QAAQ,EAAE,GAAG;QACbC,MAAM,EAAE,GAAG;QACXC,OAAO,EAAE;UACPR,GAAG,EAAE,UAAU;UACfS,IAAI,EAAE,WAAW;UACjBC,QAAQ,EAAE,gBAAgB;UAC1BC,MAAM,EAAE;SACT;QACDC,SAAS,EAAE,IAAIC,IAAI;OACpB,EACD;QACEb,GAAG,EAAE,OAAO;QACZC,KAAK,EAAE,gCAAgC;QACvCC,SAAS,EAAE,mFAAmF;QAC9FC,QAAQ,EAAE,EAAE;QACZC,KAAK,EAAE,KAAK;QACZC,KAAK,EAAE,IAAI;QACXC,QAAQ,EAAE,GAAG;QACbC,MAAM,EAAE,EAAE;QACVC,OAAO,EAAE;UACPR,GAAG,EAAE,UAAU;UACfS,IAAI,EAAE,aAAa;UACnBC,QAAQ,EAAE,cAAc;UACxBC,MAAM,EAAE;SACT;QACDC,SAAS,EAAE,IAAIC,IAAI;OACpB,EACD;QACEb,GAAG,EAAE,OAAO;QACZC,KAAK,EAAE,sBAAsB;QAC7BC,SAAS,EAAE,mFAAmF;QAC9FC,QAAQ,EAAE,EAAE;QACZC,KAAK,EAAE,KAAK;QACZC,KAAK,EAAE,IAAI;QACXC,QAAQ,EAAE,GAAG;QACbC,MAAM,EAAE,EAAE;QACVC,OAAO,EAAE;UACPR,GAAG,EAAE,UAAU;UACfS,IAAI,EAAE,aAAa;UACnBC,QAAQ,EAAE,wBAAwB;UAClCC,MAAM,EAAE;SACT;QACDC,SAAS,EAAE,IAAIC,IAAI;OACpB,EACD;QACEb,GAAG,EAAE,OAAO;QACZC,KAAK,EAAE,oBAAoB;QAC3BC,SAAS,EAAE,mFAAmF;QAC9FC,QAAQ,EAAE,EAAE;QACZC,KAAK,EAAE,KAAK;QACZC,KAAK,EAAE,IAAI;QACXC,QAAQ,EAAE,GAAG;QACbC,MAAM,EAAE,GAAG;QACXC,OAAO,EAAE;UACPR,GAAG,EAAE,UAAU;UACfS,IAAI,EAAE,aAAa;UACnBC,QAAQ,EAAE,kBAAkB;UAC5BC,MAAM,EAAE;SACT;QACDC,SAAS,EAAE,IAAIC,IAAI;OACpB,EACD;QACEb,GAAG,EAAE,OAAO;QACZC,KAAK,EAAE,yBAAyB;QAChCC,SAAS,EAAE,gFAAgF;QAC3FC,QAAQ,EAAE,EAAE;QACZC,KAAK,EAAE,KAAK;QACZC,KAAK,EAAE,IAAI;QACXC,QAAQ,EAAE,GAAG;QACbC,MAAM,EAAE,GAAG;QACXC,OAAO,EAAE;UACPR,GAAG,EAAE,UAAU;UACfS,IAAI,EAAE,aAAa;UACnBC,QAAQ,EAAE,cAAc;UACxBC,MAAM,EAAE;SACT;QACDC,SAAS,EAAE,IAAIC,IAAI;OACpB,CACF;MAEDjB,MAAI,CAACX,SAAS,GAAG,KAAK;IAAC;EACzB;EAEA6B,WAAWA,CAACC,IAAkB;IAC5B,IAAI,CAAChC,MAAM,CAACiC,QAAQ,CAAC,CAAC,OAAO,EAAED,IAAI,CAACf,GAAG,CAAC,CAAC;EAC3C;EAEAiB,cAAcA,CAACC,OAAe;IAC5B,MAAMC,OAAO,GAAGC,IAAI,CAACC,KAAK,CAACH,OAAO,GAAG,EAAE,CAAC;IACxC,MAAMI,gBAAgB,GAAGJ,OAAO,GAAG,EAAE;IACrC,OAAOC,OAAO,GAAG,CAAC,GAAG,GAAGA,OAAO,IAAIG,gBAAgB,CAACC,QAAQ,EAAE,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE,GAAG,KAAKF,gBAAgB,CAACC,QAAQ,EAAE,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE;EACzI;EAEAC,WAAWA,CAACC,KAAa;IACvB,IAAIA,KAAK,IAAI,OAAO,EAAE;MACpB,OAAO,CAACA,KAAK,GAAG,OAAO,EAAEC,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG;KAC1C,MAAM,IAAID,KAAK,IAAI,IAAI,EAAE;MACxB,OAAO,CAACA,KAAK,GAAG,IAAI,EAAEC,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG;;IAExC,OAAOD,KAAK,CAACH,QAAQ,EAAE;EACzB;EAEAK,aAAaA,CAACC,KAAa,EAAEd,IAAkB;IAC7C,OAAOA,IAAI,CAACf,GAAG;EACjB;EAEA8B,QAAQA,CAACC,KAAY;IACnBA,KAAK,CAACC,cAAc,EAAE;IACtB,IAAI,CAACjD,MAAM,CAACiC,QAAQ,CAAC,CAAC,iBAAiB,CAAC,CAAC;EAC3C;EAEAiB,OAAOA,CAAA;IACL,IAAI,CAAC5C,iBAAiB,EAAE;EAC1B;CACD;AA1KYR,sBAAsB,GAAAqD,UAAA,EAPlC1D,SAAS,CAAC;EACT2D,QAAQ,EAAE,oBAAoB;EAC9BC,UAAU,EAAE,IAAI;EAChBC,OAAO,EAAE,CAAC5D,YAAY,EAAEC,YAAY,EAAEC,WAAW,CAAC;EAClD2D,WAAW,EAAE,iCAAiC;EAC9CC,SAAS,EAAE,CAAC,iCAAiC;CAC9C,CAAC,C,EACW1D,sBAAsB,CA0KlC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}