{"ast": null, "code": "import _asyncToGenerator from \"E:/Fashion/DFashionFrontend/frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\nimport { IonicModule } from '@ionic/angular';\nimport { Subscription } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"@ionic/angular\";\nfunction TrendingBannersComponent_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 8);\n    i0.ɵɵelement(1, \"ion-spinner\", 9);\n    i0.ɵɵelementStart(2, \"p\");\n    i0.ɵɵtext(3, \"Loading trending banners...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction TrendingBannersComponent_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 10);\n    i0.ɵɵelement(1, \"ion-icon\", 11);\n    i0.ɵɵelementStart(2, \"p\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"button\", 12);\n    i0.ɵɵlistener(\"click\", function TrendingBannersComponent_div_9_Template_button_click_4_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onRetry());\n    });\n    i0.ɵɵtext(5, \"Try Again\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r1.error);\n  }\n}\nfunction TrendingBannersComponent_div_10_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 17);\n    i0.ɵɵlistener(\"click\", function TrendingBannersComponent_div_10_div_1_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onBannerClick(ctx_r1.featuredBanner));\n    });\n    i0.ɵɵelementStart(1, \"div\", 18);\n    i0.ɵɵelement(2, \"img\", 19);\n    i0.ɵɵelementStart(3, \"div\", 20)(4, \"div\", 21)(5, \"h4\", 22);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"p\", 23);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"div\", 24)(10, \"span\", 25);\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(12, \"ion-icon\", 26);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(13, \"div\", 27);\n    i0.ɵɵelement(14, \"ion-icon\", 28);\n    i0.ɵɵtext(15, \" Hot \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"src\", ctx_r1.featuredBanner.image, i0.ɵɵsanitizeUrl)(\"alt\", ctx_r1.featuredBanner.title);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r1.featuredBanner.title);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.featuredBanner.description);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r1.featuredBanner.ctaText);\n  }\n}\nfunction TrendingBannersComponent_div_10_div_2_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 31);\n    i0.ɵɵlistener(\"click\", function TrendingBannersComponent_div_10_div_2_div_1_Template_div_click_0_listener() {\n      const banner_r5 = i0.ɵɵrestoreView(_r4).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.onBannerClick(banner_r5));\n    });\n    i0.ɵɵelementStart(1, \"div\", 18);\n    i0.ɵɵelement(2, \"img\", 19);\n    i0.ɵɵelementStart(3, \"div\", 20)(4, \"div\", 21)(5, \"h5\", 22);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"p\", 23);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(9, \"div\", 32);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const banner_r5 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"src\", banner_r5.image, i0.ɵɵsanitizeUrl)(\"alt\", banner_r5.title);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(banner_r5.title);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(banner_r5.description);\n    i0.ɵɵadvance();\n    i0.ɵɵstyleProp(\"background-color\", banner_r5.tagColor);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", banner_r5.tag, \" \");\n  }\n}\nfunction TrendingBannersComponent_div_10_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 29);\n    i0.ɵɵtemplate(1, TrendingBannersComponent_div_10_div_2_div_1_Template, 11, 7, \"div\", 30);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.secondaryBanners)(\"ngForTrackBy\", ctx_r1.trackByBannerId);\n  }\n}\nfunction TrendingBannersComponent_div_10_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 33)(1, \"div\", 34);\n    i0.ɵɵelement(2, \"ion-icon\", 35);\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"div\", 34);\n    i0.ɵɵelement(6, \"ion-icon\", 36);\n    i0.ɵɵelementStart(7, \"span\");\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"div\", 34);\n    i0.ɵɵelement(10, \"ion-icon\", 37);\n    i0.ɵɵelementStart(11, \"span\");\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r1.formatCount(ctx_r1.bannerStats.totalViews), \" views\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r1.formatCount(ctx_r1.bannerStats.totalLikes), \" likes\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r1.formatCount(ctx_r1.bannerStats.totalShares), \" shares\");\n  }\n}\nfunction TrendingBannersComponent_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 13);\n    i0.ɵɵtemplate(1, TrendingBannersComponent_div_10_div_1_Template, 16, 5, \"div\", 14)(2, TrendingBannersComponent_div_10_div_2_Template, 2, 2, \"div\", 15)(3, TrendingBannersComponent_div_10_div_3_Template, 13, 3, \"div\", 16);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.featuredBanner);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.secondaryBanners.length > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.bannerStats);\n  }\n}\nexport class TrendingBannersComponent {\n  constructor(router) {\n    this.router = router;\n    this.featuredBanner = null;\n    this.secondaryBanners = [];\n    this.bannerStats = null;\n    this.isLoading = true;\n    this.error = null;\n    this.subscription = new Subscription();\n  }\n  ngOnInit() {\n    this.loadTrendingBanners();\n  }\n  ngOnDestroy() {\n    this.subscription.unsubscribe();\n  }\n  loadTrendingBanners() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      try {\n        _this.isLoading = true;\n        _this.error = null;\n        // Mock data for now - replace with actual service call\n        yield _this.loadMockBanners();\n      } catch (error) {\n        console.error('Error loading trending banners:', error);\n        _this.error = 'Failed to load trending banners';\n        _this.isLoading = false;\n      }\n    })();\n  }\n  loadMockBanners() {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      // Simulate API call delay\n      yield new Promise(resolve => setTimeout(resolve, 1000));\n      // Mock featured banner\n      _this2.featuredBanner = {\n        _id: 'banner1',\n        title: 'Summer Collection 2024',\n        description: 'Discover the hottest trends for this summer season',\n        image: 'https://images.unsplash.com/photo-1441986300917-64674bd600d8?w=800&h=400&fit=crop',\n        ctaText: 'Shop Now',\n        link: '/collections/summer-2024',\n        tag: 'NEW',\n        tagColor: '#ff6b6b',\n        priority: 1,\n        isActive: true,\n        analytics: {\n          views: 15420,\n          clicks: 2340,\n          likes: 890,\n          shares: 156\n        },\n        createdAt: new Date()\n      };\n      // Mock secondary banners\n      _this2.secondaryBanners = [{\n        _id: 'banner2',\n        title: 'Flash Sale',\n        description: 'Up to 70% off on selected items',\n        image: 'https://images.unsplash.com/photo-1607083206869-4c7672e72a8a?w=400&h=300&fit=crop',\n        ctaText: 'Shop Sale',\n        link: '/sale',\n        tag: 'SALE',\n        tagColor: '#feca57',\n        priority: 2,\n        isActive: true,\n        analytics: {\n          views: 8920,\n          clicks: 1560,\n          likes: 445,\n          shares: 89\n        },\n        createdAt: new Date()\n      }, {\n        _id: 'banner3',\n        title: 'Designer Collaboration',\n        description: 'Exclusive pieces from top designers',\n        image: 'https://images.unsplash.com/photo-1445205170230-053b83016050?w=400&h=300&fit=crop',\n        ctaText: 'Explore',\n        link: '/designer-collab',\n        tag: 'EXCLUSIVE',\n        tagColor: '#6c5ce7',\n        priority: 3,\n        isActive: true,\n        analytics: {\n          views: 6780,\n          clicks: 890,\n          likes: 234,\n          shares: 67\n        },\n        createdAt: new Date()\n      }];\n      // Calculate banner stats\n      _this2.bannerStats = {\n        totalViews: _this2.featuredBanner.analytics.views + _this2.secondaryBanners.reduce((sum, banner) => sum + banner.analytics.views, 0),\n        totalLikes: _this2.featuredBanner.analytics.likes + _this2.secondaryBanners.reduce((sum, banner) => sum + banner.analytics.likes, 0),\n        totalShares: _this2.featuredBanner.analytics.shares + _this2.secondaryBanners.reduce((sum, banner) => sum + banner.analytics.shares, 0),\n        totalClicks: _this2.featuredBanner.analytics.clicks + _this2.secondaryBanners.reduce((sum, banner) => sum + banner.analytics.clicks, 0)\n      };\n      _this2.isLoading = false;\n    })();\n  }\n  onBannerClick(banner) {\n    // Track banner click analytics\n    banner.analytics.clicks++;\n    // Navigate to banner link\n    if (banner.link.startsWith('http')) {\n      window.open(banner.link, '_blank');\n    } else {\n      this.router.navigate([banner.link]);\n    }\n  }\n  onSeeAll(event) {\n    event.preventDefault();\n    this.router.navigate(['/trending/banners']);\n  }\n  onRetry() {\n    this.loadTrendingBanners();\n  }\n  trackByBannerId(index, banner) {\n    return banner._id;\n  }\n  formatCount(count) {\n    if (count >= 1000000) {\n      return (count / 1000000).toFixed(1) + 'M';\n    } else if (count >= 1000) {\n      return (count / 1000).toFixed(1) + 'K';\n    }\n    return count.toString();\n  }\n  static {\n    this.ɵfac = function TrendingBannersComponent_Factory(t) {\n      return new (t || TrendingBannersComponent)(i0.ɵɵdirectiveInject(i1.Router));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: TrendingBannersComponent,\n      selectors: [[\"app-trending-banners\"]],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 11,\n      vars: 3,\n      consts: [[1, \"component-container\"], [1, \"component-header\"], [\"name\", \"megaphone-outline\"], [\"href\", \"#\", 1, \"see-all\", 3, \"click\"], [1, \"component-content\"], [\"class\", \"loading-container\", 4, \"ngIf\"], [\"class\", \"error-container\", 4, \"ngIf\"], [\"class\", \"banners-content\", 4, \"ngIf\"], [1, \"loading-container\"], [\"name\", \"crescent\"], [1, \"error-container\"], [\"name\", \"alert-circle-outline\"], [1, \"retry-btn\", 3, \"click\"], [1, \"banners-content\"], [\"class\", \"featured-banner\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"secondary-banners\", 4, \"ngIf\"], [\"class\", \"banner-stats\", 4, \"ngIf\"], [1, \"featured-banner\", 3, \"click\"], [1, \"banner-image-container\"], [1, \"banner-image\", 3, \"src\", \"alt\"], [1, \"banner-overlay\"], [1, \"banner-content\"], [1, \"banner-title\"], [1, \"banner-description\"], [1, \"banner-cta\"], [1, \"cta-text\"], [\"name\", \"arrow-forward\"], [1, \"trending-badge\"], [\"name\", \"flame\"], [1, \"secondary-banners\"], [\"class\", \"secondary-banner\", 3, \"click\", 4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [1, \"secondary-banner\", 3, \"click\"], [1, \"banner-tag\"], [1, \"banner-stats\"], [1, \"stat-item\"], [\"name\", \"eye-outline\"], [\"name\", \"heart-outline\"], [\"name\", \"share-outline\"]],\n      template: function TrendingBannersComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"h3\");\n          i0.ɵɵelement(3, \"ion-icon\", 2);\n          i0.ɵɵtext(4, \" Trending Banners \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(5, \"a\", 3);\n          i0.ɵɵlistener(\"click\", function TrendingBannersComponent_Template_a_click_5_listener($event) {\n            return ctx.onSeeAll($event);\n          });\n          i0.ɵɵtext(6, \"See All\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(7, \"div\", 4);\n          i0.ɵɵtemplate(8, TrendingBannersComponent_div_8_Template, 4, 0, \"div\", 5)(9, TrendingBannersComponent_div_9_Template, 6, 1, \"div\", 6)(10, TrendingBannersComponent_div_10_Template, 4, 3, \"div\", 7);\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.error && !ctx.isLoading);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading && !ctx.error);\n        }\n      },\n      dependencies: [CommonModule, i2.NgForOf, i2.NgIf, RouterModule, IonicModule, i3.IonIcon, i3.IonSpinner],\n      styles: [\".loading-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n  padding: 40px 20px;\\n  color: #666;\\n}\\n.loading-container[_ngcontent-%COMP%]   ion-spinner[_ngcontent-%COMP%] {\\n  margin-bottom: 16px;\\n}\\n.loading-container[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 14px;\\n}\\n\\n.error-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n  padding: 40px 20px;\\n  color: #e74c3c;\\n  text-align: center;\\n}\\n.error-container[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n  font-size: 48px;\\n  margin-bottom: 16px;\\n  color: #e74c3c;\\n}\\n.error-container[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0 0 16px 0;\\n  font-size: 14px;\\n}\\n.error-container[_ngcontent-%COMP%]   .retry-btn[_ngcontent-%COMP%] {\\n  background: #e74c3c;\\n  color: white;\\n  border: none;\\n  padding: 8px 16px;\\n  border-radius: 6px;\\n  font-size: 12px;\\n  cursor: pointer;\\n  transition: background 0.3s ease;\\n}\\n.error-container[_ngcontent-%COMP%]   .retry-btn[_ngcontent-%COMP%]:hover {\\n  background: #c0392b;\\n}\\n\\n.banners-content[_ngcontent-%COMP%]   .featured-banner[_ngcontent-%COMP%] {\\n  margin-bottom: 20px;\\n  cursor: pointer;\\n  border-radius: 12px;\\n  overflow: hidden;\\n  transition: transform 0.3s ease, box-shadow 0.3s ease;\\n}\\n.banners-content[_ngcontent-%COMP%]   .featured-banner[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-2px);\\n  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);\\n}\\n.banners-content[_ngcontent-%COMP%]   .featured-banner[_ngcontent-%COMP%]   .banner-image-container[_ngcontent-%COMP%] {\\n  position: relative;\\n  width: 100%;\\n  height: 200px;\\n  overflow: hidden;\\n}\\n.banners-content[_ngcontent-%COMP%]   .featured-banner[_ngcontent-%COMP%]   .banner-image-container[_ngcontent-%COMP%]   .banner-image[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 100%;\\n  object-fit: cover;\\n  transition: transform 0.3s ease;\\n}\\n.banners-content[_ngcontent-%COMP%]   .featured-banner[_ngcontent-%COMP%]   .banner-image-container[_ngcontent-%COMP%]:hover   .banner-image[_ngcontent-%COMP%] {\\n  transform: scale(1.05);\\n}\\n.banners-content[_ngcontent-%COMP%]   .featured-banner[_ngcontent-%COMP%]   .banner-image-container[_ngcontent-%COMP%]   .banner-overlay[_ngcontent-%COMP%] {\\n  position: absolute;\\n  bottom: 0;\\n  left: 0;\\n  right: 0;\\n  background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));\\n  padding: 20px;\\n  color: white;\\n}\\n.banners-content[_ngcontent-%COMP%]   .featured-banner[_ngcontent-%COMP%]   .banner-image-container[_ngcontent-%COMP%]   .banner-overlay[_ngcontent-%COMP%]   .banner-content[_ngcontent-%COMP%]   .banner-title[_ngcontent-%COMP%] {\\n  font-size: 20px;\\n  font-weight: 700;\\n  margin: 0 0 8px 0;\\n  color: white;\\n}\\n.banners-content[_ngcontent-%COMP%]   .featured-banner[_ngcontent-%COMP%]   .banner-image-container[_ngcontent-%COMP%]   .banner-overlay[_ngcontent-%COMP%]   .banner-content[_ngcontent-%COMP%]   .banner-description[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  margin: 0 0 12px 0;\\n  opacity: 0.9;\\n  line-height: 1.4;\\n}\\n.banners-content[_ngcontent-%COMP%]   .featured-banner[_ngcontent-%COMP%]   .banner-image-container[_ngcontent-%COMP%]   .banner-overlay[_ngcontent-%COMP%]   .banner-content[_ngcontent-%COMP%]   .banner-cta[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  font-weight: 600;\\n  font-size: 14px;\\n}\\n.banners-content[_ngcontent-%COMP%]   .featured-banner[_ngcontent-%COMP%]   .banner-image-container[_ngcontent-%COMP%]   .banner-overlay[_ngcontent-%COMP%]   .banner-content[_ngcontent-%COMP%]   .banner-cta[_ngcontent-%COMP%]   .cta-text[_ngcontent-%COMP%] {\\n  color: #ffd700;\\n}\\n.banners-content[_ngcontent-%COMP%]   .featured-banner[_ngcontent-%COMP%]   .banner-image-container[_ngcontent-%COMP%]   .banner-overlay[_ngcontent-%COMP%]   .banner-content[_ngcontent-%COMP%]   .banner-cta[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  color: #ffd700;\\n  transition: transform 0.3s ease;\\n}\\n.banners-content[_ngcontent-%COMP%]   .featured-banner[_ngcontent-%COMP%]   .banner-image-container[_ngcontent-%COMP%]   .banner-overlay[_ngcontent-%COMP%]   .banner-content[_ngcontent-%COMP%]:hover   .banner-cta[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n  transform: translateX(3px);\\n}\\n.banners-content[_ngcontent-%COMP%]   .featured-banner[_ngcontent-%COMP%]   .banner-image-container[_ngcontent-%COMP%]   .trending-badge[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 12px;\\n  right: 12px;\\n  background: linear-gradient(135deg, #ff6b6b, #ee5a24);\\n  color: white;\\n  padding: 6px 12px;\\n  border-radius: 20px;\\n  font-size: 12px;\\n  font-weight: 600;\\n  display: flex;\\n  align-items: center;\\n  gap: 4px;\\n  box-shadow: 0 2px 8px rgba(255, 107, 107, 0.3);\\n}\\n.banners-content[_ngcontent-%COMP%]   .featured-banner[_ngcontent-%COMP%]   .banner-image-container[_ngcontent-%COMP%]   .trending-badge[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  animation: _ngcontent-%COMP%_flicker 1.5s infinite alternate;\\n}\\n.banners-content[_ngcontent-%COMP%]   .secondary-banners[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\\n  gap: 16px;\\n  margin-bottom: 20px;\\n}\\n.banners-content[_ngcontent-%COMP%]   .secondary-banners[_ngcontent-%COMP%]   .secondary-banner[_ngcontent-%COMP%] {\\n  cursor: pointer;\\n  border-radius: 8px;\\n  overflow: hidden;\\n  transition: transform 0.3s ease, box-shadow 0.3s ease;\\n}\\n.banners-content[_ngcontent-%COMP%]   .secondary-banners[_ngcontent-%COMP%]   .secondary-banner[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-2px);\\n  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.1);\\n}\\n.banners-content[_ngcontent-%COMP%]   .secondary-banners[_ngcontent-%COMP%]   .secondary-banner[_ngcontent-%COMP%]   .banner-image-container[_ngcontent-%COMP%] {\\n  position: relative;\\n  width: 100%;\\n  height: 150px;\\n  overflow: hidden;\\n}\\n.banners-content[_ngcontent-%COMP%]   .secondary-banners[_ngcontent-%COMP%]   .secondary-banner[_ngcontent-%COMP%]   .banner-image-container[_ngcontent-%COMP%]   .banner-image[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 100%;\\n  object-fit: cover;\\n  transition: transform 0.3s ease;\\n}\\n.banners-content[_ngcontent-%COMP%]   .secondary-banners[_ngcontent-%COMP%]   .secondary-banner[_ngcontent-%COMP%]   .banner-image-container[_ngcontent-%COMP%]:hover   .banner-image[_ngcontent-%COMP%] {\\n  transform: scale(1.05);\\n}\\n.banners-content[_ngcontent-%COMP%]   .secondary-banners[_ngcontent-%COMP%]   .secondary-banner[_ngcontent-%COMP%]   .banner-image-container[_ngcontent-%COMP%]   .banner-overlay[_ngcontent-%COMP%] {\\n  position: absolute;\\n  bottom: 0;\\n  left: 0;\\n  right: 0;\\n  background: linear-gradient(transparent, rgba(0, 0, 0, 0.7));\\n  padding: 16px;\\n  color: white;\\n}\\n.banners-content[_ngcontent-%COMP%]   .secondary-banners[_ngcontent-%COMP%]   .secondary-banner[_ngcontent-%COMP%]   .banner-image-container[_ngcontent-%COMP%]   .banner-overlay[_ngcontent-%COMP%]   .banner-content[_ngcontent-%COMP%]   .banner-title[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  font-weight: 600;\\n  margin: 0 0 4px 0;\\n  color: white;\\n}\\n.banners-content[_ngcontent-%COMP%]   .secondary-banners[_ngcontent-%COMP%]   .secondary-banner[_ngcontent-%COMP%]   .banner-image-container[_ngcontent-%COMP%]   .banner-overlay[_ngcontent-%COMP%]   .banner-content[_ngcontent-%COMP%]   .banner-description[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  margin: 0;\\n  opacity: 0.9;\\n  line-height: 1.3;\\n}\\n.banners-content[_ngcontent-%COMP%]   .secondary-banners[_ngcontent-%COMP%]   .secondary-banner[_ngcontent-%COMP%]   .banner-image-container[_ngcontent-%COMP%]   .banner-tag[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 8px;\\n  left: 8px;\\n  color: white;\\n  padding: 4px 8px;\\n  border-radius: 12px;\\n  font-size: 10px;\\n  font-weight: 600;\\n  text-transform: uppercase;\\n}\\n.banners-content[_ngcontent-%COMP%]   .banner-stats[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-around;\\n  padding: 16px;\\n  background: #f8f9fa;\\n  border-radius: 8px;\\n  margin-top: 16px;\\n}\\n.banners-content[_ngcontent-%COMP%]   .banner-stats[_ngcontent-%COMP%]   .stat-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 6px;\\n  font-size: 12px;\\n  color: #666;\\n}\\n.banners-content[_ngcontent-%COMP%]   .banner-stats[_ngcontent-%COMP%]   .stat-item[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  color: #6c5ce7;\\n}\\n.banners-content[_ngcontent-%COMP%]   .banner-stats[_ngcontent-%COMP%]   .stat-item[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n}\\n\\n@keyframes _ngcontent-%COMP%_flicker {\\n  0% {\\n    opacity: 1;\\n  }\\n  100% {\\n    opacity: 0.7;\\n  }\\n}\\n@media (max-width: 768px) {\\n  .banners-content[_ngcontent-%COMP%]   .featured-banner[_ngcontent-%COMP%]   .banner-image-container[_ngcontent-%COMP%] {\\n    height: 160px;\\n  }\\n  .banners-content[_ngcontent-%COMP%]   .featured-banner[_ngcontent-%COMP%]   .banner-image-container[_ngcontent-%COMP%]   .banner-overlay[_ngcontent-%COMP%] {\\n    padding: 16px;\\n  }\\n  .banners-content[_ngcontent-%COMP%]   .featured-banner[_ngcontent-%COMP%]   .banner-image-container[_ngcontent-%COMP%]   .banner-overlay[_ngcontent-%COMP%]   .banner-content[_ngcontent-%COMP%]   .banner-title[_ngcontent-%COMP%] {\\n    font-size: 18px;\\n  }\\n  .banners-content[_ngcontent-%COMP%]   .featured-banner[_ngcontent-%COMP%]   .banner-image-container[_ngcontent-%COMP%]   .banner-overlay[_ngcontent-%COMP%]   .banner-content[_ngcontent-%COMP%]   .banner-description[_ngcontent-%COMP%] {\\n    font-size: 13px;\\n  }\\n  .banners-content[_ngcontent-%COMP%]   .featured-banner[_ngcontent-%COMP%]   .banner-image-container[_ngcontent-%COMP%]   .banner-overlay[_ngcontent-%COMP%]   .banner-content[_ngcontent-%COMP%]   .banner-cta[_ngcontent-%COMP%] {\\n    font-size: 13px;\\n  }\\n  .banners-content[_ngcontent-%COMP%]   .secondary-banners[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n    gap: 12px;\\n  }\\n  .banners-content[_ngcontent-%COMP%]   .secondary-banners[_ngcontent-%COMP%]   .secondary-banner[_ngcontent-%COMP%]   .banner-image-container[_ngcontent-%COMP%] {\\n    height: 120px;\\n  }\\n  .banners-content[_ngcontent-%COMP%]   .secondary-banners[_ngcontent-%COMP%]   .secondary-banner[_ngcontent-%COMP%]   .banner-image-container[_ngcontent-%COMP%]   .banner-overlay[_ngcontent-%COMP%] {\\n    padding: 12px;\\n  }\\n  .banners-content[_ngcontent-%COMP%]   .secondary-banners[_ngcontent-%COMP%]   .secondary-banner[_ngcontent-%COMP%]   .banner-image-container[_ngcontent-%COMP%]   .banner-overlay[_ngcontent-%COMP%]   .banner-content[_ngcontent-%COMP%]   .banner-title[_ngcontent-%COMP%] {\\n    font-size: 14px;\\n  }\\n  .banners-content[_ngcontent-%COMP%]   .secondary-banners[_ngcontent-%COMP%]   .secondary-banner[_ngcontent-%COMP%]   .banner-image-container[_ngcontent-%COMP%]   .banner-overlay[_ngcontent-%COMP%]   .banner-content[_ngcontent-%COMP%]   .banner-description[_ngcontent-%COMP%] {\\n    font-size: 11px;\\n  }\\n  .banners-content[_ngcontent-%COMP%]   .banner-stats[_ngcontent-%COMP%] {\\n    padding: 12px;\\n  }\\n  .banners-content[_ngcontent-%COMP%]   .banner-stats[_ngcontent-%COMP%]   .stat-item[_ngcontent-%COMP%] {\\n    font-size: 11px;\\n  }\\n  .banners-content[_ngcontent-%COMP%]   .banner-stats[_ngcontent-%COMP%]   .stat-item[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n    font-size: 14px;\\n  }\\n}\\n@media (max-width: 425px) {\\n  .banners-content[_ngcontent-%COMP%]   .featured-banner[_ngcontent-%COMP%]   .banner-image-container[_ngcontent-%COMP%] {\\n    height: 140px;\\n  }\\n  .banners-content[_ngcontent-%COMP%]   .featured-banner[_ngcontent-%COMP%]   .banner-image-container[_ngcontent-%COMP%]   .banner-overlay[_ngcontent-%COMP%] {\\n    padding: 12px;\\n  }\\n  .banners-content[_ngcontent-%COMP%]   .featured-banner[_ngcontent-%COMP%]   .banner-image-container[_ngcontent-%COMP%]   .banner-overlay[_ngcontent-%COMP%]   .banner-content[_ngcontent-%COMP%]   .banner-title[_ngcontent-%COMP%] {\\n    font-size: 16px;\\n  }\\n  .banners-content[_ngcontent-%COMP%]   .featured-banner[_ngcontent-%COMP%]   .banner-image-container[_ngcontent-%COMP%]   .banner-overlay[_ngcontent-%COMP%]   .banner-content[_ngcontent-%COMP%]   .banner-description[_ngcontent-%COMP%] {\\n    font-size: 12px;\\n  }\\n  .banners-content[_ngcontent-%COMP%]   .secondary-banners[_ngcontent-%COMP%]   .secondary-banner[_ngcontent-%COMP%]   .banner-image-container[_ngcontent-%COMP%] {\\n    height: 100px;\\n  }\\n  .banners-content[_ngcontent-%COMP%]   .secondary-banners[_ngcontent-%COMP%]   .secondary-banner[_ngcontent-%COMP%]   .banner-image-container[_ngcontent-%COMP%]   .banner-overlay[_ngcontent-%COMP%] {\\n    padding: 10px;\\n  }\\n  .banners-content[_ngcontent-%COMP%]   .secondary-banners[_ngcontent-%COMP%]   .secondary-banner[_ngcontent-%COMP%]   .banner-image-container[_ngcontent-%COMP%]   .banner-overlay[_ngcontent-%COMP%]   .banner-content[_ngcontent-%COMP%]   .banner-title[_ngcontent-%COMP%] {\\n    font-size: 13px;\\n  }\\n  .banners-content[_ngcontent-%COMP%]   .secondary-banners[_ngcontent-%COMP%]   .secondary-banner[_ngcontent-%COMP%]   .banner-image-container[_ngcontent-%COMP%]   .banner-overlay[_ngcontent-%COMP%]   .banner-content[_ngcontent-%COMP%]   .banner-description[_ngcontent-%COMP%] {\\n    font-size: 10px;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "RouterModule", "IonicModule", "Subscription", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵtext", "ɵɵelementEnd", "ɵɵlistener", "TrendingBannersComponent_div_9_Template_button_click_4_listener", "ɵɵrestoreView", "_r1", "ctx_r1", "ɵɵnextContext", "ɵɵresetView", "onRetry", "ɵɵadvance", "ɵɵtextInterpolate", "error", "TrendingBannersComponent_div_10_div_1_Template_div_click_0_listener", "_r3", "onBannerClick", "featuredB<PERSON>r", "ɵɵproperty", "image", "ɵɵsanitizeUrl", "title", "description", "ctaText", "TrendingBannersComponent_div_10_div_2_div_1_Template_div_click_0_listener", "banner_r5", "_r4", "$implicit", "ɵɵstyleProp", "tagColor", "ɵɵtextInterpolate1", "tag", "ɵɵtemplate", "TrendingBannersComponent_div_10_div_2_div_1_Template", "secondaryBanners", "trackByBannerId", "formatCount", "bannerStats", "totalViews", "totalLikes", "totalShares", "TrendingBannersComponent_div_10_div_1_Template", "TrendingBannersComponent_div_10_div_2_Template", "TrendingBannersComponent_div_10_div_3_Template", "length", "TrendingBannersComponent", "constructor", "router", "isLoading", "subscription", "ngOnInit", "loadTrendingBanners", "ngOnDestroy", "unsubscribe", "_this", "_asyncToGenerator", "loadMockBanners", "console", "_this2", "Promise", "resolve", "setTimeout", "_id", "link", "priority", "isActive", "analytics", "views", "clicks", "likes", "shares", "createdAt", "Date", "reduce", "sum", "banner", "totalClicks", "startsWith", "window", "open", "navigate", "onSeeAll", "event", "preventDefault", "index", "count", "toFixed", "toString", "ɵɵdirectiveInject", "i1", "Router", "selectors", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "TrendingBannersComponent_Template", "rf", "ctx", "TrendingBannersComponent_Template_a_click_5_listener", "$event", "TrendingBannersComponent_div_8_Template", "TrendingBannersComponent_div_9_Template", "TrendingBannersComponent_div_10_Template", "i2", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "i3", "IonIcon", "Ion<PERSON><PERSON><PERSON>", "styles"], "sources": ["E:\\Fashion\\DFashionFrontend\\frontend\\src\\app\\features\\home\\components\\trending-now\\trending-banners\\trending-banners.component.ts", "E:\\Fashion\\DFashionFrontend\\frontend\\src\\app\\features\\home\\components\\trending-now\\trending-banners\\trending-banners.component.html"], "sourcesContent": ["import { Component, OnInit, OnDestroy } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\nimport { IonicModule } from '@ionic/angular';\nimport { Router } from '@angular/router';\nimport { Subscription } from 'rxjs';\n\ninterface TrendingBanner {\n  _id: string;\n  title: string;\n  description: string;\n  image: string;\n  ctaText: string;\n  link: string;\n  tag: string;\n  tagColor: string;\n  priority: number;\n  isActive: boolean;\n  analytics: {\n    views: number;\n    clicks: number;\n    likes: number;\n    shares: number;\n  };\n  createdAt: Date;\n}\n\ninterface BannerStats {\n  totalViews: number;\n  totalLikes: number;\n  totalShares: number;\n  totalClicks: number;\n}\n\n@Component({\n  selector: 'app-trending-banners',\n  standalone: true,\n  imports: [\n    CommonModule,\n    RouterModule,\n    IonicModule\n  ],\n  templateUrl: './trending-banners.component.html',\n  styleUrls: ['./trending-banners.component.scss']\n})\nexport class TrendingBannersComponent implements OnInit, OnDestroy {\n  featuredBanner: TrendingBanner | null = null;\n  secondaryBanners: TrendingBanner[] = [];\n  bannerStats: BannerStats | null = null;\n  isLoading = true;\n  error: string | null = null;\n  private subscription: Subscription = new Subscription();\n\n  constructor(private router: Router) {}\n\n  ngOnInit() {\n    this.loadTrendingBanners();\n  }\n\n  ngOnDestroy() {\n    this.subscription.unsubscribe();\n  }\n\n  private async loadTrendingBanners() {\n    try {\n      this.isLoading = true;\n      this.error = null;\n      \n      // Mock data for now - replace with actual service call\n      await this.loadMockBanners();\n      \n    } catch (error) {\n      console.error('Error loading trending banners:', error);\n      this.error = 'Failed to load trending banners';\n      this.isLoading = false;\n    }\n  }\n\n  private async loadMockBanners() {\n    // Simulate API call delay\n    await new Promise(resolve => setTimeout(resolve, 1000));\n\n    // Mock featured banner\n    this.featuredBanner = {\n      _id: 'banner1',\n      title: 'Summer Collection 2024',\n      description: 'Discover the hottest trends for this summer season',\n      image: 'https://images.unsplash.com/photo-1441986300917-64674bd600d8?w=800&h=400&fit=crop',\n      ctaText: 'Shop Now',\n      link: '/collections/summer-2024',\n      tag: 'NEW',\n      tagColor: '#ff6b6b',\n      priority: 1,\n      isActive: true,\n      analytics: {\n        views: 15420,\n        clicks: 2340,\n        likes: 890,\n        shares: 156\n      },\n      createdAt: new Date()\n    };\n\n    // Mock secondary banners\n    this.secondaryBanners = [\n      {\n        _id: 'banner2',\n        title: 'Flash Sale',\n        description: 'Up to 70% off on selected items',\n        image: 'https://images.unsplash.com/photo-1607083206869-4c7672e72a8a?w=400&h=300&fit=crop',\n        ctaText: 'Shop Sale',\n        link: '/sale',\n        tag: 'SALE',\n        tagColor: '#feca57',\n        priority: 2,\n        isActive: true,\n        analytics: {\n          views: 8920,\n          clicks: 1560,\n          likes: 445,\n          shares: 89\n        },\n        createdAt: new Date()\n      },\n      {\n        _id: 'banner3',\n        title: 'Designer Collaboration',\n        description: 'Exclusive pieces from top designers',\n        image: 'https://images.unsplash.com/photo-1445205170230-053b83016050?w=400&h=300&fit=crop',\n        ctaText: 'Explore',\n        link: '/designer-collab',\n        tag: 'EXCLUSIVE',\n        tagColor: '#6c5ce7',\n        priority: 3,\n        isActive: true,\n        analytics: {\n          views: 6780,\n          clicks: 890,\n          likes: 234,\n          shares: 67\n        },\n        createdAt: new Date()\n      }\n    ];\n\n    // Calculate banner stats\n    this.bannerStats = {\n      totalViews: this.featuredBanner.analytics.views + \n                  this.secondaryBanners.reduce((sum, banner) => sum + banner.analytics.views, 0),\n      totalLikes: this.featuredBanner.analytics.likes + \n                  this.secondaryBanners.reduce((sum, banner) => sum + banner.analytics.likes, 0),\n      totalShares: this.featuredBanner.analytics.shares + \n                   this.secondaryBanners.reduce((sum, banner) => sum + banner.analytics.shares, 0),\n      totalClicks: this.featuredBanner.analytics.clicks + \n                   this.secondaryBanners.reduce((sum, banner) => sum + banner.analytics.clicks, 0)\n    };\n\n    this.isLoading = false;\n  }\n\n  onBannerClick(banner: TrendingBanner) {\n    // Track banner click analytics\n    banner.analytics.clicks++;\n    \n    // Navigate to banner link\n    if (banner.link.startsWith('http')) {\n      window.open(banner.link, '_blank');\n    } else {\n      this.router.navigate([banner.link]);\n    }\n  }\n\n  onSeeAll(event: Event) {\n    event.preventDefault();\n    this.router.navigate(['/trending/banners']);\n  }\n\n  onRetry() {\n    this.loadTrendingBanners();\n  }\n\n  trackByBannerId(index: number, banner: TrendingBanner): string {\n    return banner._id;\n  }\n\n  formatCount(count: number): string {\n    if (count >= 1000000) {\n      return (count / 1000000).toFixed(1) + 'M';\n    } else if (count >= 1000) {\n      return (count / 1000).toFixed(1) + 'K';\n    }\n    return count.toString();\n  }\n}\n", "<div class=\"component-container\">\n  <!-- Component Header -->\n  <div class=\"component-header\">\n    <h3>\n      <ion-icon name=\"megaphone-outline\"></ion-icon>\n      Trending Banners\n    </h3>\n    <a href=\"#\" class=\"see-all\" (click)=\"onSeeAll($event)\">See All</a>\n  </div>\n\n  <!-- Component Content -->\n  <div class=\"component-content\">\n    <!-- Loading State -->\n    <div *ngIf=\"isLoading\" class=\"loading-container\">\n      <ion-spinner name=\"crescent\"></ion-spinner>\n      <p>Loading trending banners...</p>\n    </div>\n\n    <!-- Error State -->\n    <div *ngIf=\"error && !isLoading\" class=\"error-container\">\n      <ion-icon name=\"alert-circle-outline\"></ion-icon>\n      <p>{{ error }}</p>\n      <button class=\"retry-btn\" (click)=\"onRetry()\">Try Again</button>\n    </div>\n\n    <!-- Banners Content -->\n    <div *ngIf=\"!isLoading && !error\" class=\"banners-content\">\n      <!-- Main Featured Banner -->\n      <div class=\"featured-banner\" *ngIf=\"featuredBanner\" (click)=\"onBannerClick(featuredBanner)\">\n        <div class=\"banner-image-container\">\n          <img [src]=\"featuredBanner.image\" [alt]=\"featuredBanner.title\" class=\"banner-image\">\n          <div class=\"banner-overlay\">\n            <div class=\"banner-content\">\n              <h4 class=\"banner-title\">{{ featuredBanner.title }}</h4>\n              <p class=\"banner-description\">{{ featuredBanner.description }}</p>\n              <div class=\"banner-cta\">\n                <span class=\"cta-text\">{{ featuredBanner.ctaText }}</span>\n                <ion-icon name=\"arrow-forward\"></ion-icon>\n              </div>\n            </div>\n          </div>\n          <div class=\"trending-badge\">\n            <ion-icon name=\"flame\"></ion-icon>\n            Hot\n          </div>\n        </div>\n      </div>\n\n      <!-- Secondary Banners Grid -->\n      <div class=\"secondary-banners\" *ngIf=\"secondaryBanners.length > 0\">\n        <div \n          *ngFor=\"let banner of secondaryBanners; trackBy: trackByBannerId\" \n          class=\"secondary-banner\"\n          (click)=\"onBannerClick(banner)\"\n        >\n          <div class=\"banner-image-container\">\n            <img [src]=\"banner.image\" [alt]=\"banner.title\" class=\"banner-image\">\n            <div class=\"banner-overlay\">\n              <div class=\"banner-content\">\n                <h5 class=\"banner-title\">{{ banner.title }}</h5>\n                <p class=\"banner-description\">{{ banner.description }}</p>\n              </div>\n            </div>\n            <div class=\"banner-tag\" [style.background-color]=\"banner.tagColor\">\n              {{ banner.tag }}\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <!-- Banner Stats -->\n      <div class=\"banner-stats\" *ngIf=\"bannerStats\">\n        <div class=\"stat-item\">\n          <ion-icon name=\"eye-outline\"></ion-icon>\n          <span>{{ formatCount(bannerStats.totalViews) }} views</span>\n        </div>\n        <div class=\"stat-item\">\n          <ion-icon name=\"heart-outline\"></ion-icon>\n          <span>{{ formatCount(bannerStats.totalLikes) }} likes</span>\n        </div>\n        <div class=\"stat-item\">\n          <ion-icon name=\"share-outline\"></ion-icon>\n          <span>{{ formatCount(bannerStats.totalShares) }} shares</span>\n        </div>\n      </div>\n    </div>\n  </div>\n</div>\n"], "mappings": ";AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,QAAQ,gBAAgB;AAE5C,SAASC,YAAY,QAAQ,MAAM;;;;;;;ICQ/BC,EAAA,CAAAC,cAAA,aAAiD;IAC/CD,EAAA,CAAAE,SAAA,qBAA2C;IAC3CF,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAG,MAAA,kCAA2B;IAChCH,EADgC,CAAAI,YAAA,EAAI,EAC9B;;;;;;IAGNJ,EAAA,CAAAC,cAAA,cAAyD;IACvDD,EAAA,CAAAE,SAAA,mBAAiD;IACjDF,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAG,MAAA,GAAW;IAAAH,EAAA,CAAAI,YAAA,EAAI;IAClBJ,EAAA,CAAAC,cAAA,iBAA8C;IAApBD,EAAA,CAAAK,UAAA,mBAAAC,gEAAA;MAAAN,EAAA,CAAAO,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAG,OAAA,EAAS;IAAA,EAAC;IAACZ,EAAA,CAAAG,MAAA,gBAAS;IACzDH,EADyD,CAAAI,YAAA,EAAS,EAC5D;;;;IAFDJ,EAAA,CAAAa,SAAA,GAAW;IAAXb,EAAA,CAAAc,iBAAA,CAAAL,MAAA,CAAAM,KAAA,CAAW;;;;;;IAOdf,EAAA,CAAAC,cAAA,cAA4F;IAAxCD,EAAA,CAAAK,UAAA,mBAAAW,oEAAA;MAAAhB,EAAA,CAAAO,aAAA,CAAAU,GAAA;MAAA,MAAAR,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAS,aAAA,CAAAT,MAAA,CAAAU,cAAA,CAA6B;IAAA,EAAC;IACzFnB,EAAA,CAAAC,cAAA,cAAoC;IAClCD,EAAA,CAAAE,SAAA,cAAoF;IAGhFF,EAFJ,CAAAC,cAAA,cAA4B,cACE,aACD;IAAAD,EAAA,CAAAG,MAAA,GAA0B;IAAAH,EAAA,CAAAI,YAAA,EAAK;IACxDJ,EAAA,CAAAC,cAAA,YAA8B;IAAAD,EAAA,CAAAG,MAAA,GAAgC;IAAAH,EAAA,CAAAI,YAAA,EAAI;IAEhEJ,EADF,CAAAC,cAAA,cAAwB,gBACC;IAAAD,EAAA,CAAAG,MAAA,IAA4B;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAC1DJ,EAAA,CAAAE,SAAA,oBAA0C;IAGhDF,EAFI,CAAAI,YAAA,EAAM,EACF,EACF;IACNJ,EAAA,CAAAC,cAAA,eAA4B;IAC1BD,EAAA,CAAAE,SAAA,oBAAkC;IAClCF,EAAA,CAAAG,MAAA,aACF;IAEJH,EAFI,CAAAI,YAAA,EAAM,EACF,EACF;;;;IAhBGJ,EAAA,CAAAa,SAAA,GAA4B;IAACb,EAA7B,CAAAoB,UAAA,QAAAX,MAAA,CAAAU,cAAA,CAAAE,KAAA,EAAArB,EAAA,CAAAsB,aAAA,CAA4B,QAAAb,MAAA,CAAAU,cAAA,CAAAI,KAAA,CAA6B;IAGjCvB,EAAA,CAAAa,SAAA,GAA0B;IAA1Bb,EAAA,CAAAc,iBAAA,CAAAL,MAAA,CAAAU,cAAA,CAAAI,KAAA,CAA0B;IACrBvB,EAAA,CAAAa,SAAA,GAAgC;IAAhCb,EAAA,CAAAc,iBAAA,CAAAL,MAAA,CAAAU,cAAA,CAAAK,WAAA,CAAgC;IAErCxB,EAAA,CAAAa,SAAA,GAA4B;IAA5Bb,EAAA,CAAAc,iBAAA,CAAAL,MAAA,CAAAU,cAAA,CAAAM,OAAA,CAA4B;;;;;;IAc3DzB,EAAA,CAAAC,cAAA,cAIC;IADCD,EAAA,CAAAK,UAAA,mBAAAqB,0EAAA;MAAA,MAAAC,SAAA,GAAA3B,EAAA,CAAAO,aAAA,CAAAqB,GAAA,EAAAC,SAAA;MAAA,MAAApB,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAS,aAAA,CAAAS,SAAA,CAAqB;IAAA,EAAC;IAE/B3B,EAAA,CAAAC,cAAA,cAAoC;IAClCD,EAAA,CAAAE,SAAA,cAAoE;IAGhEF,EAFJ,CAAAC,cAAA,cAA4B,cACE,aACD;IAAAD,EAAA,CAAAG,MAAA,GAAkB;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAChDJ,EAAA,CAAAC,cAAA,YAA8B;IAAAD,EAAA,CAAAG,MAAA,GAAwB;IAE1DH,EAF0D,CAAAI,YAAA,EAAI,EACtD,EACF;IACNJ,EAAA,CAAAC,cAAA,cAAmE;IACjED,EAAA,CAAAG,MAAA,IACF;IAEJH,EAFI,CAAAI,YAAA,EAAM,EACF,EACF;;;;IAXGJ,EAAA,CAAAa,SAAA,GAAoB;IAACb,EAArB,CAAAoB,UAAA,QAAAO,SAAA,CAAAN,KAAA,EAAArB,EAAA,CAAAsB,aAAA,CAAoB,QAAAK,SAAA,CAAAJ,KAAA,CAAqB;IAGjBvB,EAAA,CAAAa,SAAA,GAAkB;IAAlBb,EAAA,CAAAc,iBAAA,CAAAa,SAAA,CAAAJ,KAAA,CAAkB;IACbvB,EAAA,CAAAa,SAAA,GAAwB;IAAxBb,EAAA,CAAAc,iBAAA,CAAAa,SAAA,CAAAH,WAAA,CAAwB;IAGlCxB,EAAA,CAAAa,SAAA,EAA0C;IAA1Cb,EAAA,CAAA8B,WAAA,qBAAAH,SAAA,CAAAI,QAAA,CAA0C;IAChE/B,EAAA,CAAAa,SAAA,EACF;IADEb,EAAA,CAAAgC,kBAAA,MAAAL,SAAA,CAAAM,GAAA,MACF;;;;;IAhBNjC,EAAA,CAAAC,cAAA,cAAmE;IACjED,EAAA,CAAAkC,UAAA,IAAAC,oDAAA,mBAIC;IAcHnC,EAAA,CAAAI,YAAA,EAAM;;;;IAjBiBJ,EAAA,CAAAa,SAAA,EAAqB;IAAAb,EAArB,CAAAoB,UAAA,YAAAX,MAAA,CAAA2B,gBAAA,CAAqB,iBAAA3B,MAAA,CAAA4B,eAAA,CAAwB;;;;;IAqBlErC,EADF,CAAAC,cAAA,cAA8C,cACrB;IACrBD,EAAA,CAAAE,SAAA,mBAAwC;IACxCF,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAG,MAAA,GAA+C;IACvDH,EADuD,CAAAI,YAAA,EAAO,EACxD;IACNJ,EAAA,CAAAC,cAAA,cAAuB;IACrBD,EAAA,CAAAE,SAAA,mBAA0C;IAC1CF,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAG,MAAA,GAA+C;IACvDH,EADuD,CAAAI,YAAA,EAAO,EACxD;IACNJ,EAAA,CAAAC,cAAA,cAAuB;IACrBD,EAAA,CAAAE,SAAA,oBAA0C;IAC1CF,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAG,MAAA,IAAiD;IAE3DH,EAF2D,CAAAI,YAAA,EAAO,EAC1D,EACF;;;;IAVIJ,EAAA,CAAAa,SAAA,GAA+C;IAA/Cb,EAAA,CAAAgC,kBAAA,KAAAvB,MAAA,CAAA6B,WAAA,CAAA7B,MAAA,CAAA8B,WAAA,CAAAC,UAAA,YAA+C;IAI/CxC,EAAA,CAAAa,SAAA,GAA+C;IAA/Cb,EAAA,CAAAgC,kBAAA,KAAAvB,MAAA,CAAA6B,WAAA,CAAA7B,MAAA,CAAA8B,WAAA,CAAAE,UAAA,YAA+C;IAI/CzC,EAAA,CAAAa,SAAA,GAAiD;IAAjDb,EAAA,CAAAgC,kBAAA,KAAAvB,MAAA,CAAA6B,WAAA,CAAA7B,MAAA,CAAA8B,WAAA,CAAAG,WAAA,aAAiD;;;;;IAxD7D1C,EAAA,CAAAC,cAAA,cAA0D;IA6CxDD,EA3CA,CAAAkC,UAAA,IAAAS,8CAAA,mBAA4F,IAAAC,8CAAA,kBAqBzB,IAAAC,8CAAA,mBAsBrB;IAchD7C,EAAA,CAAAI,YAAA,EAAM;;;;IAzD0BJ,EAAA,CAAAa,SAAA,EAAoB;IAApBb,EAAA,CAAAoB,UAAA,SAAAX,MAAA,CAAAU,cAAA,CAAoB;IAqBlBnB,EAAA,CAAAa,SAAA,EAAiC;IAAjCb,EAAA,CAAAoB,UAAA,SAAAX,MAAA,CAAA2B,gBAAA,CAAAU,MAAA,KAAiC;IAsBtC9C,EAAA,CAAAa,SAAA,EAAiB;IAAjBb,EAAA,CAAAoB,UAAA,SAAAX,MAAA,CAAA8B,WAAA,CAAiB;;;AD1BlD,OAAM,MAAOQ,wBAAwB;EAQnCC,YAAoBC,MAAc;IAAd,KAAAA,MAAM,GAANA,MAAM;IAP1B,KAAA9B,cAAc,GAA0B,IAAI;IAC5C,KAAAiB,gBAAgB,GAAqB,EAAE;IACvC,KAAAG,WAAW,GAAuB,IAAI;IACtC,KAAAW,SAAS,GAAG,IAAI;IAChB,KAAAnC,KAAK,GAAkB,IAAI;IACnB,KAAAoC,YAAY,GAAiB,IAAIpD,YAAY,EAAE;EAElB;EAErCqD,QAAQA,CAAA;IACN,IAAI,CAACC,mBAAmB,EAAE;EAC5B;EAEAC,WAAWA,CAAA;IACT,IAAI,CAACH,YAAY,CAACI,WAAW,EAAE;EACjC;EAEcF,mBAAmBA,CAAA;IAAA,IAAAG,KAAA;IAAA,OAAAC,iBAAA;MAC/B,IAAI;QACFD,KAAI,CAACN,SAAS,GAAG,IAAI;QACrBM,KAAI,CAACzC,KAAK,GAAG,IAAI;QAEjB;QACA,MAAMyC,KAAI,CAACE,eAAe,EAAE;OAE7B,CAAC,OAAO3C,KAAK,EAAE;QACd4C,OAAO,CAAC5C,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;QACvDyC,KAAI,CAACzC,KAAK,GAAG,iCAAiC;QAC9CyC,KAAI,CAACN,SAAS,GAAG,KAAK;;IACvB;EACH;EAEcQ,eAAeA,CAAA;IAAA,IAAAE,MAAA;IAAA,OAAAH,iBAAA;MAC3B;MACA,MAAM,IAAII,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,IAAI,CAAC,CAAC;MAEvD;MACAF,MAAI,CAACzC,cAAc,GAAG;QACpB6C,GAAG,EAAE,SAAS;QACdzC,KAAK,EAAE,wBAAwB;QAC/BC,WAAW,EAAE,oDAAoD;QACjEH,KAAK,EAAE,mFAAmF;QAC1FI,OAAO,EAAE,UAAU;QACnBwC,IAAI,EAAE,0BAA0B;QAChChC,GAAG,EAAE,KAAK;QACVF,QAAQ,EAAE,SAAS;QACnBmC,QAAQ,EAAE,CAAC;QACXC,QAAQ,EAAE,IAAI;QACdC,SAAS,EAAE;UACTC,KAAK,EAAE,KAAK;UACZC,MAAM,EAAE,IAAI;UACZC,KAAK,EAAE,GAAG;UACVC,MAAM,EAAE;SACT;QACDC,SAAS,EAAE,IAAIC,IAAI;OACpB;MAED;MACAd,MAAI,CAACxB,gBAAgB,GAAG,CACtB;QACE4B,GAAG,EAAE,SAAS;QACdzC,KAAK,EAAE,YAAY;QACnBC,WAAW,EAAE,iCAAiC;QAC9CH,KAAK,EAAE,mFAAmF;QAC1FI,OAAO,EAAE,WAAW;QACpBwC,IAAI,EAAE,OAAO;QACbhC,GAAG,EAAE,MAAM;QACXF,QAAQ,EAAE,SAAS;QACnBmC,QAAQ,EAAE,CAAC;QACXC,QAAQ,EAAE,IAAI;QACdC,SAAS,EAAE;UACTC,KAAK,EAAE,IAAI;UACXC,MAAM,EAAE,IAAI;UACZC,KAAK,EAAE,GAAG;UACVC,MAAM,EAAE;SACT;QACDC,SAAS,EAAE,IAAIC,IAAI;OACpB,EACD;QACEV,GAAG,EAAE,SAAS;QACdzC,KAAK,EAAE,wBAAwB;QAC/BC,WAAW,EAAE,qCAAqC;QAClDH,KAAK,EAAE,mFAAmF;QAC1FI,OAAO,EAAE,SAAS;QAClBwC,IAAI,EAAE,kBAAkB;QACxBhC,GAAG,EAAE,WAAW;QAChBF,QAAQ,EAAE,SAAS;QACnBmC,QAAQ,EAAE,CAAC;QACXC,QAAQ,EAAE,IAAI;QACdC,SAAS,EAAE;UACTC,KAAK,EAAE,IAAI;UACXC,MAAM,EAAE,GAAG;UACXC,KAAK,EAAE,GAAG;UACVC,MAAM,EAAE;SACT;QACDC,SAAS,EAAE,IAAIC,IAAI;OACpB,CACF;MAED;MACAd,MAAI,CAACrB,WAAW,GAAG;QACjBC,UAAU,EAAEoB,MAAI,CAACzC,cAAc,CAACiD,SAAS,CAACC,KAAK,GACnCT,MAAI,CAACxB,gBAAgB,CAACuC,MAAM,CAAC,CAACC,GAAG,EAAEC,MAAM,KAAKD,GAAG,GAAGC,MAAM,CAACT,SAAS,CAACC,KAAK,EAAE,CAAC,CAAC;QAC1F5B,UAAU,EAAEmB,MAAI,CAACzC,cAAc,CAACiD,SAAS,CAACG,KAAK,GACnCX,MAAI,CAACxB,gBAAgB,CAACuC,MAAM,CAAC,CAACC,GAAG,EAAEC,MAAM,KAAKD,GAAG,GAAGC,MAAM,CAACT,SAAS,CAACG,KAAK,EAAE,CAAC,CAAC;QAC1F7B,WAAW,EAAEkB,MAAI,CAACzC,cAAc,CAACiD,SAAS,CAACI,MAAM,GACpCZ,MAAI,CAACxB,gBAAgB,CAACuC,MAAM,CAAC,CAACC,GAAG,EAAEC,MAAM,KAAKD,GAAG,GAAGC,MAAM,CAACT,SAAS,CAACI,MAAM,EAAE,CAAC,CAAC;QAC5FM,WAAW,EAAElB,MAAI,CAACzC,cAAc,CAACiD,SAAS,CAACE,MAAM,GACpCV,MAAI,CAACxB,gBAAgB,CAACuC,MAAM,CAAC,CAACC,GAAG,EAAEC,MAAM,KAAKD,GAAG,GAAGC,MAAM,CAACT,SAAS,CAACE,MAAM,EAAE,CAAC;OAC5F;MAEDV,MAAI,CAACV,SAAS,GAAG,KAAK;IAAC;EACzB;EAEAhC,aAAaA,CAAC2D,MAAsB;IAClC;IACAA,MAAM,CAACT,SAAS,CAACE,MAAM,EAAE;IAEzB;IACA,IAAIO,MAAM,CAACZ,IAAI,CAACc,UAAU,CAAC,MAAM,CAAC,EAAE;MAClCC,MAAM,CAACC,IAAI,CAACJ,MAAM,CAACZ,IAAI,EAAE,QAAQ,CAAC;KACnC,MAAM;MACL,IAAI,CAAChB,MAAM,CAACiC,QAAQ,CAAC,CAACL,MAAM,CAACZ,IAAI,CAAC,CAAC;;EAEvC;EAEAkB,QAAQA,CAACC,KAAY;IACnBA,KAAK,CAACC,cAAc,EAAE;IACtB,IAAI,CAACpC,MAAM,CAACiC,QAAQ,CAAC,CAAC,mBAAmB,CAAC,CAAC;EAC7C;EAEAtE,OAAOA,CAAA;IACL,IAAI,CAACyC,mBAAmB,EAAE;EAC5B;EAEAhB,eAAeA,CAACiD,KAAa,EAAET,MAAsB;IACnD,OAAOA,MAAM,CAACb,GAAG;EACnB;EAEA1B,WAAWA,CAACiD,KAAa;IACvB,IAAIA,KAAK,IAAI,OAAO,EAAE;MACpB,OAAO,CAACA,KAAK,GAAG,OAAO,EAAEC,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG;KAC1C,MAAM,IAAID,KAAK,IAAI,IAAI,EAAE;MACxB,OAAO,CAACA,KAAK,GAAG,IAAI,EAAEC,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG;;IAExC,OAAOD,KAAK,CAACE,QAAQ,EAAE;EACzB;;;uBAnJW1C,wBAAwB,EAAA/C,EAAA,CAAA0F,iBAAA,CAAAC,EAAA,CAAAC,MAAA;IAAA;EAAA;;;YAAxB7C,wBAAwB;MAAA8C,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAA/F,EAAA,CAAAgG,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,kCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UC1CjCtG,EAHJ,CAAAC,cAAA,aAAiC,aAED,SACxB;UACFD,EAAA,CAAAE,SAAA,kBAA8C;UAC9CF,EAAA,CAAAG,MAAA,yBACF;UAAAH,EAAA,CAAAI,YAAA,EAAK;UACLJ,EAAA,CAAAC,cAAA,WAAuD;UAA3BD,EAAA,CAAAK,UAAA,mBAAAmG,qDAAAC,MAAA;YAAA,OAASF,GAAA,CAAApB,QAAA,CAAAsB,MAAA,CAAgB;UAAA,EAAC;UAACzG,EAAA,CAAAG,MAAA,cAAO;UAChEH,EADgE,CAAAI,YAAA,EAAI,EAC9D;UAGNJ,EAAA,CAAAC,cAAA,aAA+B;UAe7BD,EAbA,CAAAkC,UAAA,IAAAwE,uCAAA,iBAAiD,IAAAC,uCAAA,iBAMQ,KAAAC,wCAAA,iBAOC;UA6D9D5G,EADE,CAAAI,YAAA,EAAM,EACF;;;UA1EIJ,EAAA,CAAAa,SAAA,GAAe;UAAfb,EAAA,CAAAoB,UAAA,SAAAmF,GAAA,CAAArD,SAAA,CAAe;UAMflD,EAAA,CAAAa,SAAA,EAAyB;UAAzBb,EAAA,CAAAoB,UAAA,SAAAmF,GAAA,CAAAxF,KAAA,KAAAwF,GAAA,CAAArD,SAAA,CAAyB;UAOzBlD,EAAA,CAAAa,SAAA,EAA0B;UAA1Bb,EAAA,CAAAoB,UAAA,UAAAmF,GAAA,CAAArD,SAAA,KAAAqD,GAAA,CAAAxF,KAAA,CAA0B;;;qBDYhCnB,YAAY,EAAAiH,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EACZlH,YAAY,EACZC,WAAW,EAAAkH,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,UAAA;MAAAC,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}