{"ast": null, "code": "import _asyncToGenerator from \"E:/Fashion/DFashionFrontend/frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\nimport { IonicModule } from '@ionic/angular';\nimport { Subscription } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"@ionic/angular\";\nconst _c0 = () => [1, 2, 3, 4, 5];\nfunction TrendingProductSlidersComponent_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 9);\n    i0.ɵɵelement(1, \"ion-spinner\", 10);\n    i0.ɵɵelementStart(2, \"p\");\n    i0.ɵɵtext(3, \"Loading trending products...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction TrendingProductSlidersComponent_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 11);\n    i0.ɵɵelement(1, \"ion-icon\", 12);\n    i0.ɵɵelementStart(2, \"p\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"button\", 13);\n    i0.ɵɵlistener(\"click\", function TrendingProductSlidersComponent_div_9_Template_button_click_4_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onRetry());\n    });\n    i0.ɵɵtext(5, \"Try Again\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r1.error);\n  }\n}\nfunction TrendingProductSlidersComponent_div_10_div_8_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 50);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const product_r5 = i0.ɵɵnextContext().$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.getDiscountPercentage(product_r5), \"% OFF \");\n  }\n}\nfunction TrendingProductSlidersComponent_div_10_div_8_span_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 51);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const product_r5 = i0.ɵɵnextContext().$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r1.formatPrice(product_r5.originalPrice));\n  }\n}\nfunction TrendingProductSlidersComponent_div_10_div_8_ion_icon_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"ion-icon\", 33);\n  }\n  if (rf & 2) {\n    const star_r6 = ctx.$implicit;\n    const product_r5 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵclassProp(\"filled\", star_r6 <= product_r5.rating.average);\n    i0.ɵɵproperty(\"name\", star_r6 <= product_r5.rating.average ? \"star\" : \"star-outline\");\n  }\n}\nfunction TrendingProductSlidersComponent_div_10_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 25);\n    i0.ɵɵlistener(\"click\", function TrendingProductSlidersComponent_div_10_div_8_Template_div_click_0_listener() {\n      const product_r5 = i0.ɵɵrestoreView(_r4).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onProductClick(product_r5));\n    });\n    i0.ɵɵelementStart(1, \"div\", 26);\n    i0.ɵɵelement(2, \"img\", 27);\n    i0.ɵɵelementStart(3, \"div\", 28);\n    i0.ɵɵelement(4, \"ion-icon\", 29);\n    i0.ɵɵtext(5, \" Trending \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(6, TrendingProductSlidersComponent_div_10_div_8_div_6_Template, 2, 1, \"div\", 30);\n    i0.ɵɵelementStart(7, \"div\", 31)(8, \"button\", 32);\n    i0.ɵɵlistener(\"click\", function TrendingProductSlidersComponent_div_10_div_8_Template_button_click_8_listener($event) {\n      const product_r5 = i0.ɵɵrestoreView(_r4).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.toggleLike(product_r5, $event));\n    });\n    i0.ɵɵelement(9, \"ion-icon\", 33);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"button\", 34);\n    i0.ɵɵlistener(\"click\", function TrendingProductSlidersComponent_div_10_div_8_Template_button_click_10_listener($event) {\n      const product_r5 = i0.ɵɵrestoreView(_r4).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.addToCart(product_r5, $event));\n    });\n    i0.ɵɵelement(11, \"ion-icon\", 35);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(12, \"div\", 36)(13, \"div\", 37);\n    i0.ɵɵtext(14);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"h4\", 38);\n    i0.ɵɵtext(16);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"div\", 39)(18, \"span\", 40);\n    i0.ɵɵtext(19);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(20, TrendingProductSlidersComponent_div_10_div_8_span_20_Template, 2, 1, \"span\", 41);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"div\", 42)(22, \"div\", 43);\n    i0.ɵɵtemplate(23, TrendingProductSlidersComponent_div_10_div_8_ion_icon_23_Template, 1, 3, \"ion-icon\", 44);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(24, \"span\", 45);\n    i0.ɵɵtext(25);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(26, \"div\", 46)(27, \"div\", 47);\n    i0.ɵɵelement(28, \"ion-icon\", 48);\n    i0.ɵɵelementStart(29, \"span\");\n    i0.ɵɵtext(30);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(31, \"div\", 47);\n    i0.ɵɵelement(32, \"ion-icon\", 49);\n    i0.ɵɵelementStart(33, \"span\");\n    i0.ɵɵtext(34);\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const product_r5 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"src\", product_r5.images[0].url, i0.ɵɵsanitizeUrl)(\"alt\", product_r5.images[0].alt || product_r5.name);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.getDiscountPercentage(product_r5) > 0);\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassProp(\"active\", ctx_r1.isProductLiked(product_r5._id));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"name\", ctx_r1.isProductLiked(product_r5._id) ? \"heart\" : \"heart-outline\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(product_r5.brand);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(product_r5.name);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r1.formatPrice(product_r5.price));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", product_r5.originalPrice && product_r5.originalPrice > product_r5.price);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpureFunction0(14, _c0));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"(\", product_r5.rating.count, \")\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r1.formatCount(product_r5.analytics.views));\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r1.formatCount(product_r5.analytics.purchases));\n  }\n}\nfunction TrendingProductSlidersComponent_div_10_button_10_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 52);\n    i0.ɵɵlistener(\"click\", function TrendingProductSlidersComponent_div_10_button_10_Template_button_click_0_listener() {\n      const i_r8 = i0.ɵɵrestoreView(_r7).index;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.goToSlide(i_r8));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const i_r8 = ctx.index;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassProp(\"active\", i_r8 === ctx_r1.currentSlide);\n  }\n}\nfunction TrendingProductSlidersComponent_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 14)(1, \"div\", 15)(2, \"button\", 16);\n    i0.ɵɵlistener(\"click\", function TrendingProductSlidersComponent_div_10_Template_button_click_2_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.previousSlide());\n    });\n    i0.ɵɵelement(3, \"ion-icon\", 17);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"button\", 18);\n    i0.ɵɵlistener(\"click\", function TrendingProductSlidersComponent_div_10_Template_button_click_4_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.nextSlide());\n    });\n    i0.ɵɵelement(5, \"ion-icon\", 19);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 20);\n    i0.ɵɵlistener(\"mouseenter\", function TrendingProductSlidersComponent_div_10_Template_div_mouseenter_6_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.pauseAutoSlide());\n    })(\"mouseleave\", function TrendingProductSlidersComponent_div_10_Template_div_mouseleave_6_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.resumeAutoSlide());\n    });\n    i0.ɵɵelementStart(7, \"div\", 21);\n    i0.ɵɵtemplate(8, TrendingProductSlidersComponent_div_10_div_8_Template, 35, 15, \"div\", 22);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"div\", 23);\n    i0.ɵɵtemplate(10, TrendingProductSlidersComponent_div_10_button_10_Template, 1, 2, \"button\", 24);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"disabled\", ctx_r1.currentSlide === 0);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"disabled\", ctx_r1.currentSlide >= ctx_r1.maxSlide);\n    i0.ɵɵadvance(3);\n    i0.ɵɵstyleProp(\"transform\", \"translateX(\" + ctx_r1.slideOffset + \"px)\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.trendingProducts)(\"ngForTrackBy\", ctx_r1.trackByProductId);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.getSlideIndicators());\n  }\n}\nfunction TrendingProductSlidersComponent_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 53);\n    i0.ɵɵelement(1, \"ion-icon\", 2);\n    i0.ɵɵelementStart(2, \"p\");\n    i0.ɵɵtext(3, \"No trending products available\");\n    i0.ɵɵelementEnd()();\n  }\n}\nexport class TrendingProductSlidersComponent {\n  constructor(router) {\n    this.router = router;\n    this.trendingProducts = [];\n    this.likedProducts = new Set();\n    this.isLoading = true;\n    this.error = null;\n    this.subscription = new Subscription();\n    // Slider properties\n    this.currentSlide = 0;\n    this.slideOffset = 0;\n    this.cardWidth = 280; // Width of each product card including margin\n    this.visibleCards = 4; // Number of cards visible at once\n    this.maxSlide = 0;\n    this.autoSlideDelay = 3000; // 3 seconds\n    this.isAutoSliding = true;\n    this.isPaused = false;\n  }\n  ngOnInit() {\n    this.loadTrendingProducts();\n    this.updateResponsiveSettings();\n    this.setupResizeListener();\n  }\n  ngOnDestroy() {\n    this.subscription.unsubscribe();\n    this.stopAutoSlide();\n  }\n  loadTrendingProducts() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      try {\n        _this.isLoading = true;\n        _this.error = null;\n        // Mock data for now - replace with actual service call\n        yield _this.loadMockProducts();\n      } catch (error) {\n        console.error('Error loading trending products:', error);\n        _this.error = 'Failed to load trending products';\n        _this.isLoading = false;\n      }\n    })();\n  }\n  loadMockProducts() {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      // Simulate API call delay\n      yield new Promise(resolve => setTimeout(resolve, 800));\n      _this2.trendingProducts = [{\n        _id: 'prod1',\n        name: 'Trendy Summer Dress',\n        brand: 'Fashion Co.',\n        price: 2999,\n        originalPrice: 3999,\n        images: [{\n          url: 'https://images.unsplash.com/photo-1515372039744-b8f02a3ae446?w=300&h=400&fit=crop',\n          alt: 'Summer Dress'\n        }],\n        rating: {\n          average: 4.5,\n          count: 128\n        },\n        analytics: {\n          views: 2340,\n          likes: 189,\n          purchases: 67,\n          shares: 23\n        },\n        isActive: true,\n        isFeatured: true,\n        createdAt: new Date()\n      }, {\n        _id: 'prod2',\n        name: 'Casual Sneakers',\n        brand: 'SportWear',\n        price: 4999,\n        originalPrice: 6999,\n        images: [{\n          url: 'https://images.unsplash.com/photo-1542291026-7eec264c27ff?w=300&h=400&fit=crop',\n          alt: 'Sneakers'\n        }],\n        rating: {\n          average: 4.7,\n          count: 256\n        },\n        analytics: {\n          views: 3450,\n          likes: 278,\n          purchases: 89,\n          shares: 34\n        },\n        isActive: true,\n        isFeatured: true,\n        createdAt: new Date()\n      }, {\n        _id: 'prod3',\n        name: 'Designer Handbag',\n        brand: 'Luxury Brand',\n        price: 8999,\n        images: [{\n          url: 'https://images.unsplash.com/photo-1553062407-98eeb64c6a62?w=300&h=400&fit=crop',\n          alt: 'Handbag'\n        }],\n        rating: {\n          average: 4.3,\n          count: 89\n        },\n        analytics: {\n          views: 1890,\n          likes: 145,\n          purchases: 34,\n          shares: 12\n        },\n        isActive: true,\n        isFeatured: true,\n        createdAt: new Date()\n      }, {\n        _id: 'prod4',\n        name: 'Stylish Sunglasses',\n        brand: 'Eye Fashion',\n        price: 1999,\n        originalPrice: 2999,\n        images: [{\n          url: 'https://images.unsplash.com/photo-1572635196237-14b3f281503f?w=300&h=400&fit=crop',\n          alt: 'Sunglasses'\n        }],\n        rating: {\n          average: 4.6,\n          count: 167\n        },\n        analytics: {\n          views: 2780,\n          likes: 234,\n          purchases: 78,\n          shares: 19\n        },\n        isActive: true,\n        isFeatured: true,\n        createdAt: new Date()\n      }, {\n        _id: 'prod5',\n        name: 'Elegant Watch',\n        brand: 'TimeKeeper',\n        price: 12999,\n        originalPrice: 15999,\n        images: [{\n          url: 'https://images.unsplash.com/photo-1523275335684-37898b6baf30?w=300&h=400&fit=crop',\n          alt: 'Watch'\n        }],\n        rating: {\n          average: 4.8,\n          count: 203\n        },\n        analytics: {\n          views: 4560,\n          likes: 389,\n          purchases: 123,\n          shares: 45\n        },\n        isActive: true,\n        isFeatured: true,\n        createdAt: new Date()\n      }];\n      _this2.updateSliderOnProductsLoad();\n      _this2.isLoading = false;\n    })();\n  }\n  updateSliderOnProductsLoad() {\n    this.calculateMaxSlide();\n    this.currentSlide = 0;\n    this.updateSlidePosition();\n    this.startAutoSlide();\n  }\n  updateResponsiveSettings() {\n    const width = window.innerWidth;\n    if (width <= 425) {\n      this.visibleCards = 1;\n      this.cardWidth = 260;\n    } else if (width <= 768) {\n      this.visibleCards = 2;\n      this.cardWidth = 270;\n    } else if (width <= 1024) {\n      this.visibleCards = 3;\n      this.cardWidth = 280;\n    } else {\n      this.visibleCards = 4;\n      this.cardWidth = 280;\n    }\n    this.calculateMaxSlide();\n    this.updateSlidePosition();\n  }\n  setupResizeListener() {\n    window.addEventListener('resize', () => {\n      this.updateResponsiveSettings();\n    });\n  }\n  calculateMaxSlide() {\n    this.maxSlide = Math.max(0, this.trendingProducts.length - this.visibleCards);\n  }\n  updateSlidePosition() {\n    this.slideOffset = -this.currentSlide * this.cardWidth;\n  }\n  // Slider navigation methods\n  nextSlide() {\n    if (this.currentSlide < this.maxSlide) {\n      this.currentSlide++;\n      this.updateSlidePosition();\n    }\n  }\n  previousSlide() {\n    if (this.currentSlide > 0) {\n      this.currentSlide--;\n      this.updateSlidePosition();\n    }\n  }\n  goToSlide(slideIndex) {\n    this.currentSlide = Math.max(0, Math.min(slideIndex, this.maxSlide));\n    this.updateSlidePosition();\n  }\n  getSlideIndicators() {\n    return Array(this.maxSlide + 1).fill(0).map((_, i) => i);\n  }\n  // Auto-sliding methods\n  startAutoSlide() {\n    if (!this.isAutoSliding || this.isPaused) return;\n    this.stopAutoSlide();\n    this.autoSlideInterval = setInterval(() => {\n      if (!this.isPaused && this.trendingProducts.length > this.visibleCards) {\n        this.autoSlideNext();\n      }\n    }, this.autoSlideDelay);\n  }\n  autoSlideNext() {\n    if (this.currentSlide >= this.maxSlide) {\n      this.currentSlide = 0;\n    } else {\n      this.currentSlide++;\n    }\n    this.updateSlidePosition();\n  }\n  stopAutoSlide() {\n    if (this.autoSlideInterval) {\n      clearInterval(this.autoSlideInterval);\n      this.autoSlideInterval = null;\n    }\n  }\n  pauseAutoSlide() {\n    this.isPaused = true;\n  }\n  resumeAutoSlide() {\n    this.isPaused = false;\n    this.startAutoSlide();\n  }\n  // Product interaction methods\n  onProductClick(product) {\n    this.router.navigate(['/product', product._id]);\n  }\n  toggleLike(product, event) {\n    event.stopPropagation();\n    if (this.likedProducts.has(product._id)) {\n      this.likedProducts.delete(product._id);\n      product.analytics.likes--;\n    } else {\n      this.likedProducts.add(product._id);\n      product.analytics.likes++;\n    }\n  }\n  addToCart(product, event) {\n    event.stopPropagation();\n    // Add to cart logic here\n    console.log('Added to cart:', product.name);\n  }\n  isProductLiked(productId) {\n    return this.likedProducts.has(productId);\n  }\n  getDiscountPercentage(product) {\n    if (product.originalPrice && product.originalPrice > product.price) {\n      return Math.round((product.originalPrice - product.price) / product.originalPrice * 100);\n    }\n    return 0;\n  }\n  formatPrice(price) {\n    return new Intl.NumberFormat('en-IN', {\n      style: 'currency',\n      currency: 'INR',\n      minimumFractionDigits: 0\n    }).format(price);\n  }\n  formatCount(count) {\n    if (count >= 1000) {\n      return (count / 1000).toFixed(1) + 'K';\n    }\n    return count.toString();\n  }\n  trackByProductId(index, product) {\n    return product._id;\n  }\n  onSeeAll(event) {\n    event.preventDefault();\n    this.router.navigate(['/trending/products']);\n  }\n  onRetry() {\n    this.loadTrendingProducts();\n  }\n  static {\n    this.ɵfac = function TrendingProductSlidersComponent_Factory(t) {\n      return new (t || TrendingProductSlidersComponent)(i0.ɵɵdirectiveInject(i1.Router));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: TrendingProductSlidersComponent,\n      selectors: [[\"app-trending-product-sliders\"]],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 12,\n      vars: 4,\n      consts: [[1, \"component-container\"], [1, \"component-header\"], [\"name\", \"storefront-outline\"], [\"href\", \"#\", 1, \"see-all\", 3, \"click\"], [1, \"component-content\"], [\"class\", \"loading-container\", 4, \"ngIf\"], [\"class\", \"error-container\", 4, \"ngIf\"], [\"class\", \"products-slider-container\", 4, \"ngIf\"], [\"class\", \"empty-container\", 4, \"ngIf\"], [1, \"loading-container\"], [\"name\", \"crescent\"], [1, \"error-container\"], [\"name\", \"alert-circle-outline\"], [1, \"retry-btn\", 3, \"click\"], [1, \"products-slider-container\"], [1, \"slider-navigation\"], [1, \"nav-btn\", \"prev-btn\", 3, \"click\", \"disabled\"], [\"name\", \"chevron-back\"], [1, \"nav-btn\", \"next-btn\", 3, \"click\", \"disabled\"], [\"name\", \"chevron-forward\"], [1, \"products-slider-wrapper\", 3, \"mouseenter\", \"mouseleave\"], [1, \"products-slider\"], [\"class\", \"product-card\", 3, \"click\", 4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [1, \"slider-indicators\"], [\"class\", \"indicator\", 3, \"active\", \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"product-card\", 3, \"click\"], [1, \"product-image-container\"], [\"loading\", \"lazy\", 1, \"product-image\", 3, \"src\", \"alt\"], [1, \"trending-badge\"], [\"name\", \"trending-up\"], [\"class\", \"discount-badge\", 4, \"ngIf\"], [1, \"quick-actions\"], [1, \"action-btn\", \"like-btn\", 3, \"click\"], [3, \"name\"], [1, \"action-btn\", \"cart-btn\", 3, \"click\"], [\"name\", \"bag-add-outline\"], [1, \"product-info\"], [1, \"product-brand\"], [1, \"product-name\"], [1, \"price-section\"], [1, \"current-price\"], [\"class\", \"original-price\", 4, \"ngIf\"], [1, \"rating-section\"], [1, \"stars\"], [3, \"name\", \"filled\", 4, \"ngFor\", \"ngForOf\"], [1, \"rating-text\"], [1, \"trending-stats\"], [1, \"stat-item\"], [\"name\", \"eye-outline\"], [\"name\", \"bag-outline\"], [1, \"discount-badge\"], [1, \"original-price\"], [1, \"indicator\", 3, \"click\"], [1, \"empty-container\"]],\n      template: function TrendingProductSlidersComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"h3\");\n          i0.ɵɵelement(3, \"ion-icon\", 2);\n          i0.ɵɵtext(4, \" Trending Products \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(5, \"a\", 3);\n          i0.ɵɵlistener(\"click\", function TrendingProductSlidersComponent_Template_a_click_5_listener($event) {\n            return ctx.onSeeAll($event);\n          });\n          i0.ɵɵtext(6, \"See All\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(7, \"div\", 4);\n          i0.ɵɵtemplate(8, TrendingProductSlidersComponent_div_8_Template, 4, 0, \"div\", 5)(9, TrendingProductSlidersComponent_div_9_Template, 6, 1, \"div\", 6)(10, TrendingProductSlidersComponent_div_10_Template, 11, 7, \"div\", 7)(11, TrendingProductSlidersComponent_div_11_Template, 4, 0, \"div\", 8);\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.error && !ctx.isLoading);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading && !ctx.error && ctx.trendingProducts.length > 0);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading && !ctx.error && ctx.trendingProducts.length === 0);\n        }\n      },\n      dependencies: [CommonModule, i2.NgForOf, i2.NgIf, RouterModule, IonicModule, i3.IonIcon, i3.IonSpinner],\n      styles: [\".loading-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n  padding: 40px 20px;\\n  color: #666;\\n}\\n.loading-container[_ngcontent-%COMP%]   ion-spinner[_ngcontent-%COMP%] {\\n  margin-bottom: 16px;\\n}\\n.loading-container[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 14px;\\n}\\n\\n.error-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n  padding: 40px 20px;\\n  color: #e74c3c;\\n  text-align: center;\\n}\\n.error-container[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n  font-size: 48px;\\n  margin-bottom: 16px;\\n  color: #e74c3c;\\n}\\n.error-container[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0 0 16px 0;\\n  font-size: 14px;\\n}\\n.error-container[_ngcontent-%COMP%]   .retry-btn[_ngcontent-%COMP%] {\\n  background: #e74c3c;\\n  color: white;\\n  border: none;\\n  padding: 8px 16px;\\n  border-radius: 6px;\\n  font-size: 12px;\\n  cursor: pointer;\\n  transition: background 0.3s ease;\\n}\\n.error-container[_ngcontent-%COMP%]   .retry-btn[_ngcontent-%COMP%]:hover {\\n  background: #c0392b;\\n}\\n\\n.empty-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n  padding: 40px 20px;\\n  color: #999;\\n  text-align: center;\\n}\\n.empty-container[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n  font-size: 48px;\\n  margin-bottom: 16px;\\n  color: #ccc;\\n}\\n.empty-container[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 14px;\\n}\\n\\n.products-slider-container[_ngcontent-%COMP%] {\\n  position: relative;\\n}\\n.products-slider-container[_ngcontent-%COMP%]   .slider-navigation[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 50%;\\n  left: 0;\\n  right: 0;\\n  transform: translateY(-50%);\\n  z-index: 10;\\n  pointer-events: none;\\n}\\n.products-slider-container[_ngcontent-%COMP%]   .slider-navigation[_ngcontent-%COMP%]   .nav-btn[_ngcontent-%COMP%] {\\n  position: absolute;\\n  background: rgba(255, 255, 255, 0.9);\\n  border: 1px solid #ddd;\\n  border-radius: 50%;\\n  width: 40px;\\n  height: 40px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n  pointer-events: all;\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\\n}\\n.products-slider-container[_ngcontent-%COMP%]   .slider-navigation[_ngcontent-%COMP%]   .nav-btn[_ngcontent-%COMP%]:hover:not(:disabled) {\\n  background: white;\\n  transform: scale(1.1);\\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\\n}\\n.products-slider-container[_ngcontent-%COMP%]   .slider-navigation[_ngcontent-%COMP%]   .nav-btn[_ngcontent-%COMP%]:disabled {\\n  opacity: 0.5;\\n  cursor: not-allowed;\\n}\\n.products-slider-container[_ngcontent-%COMP%]   .slider-navigation[_ngcontent-%COMP%]   .nav-btn[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n  font-size: 20px;\\n  color: #333;\\n}\\n.products-slider-container[_ngcontent-%COMP%]   .slider-navigation[_ngcontent-%COMP%]   .nav-btn.prev-btn[_ngcontent-%COMP%] {\\n  left: -20px;\\n}\\n.products-slider-container[_ngcontent-%COMP%]   .slider-navigation[_ngcontent-%COMP%]   .nav-btn.next-btn[_ngcontent-%COMP%] {\\n  right: -20px;\\n}\\n.products-slider-container[_ngcontent-%COMP%]   .products-slider-wrapper[_ngcontent-%COMP%] {\\n  overflow: hidden;\\n  margin: 0 20px;\\n}\\n.products-slider-container[_ngcontent-%COMP%]   .products-slider-wrapper[_ngcontent-%COMP%]   .products-slider[_ngcontent-%COMP%] {\\n  display: flex;\\n  transition: transform 0.3s ease;\\n  gap: 16px;\\n}\\n.products-slider-container[_ngcontent-%COMP%]   .products-slider-wrapper[_ngcontent-%COMP%]   .products-slider[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%] {\\n  flex: 0 0 auto;\\n  width: 260px;\\n  background: white;\\n  border-radius: 12px;\\n  overflow: hidden;\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n}\\n.products-slider-container[_ngcontent-%COMP%]   .products-slider-wrapper[_ngcontent-%COMP%]   .products-slider[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-4px);\\n  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);\\n}\\n.products-slider-container[_ngcontent-%COMP%]   .products-slider-wrapper[_ngcontent-%COMP%]   .products-slider[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]   .product-image-container[_ngcontent-%COMP%] {\\n  position: relative;\\n  width: 100%;\\n  height: 200px;\\n  overflow: hidden;\\n}\\n.products-slider-container[_ngcontent-%COMP%]   .products-slider-wrapper[_ngcontent-%COMP%]   .products-slider[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]   .product-image-container[_ngcontent-%COMP%]   .product-image[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 100%;\\n  object-fit: cover;\\n  transition: transform 0.3s ease;\\n}\\n.products-slider-container[_ngcontent-%COMP%]   .products-slider-wrapper[_ngcontent-%COMP%]   .products-slider[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]   .product-image-container[_ngcontent-%COMP%]:hover   .product-image[_ngcontent-%COMP%] {\\n  transform: scale(1.05);\\n}\\n.products-slider-container[_ngcontent-%COMP%]   .products-slider-wrapper[_ngcontent-%COMP%]   .products-slider[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]   .product-image-container[_ngcontent-%COMP%]   .trending-badge[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 8px;\\n  left: 8px;\\n  background: linear-gradient(135deg, #6c5ce7, #a29bfe);\\n  color: white;\\n  padding: 4px 8px;\\n  border-radius: 12px;\\n  font-size: 10px;\\n  font-weight: 600;\\n  display: flex;\\n  align-items: center;\\n  gap: 4px;\\n  box-shadow: 0 2px 6px rgba(108, 92, 231, 0.3);\\n}\\n.products-slider-container[_ngcontent-%COMP%]   .products-slider-wrapper[_ngcontent-%COMP%]   .products-slider[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]   .product-image-container[_ngcontent-%COMP%]   .trending-badge[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n}\\n.products-slider-container[_ngcontent-%COMP%]   .products-slider-wrapper[_ngcontent-%COMP%]   .products-slider[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]   .product-image-container[_ngcontent-%COMP%]   .discount-badge[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 8px;\\n  right: 8px;\\n  background: linear-gradient(135deg, #ff6b6b, #ee5a24);\\n  color: white;\\n  padding: 4px 8px;\\n  border-radius: 12px;\\n  font-size: 10px;\\n  font-weight: 600;\\n  box-shadow: 0 2px 6px rgba(255, 107, 107, 0.3);\\n}\\n.products-slider-container[_ngcontent-%COMP%]   .products-slider-wrapper[_ngcontent-%COMP%]   .products-slider[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]   .product-image-container[_ngcontent-%COMP%]   .quick-actions[_ngcontent-%COMP%] {\\n  position: absolute;\\n  bottom: 8px;\\n  right: 8px;\\n  display: flex;\\n  gap: 6px;\\n  opacity: 0;\\n  transition: opacity 0.3s ease;\\n}\\n.products-slider-container[_ngcontent-%COMP%]   .products-slider-wrapper[_ngcontent-%COMP%]   .products-slider[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]   .product-image-container[_ngcontent-%COMP%]   .quick-actions[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%] {\\n  background: rgba(255, 255, 255, 0.9);\\n  border: none;\\n  border-radius: 50%;\\n  width: 32px;\\n  height: 32px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);\\n}\\n.products-slider-container[_ngcontent-%COMP%]   .products-slider-wrapper[_ngcontent-%COMP%]   .products-slider[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]   .product-image-container[_ngcontent-%COMP%]   .quick-actions[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%]:hover {\\n  background: white;\\n  transform: scale(1.1);\\n}\\n.products-slider-container[_ngcontent-%COMP%]   .products-slider-wrapper[_ngcontent-%COMP%]   .products-slider[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]   .product-image-container[_ngcontent-%COMP%]   .quick-actions[_ngcontent-%COMP%]   .action-btn.like-btn.active[_ngcontent-%COMP%] {\\n  background: #ff6b6b;\\n  color: white;\\n}\\n.products-slider-container[_ngcontent-%COMP%]   .products-slider-wrapper[_ngcontent-%COMP%]   .products-slider[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]   .product-image-container[_ngcontent-%COMP%]   .quick-actions[_ngcontent-%COMP%]   .action-btn.cart-btn[_ngcontent-%COMP%]:hover {\\n  background: #6c5ce7;\\n  color: white;\\n}\\n.products-slider-container[_ngcontent-%COMP%]   .products-slider-wrapper[_ngcontent-%COMP%]   .products-slider[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]   .product-image-container[_ngcontent-%COMP%]   .quick-actions[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n}\\n.products-slider-container[_ngcontent-%COMP%]   .products-slider-wrapper[_ngcontent-%COMP%]   .products-slider[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]   .product-image-container[_ngcontent-%COMP%]:hover   .quick-actions[_ngcontent-%COMP%] {\\n  opacity: 1;\\n}\\n.products-slider-container[_ngcontent-%COMP%]   .products-slider-wrapper[_ngcontent-%COMP%]   .products-slider[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]   .product-info[_ngcontent-%COMP%] {\\n  padding: 16px;\\n}\\n.products-slider-container[_ngcontent-%COMP%]   .products-slider-wrapper[_ngcontent-%COMP%]   .products-slider[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]   .product-info[_ngcontent-%COMP%]   .product-brand[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  color: #666;\\n  text-transform: uppercase;\\n  font-weight: 600;\\n  margin-bottom: 4px;\\n}\\n.products-slider-container[_ngcontent-%COMP%]   .products-slider-wrapper[_ngcontent-%COMP%]   .products-slider[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]   .product-info[_ngcontent-%COMP%]   .product-name[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  font-weight: 600;\\n  color: #333;\\n  margin: 0 0 8px 0;\\n  line-height: 1.3;\\n  display: -webkit-box;\\n  -webkit-line-clamp: 2;\\n  -webkit-box-orient: vertical;\\n  overflow: hidden;\\n}\\n.products-slider-container[_ngcontent-%COMP%]   .products-slider-wrapper[_ngcontent-%COMP%]   .products-slider[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]   .product-info[_ngcontent-%COMP%]   .price-section[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  margin-bottom: 8px;\\n}\\n.products-slider-container[_ngcontent-%COMP%]   .products-slider-wrapper[_ngcontent-%COMP%]   .products-slider[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]   .product-info[_ngcontent-%COMP%]   .price-section[_ngcontent-%COMP%]   .current-price[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  font-weight: 700;\\n  color: #333;\\n}\\n.products-slider-container[_ngcontent-%COMP%]   .products-slider-wrapper[_ngcontent-%COMP%]   .products-slider[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]   .product-info[_ngcontent-%COMP%]   .price-section[_ngcontent-%COMP%]   .original-price[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  color: #999;\\n  text-decoration: line-through;\\n}\\n.products-slider-container[_ngcontent-%COMP%]   .products-slider-wrapper[_ngcontent-%COMP%]   .products-slider[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]   .product-info[_ngcontent-%COMP%]   .rating-section[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 6px;\\n  margin-bottom: 8px;\\n}\\n.products-slider-container[_ngcontent-%COMP%]   .products-slider-wrapper[_ngcontent-%COMP%]   .products-slider[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]   .product-info[_ngcontent-%COMP%]   .rating-section[_ngcontent-%COMP%]   .stars[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 2px;\\n}\\n.products-slider-container[_ngcontent-%COMP%]   .products-slider-wrapper[_ngcontent-%COMP%]   .products-slider[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]   .product-info[_ngcontent-%COMP%]   .rating-section[_ngcontent-%COMP%]   .stars[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  color: #ddd;\\n}\\n.products-slider-container[_ngcontent-%COMP%]   .products-slider-wrapper[_ngcontent-%COMP%]   .products-slider[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]   .product-info[_ngcontent-%COMP%]   .rating-section[_ngcontent-%COMP%]   .stars[_ngcontent-%COMP%]   ion-icon.filled[_ngcontent-%COMP%] {\\n  color: #ffd700;\\n}\\n.products-slider-container[_ngcontent-%COMP%]   .products-slider-wrapper[_ngcontent-%COMP%]   .products-slider[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]   .product-info[_ngcontent-%COMP%]   .rating-section[_ngcontent-%COMP%]   .rating-text[_ngcontent-%COMP%] {\\n  font-size: 11px;\\n  color: #666;\\n}\\n.products-slider-container[_ngcontent-%COMP%]   .products-slider-wrapper[_ngcontent-%COMP%]   .products-slider[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]   .product-info[_ngcontent-%COMP%]   .trending-stats[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n}\\n.products-slider-container[_ngcontent-%COMP%]   .products-slider-wrapper[_ngcontent-%COMP%]   .products-slider[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]   .product-info[_ngcontent-%COMP%]   .trending-stats[_ngcontent-%COMP%]   .stat-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 4px;\\n  font-size: 10px;\\n  color: #666;\\n}\\n.products-slider-container[_ngcontent-%COMP%]   .products-slider-wrapper[_ngcontent-%COMP%]   .products-slider[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]   .product-info[_ngcontent-%COMP%]   .trending-stats[_ngcontent-%COMP%]   .stat-item[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  color: #6c5ce7;\\n}\\n.products-slider-container[_ngcontent-%COMP%]   .slider-indicators[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: center;\\n  gap: 8px;\\n  margin-top: 16px;\\n}\\n.products-slider-container[_ngcontent-%COMP%]   .slider-indicators[_ngcontent-%COMP%]   .indicator[_ngcontent-%COMP%] {\\n  width: 8px;\\n  height: 8px;\\n  border-radius: 50%;\\n  border: none;\\n  background: #ddd;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n}\\n.products-slider-container[_ngcontent-%COMP%]   .slider-indicators[_ngcontent-%COMP%]   .indicator.active[_ngcontent-%COMP%] {\\n  background: #6c5ce7;\\n  transform: scale(1.2);\\n}\\n.products-slider-container[_ngcontent-%COMP%]   .slider-indicators[_ngcontent-%COMP%]   .indicator[_ngcontent-%COMP%]:hover {\\n  background: #a29bfe;\\n}\\n\\n@media (max-width: 1024px) {\\n  .products-slider-container[_ngcontent-%COMP%]   .products-slider-wrapper[_ngcontent-%COMP%]   .products-slider[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%] {\\n    width: 240px;\\n  }\\n  .products-slider-container[_ngcontent-%COMP%]   .products-slider-wrapper[_ngcontent-%COMP%]   .products-slider[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]   .product-image-container[_ngcontent-%COMP%] {\\n    height: 180px;\\n  }\\n  .products-slider-container[_ngcontent-%COMP%]   .products-slider-wrapper[_ngcontent-%COMP%]   .products-slider[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]   .product-info[_ngcontent-%COMP%] {\\n    padding: 12px;\\n  }\\n  .products-slider-container[_ngcontent-%COMP%]   .products-slider-wrapper[_ngcontent-%COMP%]   .products-slider[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]   .product-info[_ngcontent-%COMP%]   .product-name[_ngcontent-%COMP%] {\\n    font-size: 13px;\\n  }\\n  .products-slider-container[_ngcontent-%COMP%]   .products-slider-wrapper[_ngcontent-%COMP%]   .products-slider[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]   .product-info[_ngcontent-%COMP%]   .price-section[_ngcontent-%COMP%]   .current-price[_ngcontent-%COMP%] {\\n    font-size: 15px;\\n  }\\n}\\n@media (max-width: 768px) {\\n  .products-slider-container[_ngcontent-%COMP%]   .slider-navigation[_ngcontent-%COMP%]   .nav-btn[_ngcontent-%COMP%] {\\n    width: 36px;\\n    height: 36px;\\n  }\\n  .products-slider-container[_ngcontent-%COMP%]   .slider-navigation[_ngcontent-%COMP%]   .nav-btn.prev-btn[_ngcontent-%COMP%] {\\n    left: -18px;\\n  }\\n  .products-slider-container[_ngcontent-%COMP%]   .slider-navigation[_ngcontent-%COMP%]   .nav-btn.next-btn[_ngcontent-%COMP%] {\\n    right: -18px;\\n  }\\n  .products-slider-container[_ngcontent-%COMP%]   .slider-navigation[_ngcontent-%COMP%]   .nav-btn[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n    font-size: 18px;\\n  }\\n  .products-slider-container[_ngcontent-%COMP%]   .products-slider-wrapper[_ngcontent-%COMP%] {\\n    margin: 0 15px;\\n  }\\n  .products-slider-container[_ngcontent-%COMP%]   .products-slider-wrapper[_ngcontent-%COMP%]   .products-slider[_ngcontent-%COMP%] {\\n    gap: 12px;\\n  }\\n  .products-slider-container[_ngcontent-%COMP%]   .products-slider-wrapper[_ngcontent-%COMP%]   .products-slider[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%] {\\n    width: 220px;\\n  }\\n  .products-slider-container[_ngcontent-%COMP%]   .products-slider-wrapper[_ngcontent-%COMP%]   .products-slider[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]   .product-image-container[_ngcontent-%COMP%] {\\n    height: 160px;\\n  }\\n  .products-slider-container[_ngcontent-%COMP%]   .products-slider-wrapper[_ngcontent-%COMP%]   .products-slider[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]   .product-image-container[_ngcontent-%COMP%]   .quick-actions[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%] {\\n    width: 28px;\\n    height: 28px;\\n  }\\n  .products-slider-container[_ngcontent-%COMP%]   .products-slider-wrapper[_ngcontent-%COMP%]   .products-slider[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]   .product-image-container[_ngcontent-%COMP%]   .quick-actions[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n    font-size: 14px;\\n  }\\n  .products-slider-container[_ngcontent-%COMP%]   .products-slider-wrapper[_ngcontent-%COMP%]   .products-slider[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]   .product-info[_ngcontent-%COMP%] {\\n    padding: 10px;\\n  }\\n  .products-slider-container[_ngcontent-%COMP%]   .products-slider-wrapper[_ngcontent-%COMP%]   .products-slider[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]   .product-info[_ngcontent-%COMP%]   .product-name[_ngcontent-%COMP%] {\\n    font-size: 12px;\\n  }\\n  .products-slider-container[_ngcontent-%COMP%]   .products-slider-wrapper[_ngcontent-%COMP%]   .products-slider[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]   .product-info[_ngcontent-%COMP%]   .price-section[_ngcontent-%COMP%]   .current-price[_ngcontent-%COMP%] {\\n    font-size: 14px;\\n  }\\n  .products-slider-container[_ngcontent-%COMP%]   .products-slider-wrapper[_ngcontent-%COMP%]   .products-slider[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]   .product-info[_ngcontent-%COMP%]   .price-section[_ngcontent-%COMP%]   .original-price[_ngcontent-%COMP%] {\\n    font-size: 11px;\\n  }\\n  .products-slider-container[_ngcontent-%COMP%]   .products-slider-wrapper[_ngcontent-%COMP%]   .products-slider[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]   .product-info[_ngcontent-%COMP%]   .rating-section[_ngcontent-%COMP%]   .stars[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n    font-size: 11px;\\n  }\\n  .products-slider-container[_ngcontent-%COMP%]   .products-slider-wrapper[_ngcontent-%COMP%]   .products-slider[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]   .product-info[_ngcontent-%COMP%]   .rating-section[_ngcontent-%COMP%]   .rating-text[_ngcontent-%COMP%] {\\n    font-size: 10px;\\n  }\\n  .products-slider-container[_ngcontent-%COMP%]   .products-slider-wrapper[_ngcontent-%COMP%]   .products-slider[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]   .product-info[_ngcontent-%COMP%]   .trending-stats[_ngcontent-%COMP%]   .stat-item[_ngcontent-%COMP%] {\\n    font-size: 9px;\\n  }\\n  .products-slider-container[_ngcontent-%COMP%]   .products-slider-wrapper[_ngcontent-%COMP%]   .products-slider[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]   .product-info[_ngcontent-%COMP%]   .trending-stats[_ngcontent-%COMP%]   .stat-item[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n    font-size: 11px;\\n  }\\n  .products-slider-container[_ngcontent-%COMP%]   .slider-indicators[_ngcontent-%COMP%] {\\n    margin-top: 12px;\\n  }\\n  .products-slider-container[_ngcontent-%COMP%]   .slider-indicators[_ngcontent-%COMP%]   .indicator[_ngcontent-%COMP%] {\\n    width: 6px;\\n    height: 6px;\\n  }\\n}\\n@media (max-width: 425px) {\\n  .products-slider-container[_ngcontent-%COMP%]   .slider-navigation[_ngcontent-%COMP%]   .nav-btn[_ngcontent-%COMP%] {\\n    width: 32px;\\n    height: 32px;\\n  }\\n  .products-slider-container[_ngcontent-%COMP%]   .slider-navigation[_ngcontent-%COMP%]   .nav-btn.prev-btn[_ngcontent-%COMP%] {\\n    left: -16px;\\n  }\\n  .products-slider-container[_ngcontent-%COMP%]   .slider-navigation[_ngcontent-%COMP%]   .nav-btn.next-btn[_ngcontent-%COMP%] {\\n    right: -16px;\\n  }\\n  .products-slider-container[_ngcontent-%COMP%]   .slider-navigation[_ngcontent-%COMP%]   .nav-btn[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n    font-size: 16px;\\n  }\\n  .products-slider-container[_ngcontent-%COMP%]   .products-slider-wrapper[_ngcontent-%COMP%] {\\n    margin: 0 10px;\\n  }\\n  .products-slider-container[_ngcontent-%COMP%]   .products-slider-wrapper[_ngcontent-%COMP%]   .products-slider[_ngcontent-%COMP%] {\\n    gap: 8px;\\n  }\\n  .products-slider-container[_ngcontent-%COMP%]   .products-slider-wrapper[_ngcontent-%COMP%]   .products-slider[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%] {\\n    width: 200px;\\n  }\\n  .products-slider-container[_ngcontent-%COMP%]   .products-slider-wrapper[_ngcontent-%COMP%]   .products-slider[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]   .product-image-container[_ngcontent-%COMP%] {\\n    height: 140px;\\n  }\\n  .products-slider-container[_ngcontent-%COMP%]   .products-slider-wrapper[_ngcontent-%COMP%]   .products-slider[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]   .product-info[_ngcontent-%COMP%] {\\n    padding: 8px;\\n  }\\n  .products-slider-container[_ngcontent-%COMP%]   .products-slider-wrapper[_ngcontent-%COMP%]   .products-slider[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]   .product-info[_ngcontent-%COMP%]   .product-name[_ngcontent-%COMP%] {\\n    font-size: 11px;\\n  }\\n  .products-slider-container[_ngcontent-%COMP%]   .products-slider-wrapper[_ngcontent-%COMP%]   .products-slider[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]   .product-info[_ngcontent-%COMP%]   .price-section[_ngcontent-%COMP%]   .current-price[_ngcontent-%COMP%] {\\n    font-size: 13px;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "RouterModule", "IonicModule", "Subscription", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵtext", "ɵɵelementEnd", "ɵɵlistener", "TrendingProductSlidersComponent_div_9_Template_button_click_4_listener", "ɵɵrestoreView", "_r1", "ctx_r1", "ɵɵnextContext", "ɵɵresetView", "onRetry", "ɵɵadvance", "ɵɵtextInterpolate", "error", "ɵɵtextInterpolate1", "getDiscountPercentage", "product_r5", "formatPrice", "originalPrice", "ɵɵclassProp", "star_r6", "rating", "average", "ɵɵproperty", "TrendingProductSlidersComponent_div_10_div_8_Template_div_click_0_listener", "_r4", "$implicit", "onProductClick", "ɵɵtemplate", "TrendingProductSlidersComponent_div_10_div_8_div_6_Template", "TrendingProductSlidersComponent_div_10_div_8_Template_button_click_8_listener", "$event", "toggleLike", "TrendingProductSlidersComponent_div_10_div_8_Template_button_click_10_listener", "addToCart", "TrendingProductSlidersComponent_div_10_div_8_span_20_Template", "TrendingProductSlidersComponent_div_10_div_8_ion_icon_23_Template", "images", "url", "ɵɵsanitizeUrl", "alt", "name", "isProductLiked", "_id", "brand", "price", "ɵɵpureFunction0", "_c0", "count", "formatCount", "analytics", "views", "purchases", "TrendingProductSlidersComponent_div_10_button_10_Template_button_click_0_listener", "i_r8", "_r7", "index", "goToSlide", "currentSlide", "TrendingProductSlidersComponent_div_10_Template_button_click_2_listener", "_r3", "previousSlide", "TrendingProductSlidersComponent_div_10_Template_button_click_4_listener", "nextSlide", "TrendingProductSlidersComponent_div_10_Template_div_mouseenter_6_listener", "pauseAutoSlide", "TrendingProductSlidersComponent_div_10_Template_div_mouseleave_6_listener", "resumeAutoSlide", "TrendingProductSlidersComponent_div_10_div_8_Template", "TrendingProductSlidersComponent_div_10_button_10_Template", "maxSlide", "ɵɵstyleProp", "slideOffset", "trendingProducts", "trackByProductId", "getSlideIndicators", "TrendingProductSlidersComponent", "constructor", "router", "likedProducts", "Set", "isLoading", "subscription", "<PERSON><PERSON><PERSON><PERSON>", "visibleCards", "autoSlideDelay", "isAutoSliding", "isPaused", "ngOnInit", "loadTrendingProducts", "updateResponsiveSettings", "setupResizeListener", "ngOnDestroy", "unsubscribe", "stopAutoSlide", "_this", "_asyncToGenerator", "loadMockProducts", "console", "_this2", "Promise", "resolve", "setTimeout", "likes", "shares", "isActive", "isFeatured", "createdAt", "Date", "updateSliderOnProductsLoad", "calculateMaxSlide", "updateSlidePosition", "startAutoSlide", "width", "window", "innerWidth", "addEventListener", "Math", "max", "length", "slideIndex", "min", "Array", "fill", "map", "_", "i", "autoSlideInterval", "setInterval", "autoSlideNext", "clearInterval", "product", "navigate", "event", "stopPropagation", "has", "delete", "add", "log", "productId", "round", "Intl", "NumberFormat", "style", "currency", "minimumFractionDigits", "format", "toFixed", "toString", "onSeeAll", "preventDefault", "ɵɵdirectiveInject", "i1", "Router", "selectors", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "TrendingProductSlidersComponent_Template", "rf", "ctx", "TrendingProductSlidersComponent_Template_a_click_5_listener", "TrendingProductSlidersComponent_div_8_Template", "TrendingProductSlidersComponent_div_9_Template", "TrendingProductSlidersComponent_div_10_Template", "TrendingProductSlidersComponent_div_11_Template", "i2", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "i3", "IonIcon", "Ion<PERSON><PERSON><PERSON>", "styles"], "sources": ["E:\\Fashion\\DFashionFrontend\\frontend\\src\\app\\features\\home\\components\\trending-now\\trending-product-sliders\\trending-product-sliders.component.ts", "E:\\Fashion\\DFashionFrontend\\frontend\\src\\app\\features\\home\\components\\trending-now\\trending-product-sliders\\trending-product-sliders.component.html"], "sourcesContent": ["import { Component, OnInit, OnDestroy } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\nimport { IonicModule } from '@ionic/angular';\nimport { Router } from '@angular/router';\nimport { Subscription } from 'rxjs';\n\ninterface Product {\n  _id: string;\n  name: string;\n  brand: string;\n  price: number;\n  originalPrice?: number;\n  images: Array<{\n    url: string;\n    alt?: string;\n  }>;\n  rating: {\n    average: number;\n    count: number;\n  };\n  analytics: {\n    views: number;\n    likes: number;\n    purchases: number;\n    shares: number;\n  };\n  isActive: boolean;\n  isFeatured: boolean;\n  createdAt: Date;\n}\n\n@Component({\n  selector: 'app-trending-product-sliders',\n  standalone: true,\n  imports: [\n    CommonModule,\n    RouterModule,\n    IonicModule\n  ],\n  templateUrl: './trending-product-sliders.component.html',\n  styleUrls: ['./trending-product-sliders.component.scss']\n})\nexport class TrendingProductSlidersComponent implements OnInit, OnDestroy {\n  trendingProducts: Product[] = [];\n  likedProducts = new Set<string>();\n  isLoading = true;\n  error: string | null = null;\n  private subscription: Subscription = new Subscription();\n\n  // Slider properties\n  currentSlide = 0;\n  slideOffset = 0;\n  cardWidth = 280; // Width of each product card including margin\n  visibleCards = 4; // Number of cards visible at once\n  maxSlide = 0;\n\n  // Auto-sliding properties\n  autoSlideInterval: any;\n  autoSlideDelay = 3000; // 3 seconds\n  isAutoSliding = true;\n  isPaused = false;\n\n  constructor(private router: Router) {}\n\n  ngOnInit() {\n    this.loadTrendingProducts();\n    this.updateResponsiveSettings();\n    this.setupResizeListener();\n  }\n\n  ngOnDestroy() {\n    this.subscription.unsubscribe();\n    this.stopAutoSlide();\n  }\n\n  private async loadTrendingProducts() {\n    try {\n      this.isLoading = true;\n      this.error = null;\n      \n      // Mock data for now - replace with actual service call\n      await this.loadMockProducts();\n      \n    } catch (error) {\n      console.error('Error loading trending products:', error);\n      this.error = 'Failed to load trending products';\n      this.isLoading = false;\n    }\n  }\n\n  private async loadMockProducts() {\n    // Simulate API call delay\n    await new Promise(resolve => setTimeout(resolve, 800));\n\n    this.trendingProducts = [\n      {\n        _id: 'prod1',\n        name: 'Trendy Summer Dress',\n        brand: 'Fashion Co.',\n        price: 2999,\n        originalPrice: 3999,\n        images: [\n          { url: 'https://images.unsplash.com/photo-1515372039744-b8f02a3ae446?w=300&h=400&fit=crop', alt: 'Summer Dress' }\n        ],\n        rating: { average: 4.5, count: 128 },\n        analytics: { views: 2340, likes: 189, purchases: 67, shares: 23 },\n        isActive: true,\n        isFeatured: true,\n        createdAt: new Date()\n      },\n      {\n        _id: 'prod2',\n        name: 'Casual Sneakers',\n        brand: 'SportWear',\n        price: 4999,\n        originalPrice: 6999,\n        images: [\n          { url: 'https://images.unsplash.com/photo-1542291026-7eec264c27ff?w=300&h=400&fit=crop', alt: 'Sneakers' }\n        ],\n        rating: { average: 4.7, count: 256 },\n        analytics: { views: 3450, likes: 278, purchases: 89, shares: 34 },\n        isActive: true,\n        isFeatured: true,\n        createdAt: new Date()\n      },\n      {\n        _id: 'prod3',\n        name: 'Designer Handbag',\n        brand: 'Luxury Brand',\n        price: 8999,\n        images: [\n          { url: 'https://images.unsplash.com/photo-1553062407-98eeb64c6a62?w=300&h=400&fit=crop', alt: 'Handbag' }\n        ],\n        rating: { average: 4.3, count: 89 },\n        analytics: { views: 1890, likes: 145, purchases: 34, shares: 12 },\n        isActive: true,\n        isFeatured: true,\n        createdAt: new Date()\n      },\n      {\n        _id: 'prod4',\n        name: 'Stylish Sunglasses',\n        brand: 'Eye Fashion',\n        price: 1999,\n        originalPrice: 2999,\n        images: [\n          { url: 'https://images.unsplash.com/photo-1572635196237-14b3f281503f?w=300&h=400&fit=crop', alt: 'Sunglasses' }\n        ],\n        rating: { average: 4.6, count: 167 },\n        analytics: { views: 2780, likes: 234, purchases: 78, shares: 19 },\n        isActive: true,\n        isFeatured: true,\n        createdAt: new Date()\n      },\n      {\n        _id: 'prod5',\n        name: 'Elegant Watch',\n        brand: 'TimeKeeper',\n        price: 12999,\n        originalPrice: 15999,\n        images: [\n          { url: 'https://images.unsplash.com/photo-1523275335684-37898b6baf30?w=300&h=400&fit=crop', alt: 'Watch' }\n        ],\n        rating: { average: 4.8, count: 203 },\n        analytics: { views: 4560, likes: 389, purchases: 123, shares: 45 },\n        isActive: true,\n        isFeatured: true,\n        createdAt: new Date()\n      }\n    ];\n\n    this.updateSliderOnProductsLoad();\n    this.isLoading = false;\n  }\n\n  private updateSliderOnProductsLoad() {\n    this.calculateMaxSlide();\n    this.currentSlide = 0;\n    this.updateSlidePosition();\n    this.startAutoSlide();\n  }\n\n  private updateResponsiveSettings() {\n    const width = window.innerWidth;\n    if (width <= 425) {\n      this.visibleCards = 1;\n      this.cardWidth = 260;\n    } else if (width <= 768) {\n      this.visibleCards = 2;\n      this.cardWidth = 270;\n    } else if (width <= 1024) {\n      this.visibleCards = 3;\n      this.cardWidth = 280;\n    } else {\n      this.visibleCards = 4;\n      this.cardWidth = 280;\n    }\n    this.calculateMaxSlide();\n    this.updateSlidePosition();\n  }\n\n  private setupResizeListener() {\n    window.addEventListener('resize', () => {\n      this.updateResponsiveSettings();\n    });\n  }\n\n  private calculateMaxSlide() {\n    this.maxSlide = Math.max(0, this.trendingProducts.length - this.visibleCards);\n  }\n\n  private updateSlidePosition() {\n    this.slideOffset = -this.currentSlide * this.cardWidth;\n  }\n\n  // Slider navigation methods\n  nextSlide() {\n    if (this.currentSlide < this.maxSlide) {\n      this.currentSlide++;\n      this.updateSlidePosition();\n    }\n  }\n\n  previousSlide() {\n    if (this.currentSlide > 0) {\n      this.currentSlide--;\n      this.updateSlidePosition();\n    }\n  }\n\n  goToSlide(slideIndex: number) {\n    this.currentSlide = Math.max(0, Math.min(slideIndex, this.maxSlide));\n    this.updateSlidePosition();\n  }\n\n  getSlideIndicators(): number[] {\n    return Array(this.maxSlide + 1).fill(0).map((_, i) => i);\n  }\n\n  // Auto-sliding methods\n  private startAutoSlide() {\n    if (!this.isAutoSliding || this.isPaused) return;\n\n    this.stopAutoSlide();\n    this.autoSlideInterval = setInterval(() => {\n      if (!this.isPaused && this.trendingProducts.length > this.visibleCards) {\n        this.autoSlideNext();\n      }\n    }, this.autoSlideDelay);\n  }\n\n  private autoSlideNext() {\n    if (this.currentSlide >= this.maxSlide) {\n      this.currentSlide = 0;\n    } else {\n      this.currentSlide++;\n    }\n    this.updateSlidePosition();\n  }\n\n  private stopAutoSlide() {\n    if (this.autoSlideInterval) {\n      clearInterval(this.autoSlideInterval);\n      this.autoSlideInterval = null;\n    }\n  }\n\n  pauseAutoSlide() {\n    this.isPaused = true;\n  }\n\n  resumeAutoSlide() {\n    this.isPaused = false;\n    this.startAutoSlide();\n  }\n\n  // Product interaction methods\n  onProductClick(product: Product) {\n    this.router.navigate(['/product', product._id]);\n  }\n\n  toggleLike(product: Product, event: Event) {\n    event.stopPropagation();\n    \n    if (this.likedProducts.has(product._id)) {\n      this.likedProducts.delete(product._id);\n      product.analytics.likes--;\n    } else {\n      this.likedProducts.add(product._id);\n      product.analytics.likes++;\n    }\n  }\n\n  addToCart(product: Product, event: Event) {\n    event.stopPropagation();\n    // Add to cart logic here\n    console.log('Added to cart:', product.name);\n  }\n\n  isProductLiked(productId: string): boolean {\n    return this.likedProducts.has(productId);\n  }\n\n  getDiscountPercentage(product: Product): number {\n    if (product.originalPrice && product.originalPrice > product.price) {\n      return Math.round(((product.originalPrice - product.price) / product.originalPrice) * 100);\n    }\n    return 0;\n  }\n\n  formatPrice(price: number): string {\n    return new Intl.NumberFormat('en-IN', {\n      style: 'currency',\n      currency: 'INR',\n      minimumFractionDigits: 0\n    }).format(price);\n  }\n\n  formatCount(count: number): string {\n    if (count >= 1000) {\n      return (count / 1000).toFixed(1) + 'K';\n    }\n    return count.toString();\n  }\n\n  trackByProductId(index: number, product: Product): string {\n    return product._id;\n  }\n\n  onSeeAll(event: Event) {\n    event.preventDefault();\n    this.router.navigate(['/trending/products']);\n  }\n\n  onRetry() {\n    this.loadTrendingProducts();\n  }\n}\n", "<div class=\"component-container\">\n  <!-- Component Header -->\n  <div class=\"component-header\">\n    <h3>\n      <ion-icon name=\"storefront-outline\"></ion-icon>\n      Trending Products\n    </h3>\n    <a href=\"#\" class=\"see-all\" (click)=\"onSeeAll($event)\">See All</a>\n  </div>\n\n  <!-- Component Content -->\n  <div class=\"component-content\">\n    <!-- Loading State -->\n    <div *ngIf=\"isLoading\" class=\"loading-container\">\n      <ion-spinner name=\"crescent\"></ion-spinner>\n      <p>Loading trending products...</p>\n    </div>\n\n    <!-- Error State -->\n    <div *ngIf=\"error && !isLoading\" class=\"error-container\">\n      <ion-icon name=\"alert-circle-outline\"></ion-icon>\n      <p>{{ error }}</p>\n      <button class=\"retry-btn\" (click)=\"onRetry()\">Try Again</button>\n    </div>\n\n    <!-- Products Slider -->\n    <div *ngIf=\"!isLoading && !error && trendingProducts.length > 0\" class=\"products-slider-container\">\n      <!-- Slider Navigation -->\n      <div class=\"slider-navigation\">\n        <button \n          class=\"nav-btn prev-btn\" \n          [disabled]=\"currentSlide === 0\"\n          (click)=\"previousSlide()\"\n        >\n          <ion-icon name=\"chevron-back\"></ion-icon>\n        </button>\n        <button \n          class=\"nav-btn next-btn\" \n          [disabled]=\"currentSlide >= maxSlide\"\n          (click)=\"nextSlide()\"\n        >\n          <ion-icon name=\"chevron-forward\"></ion-icon>\n        </button>\n      </div>\n\n      <!-- Slider Wrapper -->\n      <div class=\"products-slider-wrapper\" (mouseenter)=\"pauseAutoSlide()\" (mouseleave)=\"resumeAutoSlide()\">\n        <div class=\"products-slider\" [style.transform]=\"'translateX(' + slideOffset + 'px)'\">\n          <div\n            *ngFor=\"let product of trendingProducts; trackBy: trackByProductId\"\n            class=\"product-card\"\n            (click)=\"onProductClick(product)\"\n          >\n            <!-- Product Image -->\n            <div class=\"product-image-container\">\n              <img \n                [src]=\"product.images[0].url\"\n                [alt]=\"product.images[0].alt || product.name\"\n                class=\"product-image\"\n                loading=\"lazy\"\n              />\n              \n              <!-- Trending Badge -->\n              <div class=\"trending-badge\">\n                <ion-icon name=\"trending-up\"></ion-icon>\n                Trending\n              </div>\n\n              <!-- Discount Badge -->\n              <div *ngIf=\"getDiscountPercentage(product) > 0\" class=\"discount-badge\">\n                {{ getDiscountPercentage(product) }}% OFF\n              </div>\n\n              <!-- Quick Actions -->\n              <div class=\"quick-actions\">\n                <button \n                  class=\"action-btn like-btn\"\n                  [class.active]=\"isProductLiked(product._id)\"\n                  (click)=\"toggleLike(product, $event)\"\n                >\n                  <ion-icon [name]=\"isProductLiked(product._id) ? 'heart' : 'heart-outline'\"></ion-icon>\n                </button>\n                <button \n                  class=\"action-btn cart-btn\"\n                  (click)=\"addToCart(product, $event)\"\n                >\n                  <ion-icon name=\"bag-add-outline\"></ion-icon>\n                </button>\n              </div>\n            </div>\n\n            <!-- Product Info -->\n            <div class=\"product-info\">\n              <div class=\"product-brand\">{{ product.brand }}</div>\n              <h4 class=\"product-name\">{{ product.name }}</h4>\n              \n              <!-- Price Section -->\n              <div class=\"price-section\">\n                <span class=\"current-price\">{{ formatPrice(product.price) }}</span>\n                <span *ngIf=\"product.originalPrice && product.originalPrice > product.price\" \n                      class=\"original-price\">{{ formatPrice(product.originalPrice) }}</span>\n              </div>\n\n              <!-- Rating Section -->\n              <div class=\"rating-section\">\n                <div class=\"stars\">\n                  <ion-icon \n                    *ngFor=\"let star of [1,2,3,4,5]\" \n                    [name]=\"star <= product.rating.average ? 'star' : 'star-outline'\"\n                    [class.filled]=\"star <= product.rating.average\"\n                  ></ion-icon>\n                </div>\n                <span class=\"rating-text\">({{ product.rating.count }})</span>\n              </div>\n\n              <!-- Trending Stats -->\n              <div class=\"trending-stats\">\n                <div class=\"stat-item\">\n                  <ion-icon name=\"eye-outline\"></ion-icon>\n                  <span>{{ formatCount(product.analytics.views) }}</span>\n                </div>\n                <div class=\"stat-item\">\n                  <ion-icon name=\"bag-outline\"></ion-icon>\n                  <span>{{ formatCount(product.analytics.purchases) }}</span>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <!-- Slider Indicators -->\n      <div class=\"slider-indicators\">\n        <button \n          *ngFor=\"let indicator of getSlideIndicators(); let i = index\"\n          class=\"indicator\"\n          [class.active]=\"i === currentSlide\"\n          (click)=\"goToSlide(i)\"\n        ></button>\n      </div>\n    </div>\n\n    <!-- Empty State -->\n    <div *ngIf=\"!isLoading && !error && trendingProducts.length === 0\" class=\"empty-container\">\n      <ion-icon name=\"storefront-outline\"></ion-icon>\n      <p>No trending products available</p>\n    </div>\n  </div>\n</div>\n"], "mappings": ";AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,QAAQ,gBAAgB;AAE5C,SAASC,YAAY,QAAQ,MAAM;;;;;;;;ICQ/BC,EAAA,CAAAC,cAAA,aAAiD;IAC/CD,EAAA,CAAAE,SAAA,sBAA2C;IAC3CF,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAG,MAAA,mCAA4B;IACjCH,EADiC,CAAAI,YAAA,EAAI,EAC/B;;;;;;IAGNJ,EAAA,CAAAC,cAAA,cAAyD;IACvDD,EAAA,CAAAE,SAAA,mBAAiD;IACjDF,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAG,MAAA,GAAW;IAAAH,EAAA,CAAAI,YAAA,EAAI;IAClBJ,EAAA,CAAAC,cAAA,iBAA8C;IAApBD,EAAA,CAAAK,UAAA,mBAAAC,uEAAA;MAAAN,EAAA,CAAAO,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAG,OAAA,EAAS;IAAA,EAAC;IAACZ,EAAA,CAAAG,MAAA,gBAAS;IACzDH,EADyD,CAAAI,YAAA,EAAS,EAC5D;;;;IAFDJ,EAAA,CAAAa,SAAA,GAAW;IAAXb,EAAA,CAAAc,iBAAA,CAAAL,MAAA,CAAAM,KAAA,CAAW;;;;;IAgDNf,EAAA,CAAAC,cAAA,cAAuE;IACrED,EAAA,CAAAG,MAAA,GACF;IAAAH,EAAA,CAAAI,YAAA,EAAM;;;;;IADJJ,EAAA,CAAAa,SAAA,EACF;IADEb,EAAA,CAAAgB,kBAAA,MAAAP,MAAA,CAAAQ,qBAAA,CAAAC,UAAA,YACF;;;;;IA4BElB,EAAA,CAAAC,cAAA,eAC6B;IAAAD,EAAA,CAAAG,MAAA,GAAwC;IAAAH,EAAA,CAAAI,YAAA,EAAO;;;;;IAA/CJ,EAAA,CAAAa,SAAA,EAAwC;IAAxCb,EAAA,CAAAc,iBAAA,CAAAL,MAAA,CAAAU,WAAA,CAAAD,UAAA,CAAAE,aAAA,EAAwC;;;;;IAMnEpB,EAAA,CAAAE,SAAA,mBAIY;;;;;IADVF,EAAA,CAAAqB,WAAA,WAAAC,OAAA,IAAAJ,UAAA,CAAAK,MAAA,CAAAC,OAAA,CAA+C;IAD/CxB,EAAA,CAAAyB,UAAA,SAAAH,OAAA,IAAAJ,UAAA,CAAAK,MAAA,CAAAC,OAAA,2BAAiE;;;;;;IA5D3ExB,EAAA,CAAAC,cAAA,cAIC;IADCD,EAAA,CAAAK,UAAA,mBAAAqB,2EAAA;MAAA,MAAAR,UAAA,GAAAlB,EAAA,CAAAO,aAAA,CAAAoB,GAAA,EAAAC,SAAA;MAAA,MAAAnB,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAoB,cAAA,CAAAX,UAAA,CAAuB;IAAA,EAAC;IAGjClB,EAAA,CAAAC,cAAA,cAAqC;IACnCD,EAAA,CAAAE,SAAA,cAKE;IAGFF,EAAA,CAAAC,cAAA,cAA4B;IAC1BD,EAAA,CAAAE,SAAA,mBAAwC;IACxCF,EAAA,CAAAG,MAAA,iBACF;IAAAH,EAAA,CAAAI,YAAA,EAAM;IAGNJ,EAAA,CAAA8B,UAAA,IAAAC,2DAAA,kBAAuE;IAMrE/B,EADF,CAAAC,cAAA,cAA2B,iBAKxB;IADCD,EAAA,CAAAK,UAAA,mBAAA2B,8EAAAC,MAAA;MAAA,MAAAf,UAAA,GAAAlB,EAAA,CAAAO,aAAA,CAAAoB,GAAA,EAAAC,SAAA;MAAA,MAAAnB,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAyB,UAAA,CAAAhB,UAAA,EAAAe,MAAA,CAA2B;IAAA,EAAC;IAErCjC,EAAA,CAAAE,SAAA,mBAAsF;IACxFF,EAAA,CAAAI,YAAA,EAAS;IACTJ,EAAA,CAAAC,cAAA,kBAGC;IADCD,EAAA,CAAAK,UAAA,mBAAA8B,+EAAAF,MAAA;MAAA,MAAAf,UAAA,GAAAlB,EAAA,CAAAO,aAAA,CAAAoB,GAAA,EAAAC,SAAA;MAAA,MAAAnB,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAA2B,SAAA,CAAAlB,UAAA,EAAAe,MAAA,CAA0B;IAAA,EAAC;IAEpCjC,EAAA,CAAAE,SAAA,oBAA4C;IAGlDF,EAFI,CAAAI,YAAA,EAAS,EACL,EACF;IAIJJ,EADF,CAAAC,cAAA,eAA0B,eACG;IAAAD,EAAA,CAAAG,MAAA,IAAmB;IAAAH,EAAA,CAAAI,YAAA,EAAM;IACpDJ,EAAA,CAAAC,cAAA,cAAyB;IAAAD,EAAA,CAAAG,MAAA,IAAkB;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAI9CJ,EADF,CAAAC,cAAA,eAA2B,gBACG;IAAAD,EAAA,CAAAG,MAAA,IAAgC;IAAAH,EAAA,CAAAI,YAAA,EAAO;IACnEJ,EAAA,CAAA8B,UAAA,KAAAO,6DAAA,mBAC6B;IAC/BrC,EAAA,CAAAI,YAAA,EAAM;IAIJJ,EADF,CAAAC,cAAA,eAA4B,eACP;IACjBD,EAAA,CAAA8B,UAAA,KAAAQ,iEAAA,uBAIC;IACHtC,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAC,cAAA,gBAA0B;IAAAD,EAAA,CAAAG,MAAA,IAA4B;IACxDH,EADwD,CAAAI,YAAA,EAAO,EACzD;IAIJJ,EADF,CAAAC,cAAA,eAA4B,eACH;IACrBD,EAAA,CAAAE,SAAA,oBAAwC;IACxCF,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAG,MAAA,IAA0C;IAClDH,EADkD,CAAAI,YAAA,EAAO,EACnD;IACNJ,EAAA,CAAAC,cAAA,eAAuB;IACrBD,EAAA,CAAAE,SAAA,oBAAwC;IACxCF,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAG,MAAA,IAA8C;IAI5DH,EAJ4D,CAAAI,YAAA,EAAO,EACvD,EACF,EACF,EACF;;;;;IAvEAJ,EAAA,CAAAa,SAAA,GAA6B;IAC7Bb,EADA,CAAAyB,UAAA,QAAAP,UAAA,CAAAqB,MAAA,IAAAC,GAAA,EAAAxC,EAAA,CAAAyC,aAAA,CAA6B,QAAAvB,UAAA,CAAAqB,MAAA,IAAAG,GAAA,IAAAxB,UAAA,CAAAyB,IAAA,CACgB;IAYzC3C,EAAA,CAAAa,SAAA,GAAwC;IAAxCb,EAAA,CAAAyB,UAAA,SAAAhB,MAAA,CAAAQ,qBAAA,CAAAC,UAAA,MAAwC;IAQ1ClB,EAAA,CAAAa,SAAA,GAA4C;IAA5Cb,EAAA,CAAAqB,WAAA,WAAAZ,MAAA,CAAAmC,cAAA,CAAA1B,UAAA,CAAA2B,GAAA,EAA4C;IAGlC7C,EAAA,CAAAa,SAAA,EAAgE;IAAhEb,EAAA,CAAAyB,UAAA,SAAAhB,MAAA,CAAAmC,cAAA,CAAA1B,UAAA,CAAA2B,GAAA,8BAAgE;IAanD7C,EAAA,CAAAa,SAAA,GAAmB;IAAnBb,EAAA,CAAAc,iBAAA,CAAAI,UAAA,CAAA4B,KAAA,CAAmB;IACrB9C,EAAA,CAAAa,SAAA,GAAkB;IAAlBb,EAAA,CAAAc,iBAAA,CAAAI,UAAA,CAAAyB,IAAA,CAAkB;IAIb3C,EAAA,CAAAa,SAAA,GAAgC;IAAhCb,EAAA,CAAAc,iBAAA,CAAAL,MAAA,CAAAU,WAAA,CAAAD,UAAA,CAAA6B,KAAA,EAAgC;IACrD/C,EAAA,CAAAa,SAAA,EAAoE;IAApEb,EAAA,CAAAyB,UAAA,SAAAP,UAAA,CAAAE,aAAA,IAAAF,UAAA,CAAAE,aAAA,GAAAF,UAAA,CAAA6B,KAAA,CAAoE;IAQtD/C,EAAA,CAAAa,SAAA,GAAc;IAAdb,EAAA,CAAAyB,UAAA,YAAAzB,EAAA,CAAAgD,eAAA,KAAAC,GAAA,EAAc;IAKTjD,EAAA,CAAAa,SAAA,GAA4B;IAA5Bb,EAAA,CAAAgB,kBAAA,MAAAE,UAAA,CAAAK,MAAA,CAAA2B,KAAA,MAA4B;IAO9ClD,EAAA,CAAAa,SAAA,GAA0C;IAA1Cb,EAAA,CAAAc,iBAAA,CAAAL,MAAA,CAAA0C,WAAA,CAAAjC,UAAA,CAAAkC,SAAA,CAAAC,KAAA,EAA0C;IAI1CrD,EAAA,CAAAa,SAAA,GAA8C;IAA9Cb,EAAA,CAAAc,iBAAA,CAAAL,MAAA,CAAA0C,WAAA,CAAAjC,UAAA,CAAAkC,SAAA,CAAAE,SAAA,EAA8C;;;;;;IAU9DtD,EAAA,CAAAC,cAAA,iBAKC;IADCD,EAAA,CAAAK,UAAA,mBAAAkD,kFAAA;MAAA,MAAAC,IAAA,GAAAxD,EAAA,CAAAO,aAAA,CAAAkD,GAAA,EAAAC,KAAA;MAAA,MAAAjD,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAkD,SAAA,CAAAH,IAAA,CAAY;IAAA,EAAC;IACvBxD,EAAA,CAAAI,YAAA,EAAS;;;;;IAFRJ,EAAA,CAAAqB,WAAA,WAAAmC,IAAA,KAAA/C,MAAA,CAAAmD,YAAA,CAAmC;;;;;;IA3GrC5D,EAHJ,CAAAC,cAAA,cAAmG,cAElE,iBAK5B;IADCD,EAAA,CAAAK,UAAA,mBAAAwD,wEAAA;MAAA7D,EAAA,CAAAO,aAAA,CAAAuD,GAAA;MAAA,MAAArD,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAsD,aAAA,EAAe;IAAA,EAAC;IAEzB/D,EAAA,CAAAE,SAAA,mBAAyC;IAC3CF,EAAA,CAAAI,YAAA,EAAS;IACTJ,EAAA,CAAAC,cAAA,iBAIC;IADCD,EAAA,CAAAK,UAAA,mBAAA2D,wEAAA;MAAAhE,EAAA,CAAAO,aAAA,CAAAuD,GAAA;MAAA,MAAArD,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAwD,SAAA,EAAW;IAAA,EAAC;IAErBjE,EAAA,CAAAE,SAAA,mBAA4C;IAEhDF,EADE,CAAAI,YAAA,EAAS,EACL;IAGNJ,EAAA,CAAAC,cAAA,cAAsG;IAAjCD,EAAhC,CAAAK,UAAA,wBAAA6D,0EAAA;MAAAlE,EAAA,CAAAO,aAAA,CAAAuD,GAAA;MAAA,MAAArD,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAAcF,MAAA,CAAA0D,cAAA,EAAgB;IAAA,EAAC,wBAAAC,0EAAA;MAAApE,EAAA,CAAAO,aAAA,CAAAuD,GAAA;MAAA,MAAArD,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAAeF,MAAA,CAAA4D,eAAA,EAAiB;IAAA,EAAC;IACnGrE,EAAA,CAAAC,cAAA,cAAqF;IACnFD,EAAA,CAAA8B,UAAA,IAAAwC,qDAAA,oBAIC;IA6ELtE,EADE,CAAAI,YAAA,EAAM,EACF;IAGNJ,EAAA,CAAAC,cAAA,cAA+B;IAC7BD,EAAA,CAAA8B,UAAA,KAAAyC,yDAAA,qBAKC;IAELvE,EADE,CAAAI,YAAA,EAAM,EACF;;;;IA7GAJ,EAAA,CAAAa,SAAA,GAA+B;IAA/Bb,EAAA,CAAAyB,UAAA,aAAAhB,MAAA,CAAAmD,YAAA,OAA+B;IAO/B5D,EAAA,CAAAa,SAAA,GAAqC;IAArCb,EAAA,CAAAyB,UAAA,aAAAhB,MAAA,CAAAmD,YAAA,IAAAnD,MAAA,CAAA+D,QAAA,CAAqC;IASVxE,EAAA,CAAAa,SAAA,GAAuD;IAAvDb,EAAA,CAAAyE,WAAA,8BAAAhE,MAAA,CAAAiE,WAAA,SAAuD;IAE5D1E,EAAA,CAAAa,SAAA,EAAqB;IAAAb,EAArB,CAAAyB,UAAA,YAAAhB,MAAA,CAAAkE,gBAAA,CAAqB,iBAAAlE,MAAA,CAAAmE,gBAAA,CAAyB;IAqF9C5E,EAAA,CAAAa,SAAA,GAAyB;IAAzBb,EAAA,CAAAyB,UAAA,YAAAhB,MAAA,CAAAoE,kBAAA,GAAyB;;;;;IASrD7E,EAAA,CAAAC,cAAA,cAA2F;IACzFD,EAAA,CAAAE,SAAA,kBAA+C;IAC/CF,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAG,MAAA,qCAA8B;IACnCH,EADmC,CAAAI,YAAA,EAAI,EACjC;;;ADvGV,OAAM,MAAO0E,+BAA+B;EAoB1CC,YAAoBC,MAAc;IAAd,KAAAA,MAAM,GAANA,MAAM;IAnB1B,KAAAL,gBAAgB,GAAc,EAAE;IAChC,KAAAM,aAAa,GAAG,IAAIC,GAAG,EAAU;IACjC,KAAAC,SAAS,GAAG,IAAI;IAChB,KAAApE,KAAK,GAAkB,IAAI;IACnB,KAAAqE,YAAY,GAAiB,IAAIrF,YAAY,EAAE;IAEvD;IACA,KAAA6D,YAAY,GAAG,CAAC;IAChB,KAAAc,WAAW,GAAG,CAAC;IACf,KAAAW,SAAS,GAAG,GAAG,CAAC,CAAC;IACjB,KAAAC,YAAY,GAAG,CAAC,CAAC,CAAC;IAClB,KAAAd,QAAQ,GAAG,CAAC;IAIZ,KAAAe,cAAc,GAAG,IAAI,CAAC,CAAC;IACvB,KAAAC,aAAa,GAAG,IAAI;IACpB,KAAAC,QAAQ,GAAG,KAAK;EAEqB;EAErCC,QAAQA,CAAA;IACN,IAAI,CAACC,oBAAoB,EAAE;IAC3B,IAAI,CAACC,wBAAwB,EAAE;IAC/B,IAAI,CAACC,mBAAmB,EAAE;EAC5B;EAEAC,WAAWA,CAAA;IACT,IAAI,CAACV,YAAY,CAACW,WAAW,EAAE;IAC/B,IAAI,CAACC,aAAa,EAAE;EACtB;EAEcL,oBAAoBA,CAAA;IAAA,IAAAM,KAAA;IAAA,OAAAC,iBAAA;MAChC,IAAI;QACFD,KAAI,CAACd,SAAS,GAAG,IAAI;QACrBc,KAAI,CAAClF,KAAK,GAAG,IAAI;QAEjB;QACA,MAAMkF,KAAI,CAACE,gBAAgB,EAAE;OAE9B,CAAC,OAAOpF,KAAK,EAAE;QACdqF,OAAO,CAACrF,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;QACxDkF,KAAI,CAAClF,KAAK,GAAG,kCAAkC;QAC/CkF,KAAI,CAACd,SAAS,GAAG,KAAK;;IACvB;EACH;EAEcgB,gBAAgBA,CAAA;IAAA,IAAAE,MAAA;IAAA,OAAAH,iBAAA;MAC5B;MACA,MAAM,IAAII,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,GAAG,CAAC,CAAC;MAEtDF,MAAI,CAAC1B,gBAAgB,GAAG,CACtB;QACE9B,GAAG,EAAE,OAAO;QACZF,IAAI,EAAE,qBAAqB;QAC3BG,KAAK,EAAE,aAAa;QACpBC,KAAK,EAAE,IAAI;QACX3B,aAAa,EAAE,IAAI;QACnBmB,MAAM,EAAE,CACN;UAAEC,GAAG,EAAE,mFAAmF;UAAEE,GAAG,EAAE;QAAc,CAAE,CAClH;QACDnB,MAAM,EAAE;UAAEC,OAAO,EAAE,GAAG;UAAE0B,KAAK,EAAE;QAAG,CAAE;QACpCE,SAAS,EAAE;UAAEC,KAAK,EAAE,IAAI;UAAEoD,KAAK,EAAE,GAAG;UAAEnD,SAAS,EAAE,EAAE;UAAEoD,MAAM,EAAE;QAAE,CAAE;QACjEC,QAAQ,EAAE,IAAI;QACdC,UAAU,EAAE,IAAI;QAChBC,SAAS,EAAE,IAAIC,IAAI;OACpB,EACD;QACEjE,GAAG,EAAE,OAAO;QACZF,IAAI,EAAE,iBAAiB;QACvBG,KAAK,EAAE,WAAW;QAClBC,KAAK,EAAE,IAAI;QACX3B,aAAa,EAAE,IAAI;QACnBmB,MAAM,EAAE,CACN;UAAEC,GAAG,EAAE,gFAAgF;UAAEE,GAAG,EAAE;QAAU,CAAE,CAC3G;QACDnB,MAAM,EAAE;UAAEC,OAAO,EAAE,GAAG;UAAE0B,KAAK,EAAE;QAAG,CAAE;QACpCE,SAAS,EAAE;UAAEC,KAAK,EAAE,IAAI;UAAEoD,KAAK,EAAE,GAAG;UAAEnD,SAAS,EAAE,EAAE;UAAEoD,MAAM,EAAE;QAAE,CAAE;QACjEC,QAAQ,EAAE,IAAI;QACdC,UAAU,EAAE,IAAI;QAChBC,SAAS,EAAE,IAAIC,IAAI;OACpB,EACD;QACEjE,GAAG,EAAE,OAAO;QACZF,IAAI,EAAE,kBAAkB;QACxBG,KAAK,EAAE,cAAc;QACrBC,KAAK,EAAE,IAAI;QACXR,MAAM,EAAE,CACN;UAAEC,GAAG,EAAE,gFAAgF;UAAEE,GAAG,EAAE;QAAS,CAAE,CAC1G;QACDnB,MAAM,EAAE;UAAEC,OAAO,EAAE,GAAG;UAAE0B,KAAK,EAAE;QAAE,CAAE;QACnCE,SAAS,EAAE;UAAEC,KAAK,EAAE,IAAI;UAAEoD,KAAK,EAAE,GAAG;UAAEnD,SAAS,EAAE,EAAE;UAAEoD,MAAM,EAAE;QAAE,CAAE;QACjEC,QAAQ,EAAE,IAAI;QACdC,UAAU,EAAE,IAAI;QAChBC,SAAS,EAAE,IAAIC,IAAI;OACpB,EACD;QACEjE,GAAG,EAAE,OAAO;QACZF,IAAI,EAAE,oBAAoB;QAC1BG,KAAK,EAAE,aAAa;QACpBC,KAAK,EAAE,IAAI;QACX3B,aAAa,EAAE,IAAI;QACnBmB,MAAM,EAAE,CACN;UAAEC,GAAG,EAAE,mFAAmF;UAAEE,GAAG,EAAE;QAAY,CAAE,CAChH;QACDnB,MAAM,EAAE;UAAEC,OAAO,EAAE,GAAG;UAAE0B,KAAK,EAAE;QAAG,CAAE;QACpCE,SAAS,EAAE;UAAEC,KAAK,EAAE,IAAI;UAAEoD,KAAK,EAAE,GAAG;UAAEnD,SAAS,EAAE,EAAE;UAAEoD,MAAM,EAAE;QAAE,CAAE;QACjEC,QAAQ,EAAE,IAAI;QACdC,UAAU,EAAE,IAAI;QAChBC,SAAS,EAAE,IAAIC,IAAI;OACpB,EACD;QACEjE,GAAG,EAAE,OAAO;QACZF,IAAI,EAAE,eAAe;QACrBG,KAAK,EAAE,YAAY;QACnBC,KAAK,EAAE,KAAK;QACZ3B,aAAa,EAAE,KAAK;QACpBmB,MAAM,EAAE,CACN;UAAEC,GAAG,EAAE,mFAAmF;UAAEE,GAAG,EAAE;QAAO,CAAE,CAC3G;QACDnB,MAAM,EAAE;UAAEC,OAAO,EAAE,GAAG;UAAE0B,KAAK,EAAE;QAAG,CAAE;QACpCE,SAAS,EAAE;UAAEC,KAAK,EAAE,IAAI;UAAEoD,KAAK,EAAE,GAAG;UAAEnD,SAAS,EAAE,GAAG;UAAEoD,MAAM,EAAE;QAAE,CAAE;QAClEC,QAAQ,EAAE,IAAI;QACdC,UAAU,EAAE,IAAI;QAChBC,SAAS,EAAE,IAAIC,IAAI;OACpB,CACF;MAEDT,MAAI,CAACU,0BAA0B,EAAE;MACjCV,MAAI,CAAClB,SAAS,GAAG,KAAK;IAAC;EACzB;EAEQ4B,0BAA0BA,CAAA;IAChC,IAAI,CAACC,iBAAiB,EAAE;IACxB,IAAI,CAACpD,YAAY,GAAG,CAAC;IACrB,IAAI,CAACqD,mBAAmB,EAAE;IAC1B,IAAI,CAACC,cAAc,EAAE;EACvB;EAEQtB,wBAAwBA,CAAA;IAC9B,MAAMuB,KAAK,GAAGC,MAAM,CAACC,UAAU;IAC/B,IAAIF,KAAK,IAAI,GAAG,EAAE;MAChB,IAAI,CAAC7B,YAAY,GAAG,CAAC;MACrB,IAAI,CAACD,SAAS,GAAG,GAAG;KACrB,MAAM,IAAI8B,KAAK,IAAI,GAAG,EAAE;MACvB,IAAI,CAAC7B,YAAY,GAAG,CAAC;MACrB,IAAI,CAACD,SAAS,GAAG,GAAG;KACrB,MAAM,IAAI8B,KAAK,IAAI,IAAI,EAAE;MACxB,IAAI,CAAC7B,YAAY,GAAG,CAAC;MACrB,IAAI,CAACD,SAAS,GAAG,GAAG;KACrB,MAAM;MACL,IAAI,CAACC,YAAY,GAAG,CAAC;MACrB,IAAI,CAACD,SAAS,GAAG,GAAG;;IAEtB,IAAI,CAAC2B,iBAAiB,EAAE;IACxB,IAAI,CAACC,mBAAmB,EAAE;EAC5B;EAEQpB,mBAAmBA,CAAA;IACzBuB,MAAM,CAACE,gBAAgB,CAAC,QAAQ,EAAE,MAAK;MACrC,IAAI,CAAC1B,wBAAwB,EAAE;IACjC,CAAC,CAAC;EACJ;EAEQoB,iBAAiBA,CAAA;IACvB,IAAI,CAACxC,QAAQ,GAAG+C,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC7C,gBAAgB,CAAC8C,MAAM,GAAG,IAAI,CAACnC,YAAY,CAAC;EAC/E;EAEQ2B,mBAAmBA,CAAA;IACzB,IAAI,CAACvC,WAAW,GAAG,CAAC,IAAI,CAACd,YAAY,GAAG,IAAI,CAACyB,SAAS;EACxD;EAEA;EACApB,SAASA,CAAA;IACP,IAAI,IAAI,CAACL,YAAY,GAAG,IAAI,CAACY,QAAQ,EAAE;MACrC,IAAI,CAACZ,YAAY,EAAE;MACnB,IAAI,CAACqD,mBAAmB,EAAE;;EAE9B;EAEAlD,aAAaA,CAAA;IACX,IAAI,IAAI,CAACH,YAAY,GAAG,CAAC,EAAE;MACzB,IAAI,CAACA,YAAY,EAAE;MACnB,IAAI,CAACqD,mBAAmB,EAAE;;EAE9B;EAEAtD,SAASA,CAAC+D,UAAkB;IAC1B,IAAI,CAAC9D,YAAY,GAAG2D,IAAI,CAACC,GAAG,CAAC,CAAC,EAAED,IAAI,CAACI,GAAG,CAACD,UAAU,EAAE,IAAI,CAAClD,QAAQ,CAAC,CAAC;IACpE,IAAI,CAACyC,mBAAmB,EAAE;EAC5B;EAEApC,kBAAkBA,CAAA;IAChB,OAAO+C,KAAK,CAAC,IAAI,CAACpD,QAAQ,GAAG,CAAC,CAAC,CAACqD,IAAI,CAAC,CAAC,CAAC,CAACC,GAAG,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKA,CAAC,CAAC;EAC1D;EAEA;EACQd,cAAcA,CAAA;IACpB,IAAI,CAAC,IAAI,CAAC1B,aAAa,IAAI,IAAI,CAACC,QAAQ,EAAE;IAE1C,IAAI,CAACO,aAAa,EAAE;IACpB,IAAI,CAACiC,iBAAiB,GAAGC,WAAW,CAAC,MAAK;MACxC,IAAI,CAAC,IAAI,CAACzC,QAAQ,IAAI,IAAI,CAACd,gBAAgB,CAAC8C,MAAM,GAAG,IAAI,CAACnC,YAAY,EAAE;QACtE,IAAI,CAAC6C,aAAa,EAAE;;IAExB,CAAC,EAAE,IAAI,CAAC5C,cAAc,CAAC;EACzB;EAEQ4C,aAAaA,CAAA;IACnB,IAAI,IAAI,CAACvE,YAAY,IAAI,IAAI,CAACY,QAAQ,EAAE;MACtC,IAAI,CAACZ,YAAY,GAAG,CAAC;KACtB,MAAM;MACL,IAAI,CAACA,YAAY,EAAE;;IAErB,IAAI,CAACqD,mBAAmB,EAAE;EAC5B;EAEQjB,aAAaA,CAAA;IACnB,IAAI,IAAI,CAACiC,iBAAiB,EAAE;MAC1BG,aAAa,CAAC,IAAI,CAACH,iBAAiB,CAAC;MACrC,IAAI,CAACA,iBAAiB,GAAG,IAAI;;EAEjC;EAEA9D,cAAcA,CAAA;IACZ,IAAI,CAACsB,QAAQ,GAAG,IAAI;EACtB;EAEApB,eAAeA,CAAA;IACb,IAAI,CAACoB,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACyB,cAAc,EAAE;EACvB;EAEA;EACArF,cAAcA,CAACwG,OAAgB;IAC7B,IAAI,CAACrD,MAAM,CAACsD,QAAQ,CAAC,CAAC,UAAU,EAAED,OAAO,CAACxF,GAAG,CAAC,CAAC;EACjD;EAEAX,UAAUA,CAACmG,OAAgB,EAAEE,KAAY;IACvCA,KAAK,CAACC,eAAe,EAAE;IAEvB,IAAI,IAAI,CAACvD,aAAa,CAACwD,GAAG,CAACJ,OAAO,CAACxF,GAAG,CAAC,EAAE;MACvC,IAAI,CAACoC,aAAa,CAACyD,MAAM,CAACL,OAAO,CAACxF,GAAG,CAAC;MACtCwF,OAAO,CAACjF,SAAS,CAACqD,KAAK,EAAE;KAC1B,MAAM;MACL,IAAI,CAACxB,aAAa,CAAC0D,GAAG,CAACN,OAAO,CAACxF,GAAG,CAAC;MACnCwF,OAAO,CAACjF,SAAS,CAACqD,KAAK,EAAE;;EAE7B;EAEArE,SAASA,CAACiG,OAAgB,EAAEE,KAAY;IACtCA,KAAK,CAACC,eAAe,EAAE;IACvB;IACApC,OAAO,CAACwC,GAAG,CAAC,gBAAgB,EAAEP,OAAO,CAAC1F,IAAI,CAAC;EAC7C;EAEAC,cAAcA,CAACiG,SAAiB;IAC9B,OAAO,IAAI,CAAC5D,aAAa,CAACwD,GAAG,CAACI,SAAS,CAAC;EAC1C;EAEA5H,qBAAqBA,CAACoH,OAAgB;IACpC,IAAIA,OAAO,CAACjH,aAAa,IAAIiH,OAAO,CAACjH,aAAa,GAAGiH,OAAO,CAACtF,KAAK,EAAE;MAClE,OAAOwE,IAAI,CAACuB,KAAK,CAAE,CAACT,OAAO,CAACjH,aAAa,GAAGiH,OAAO,CAACtF,KAAK,IAAIsF,OAAO,CAACjH,aAAa,GAAI,GAAG,CAAC;;IAE5F,OAAO,CAAC;EACV;EAEAD,WAAWA,CAAC4B,KAAa;IACvB,OAAO,IAAIgG,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;MACpCC,KAAK,EAAE,UAAU;MACjBC,QAAQ,EAAE,KAAK;MACfC,qBAAqB,EAAE;KACxB,CAAC,CAACC,MAAM,CAACrG,KAAK,CAAC;EAClB;EAEAI,WAAWA,CAACD,KAAa;IACvB,IAAIA,KAAK,IAAI,IAAI,EAAE;MACjB,OAAO,CAACA,KAAK,GAAG,IAAI,EAAEmG,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG;;IAExC,OAAOnG,KAAK,CAACoG,QAAQ,EAAE;EACzB;EAEA1E,gBAAgBA,CAAClB,KAAa,EAAE2E,OAAgB;IAC9C,OAAOA,OAAO,CAACxF,GAAG;EACpB;EAEA0G,QAAQA,CAAChB,KAAY;IACnBA,KAAK,CAACiB,cAAc,EAAE;IACtB,IAAI,CAACxE,MAAM,CAACsD,QAAQ,CAAC,CAAC,oBAAoB,CAAC,CAAC;EAC9C;EAEA1H,OAAOA,CAAA;IACL,IAAI,CAAC+E,oBAAoB,EAAE;EAC7B;;;uBAtSWb,+BAA+B,EAAA9E,EAAA,CAAAyJ,iBAAA,CAAAC,EAAA,CAAAC,MAAA;IAAA;EAAA;;;YAA/B7E,+BAA+B;MAAA8E,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAA9J,EAAA,CAAA+J,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,yCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCxCxCrK,EAHJ,CAAAC,cAAA,aAAiC,aAED,SACxB;UACFD,EAAA,CAAAE,SAAA,kBAA+C;UAC/CF,EAAA,CAAAG,MAAA,0BACF;UAAAH,EAAA,CAAAI,YAAA,EAAK;UACLJ,EAAA,CAAAC,cAAA,WAAuD;UAA3BD,EAAA,CAAAK,UAAA,mBAAAkK,4DAAAtI,MAAA;YAAA,OAASqI,GAAA,CAAAf,QAAA,CAAAtH,MAAA,CAAgB;UAAA,EAAC;UAACjC,EAAA,CAAAG,MAAA,cAAO;UAChEH,EADgE,CAAAI,YAAA,EAAI,EAC9D;UAGNJ,EAAA,CAAAC,cAAA,aAA+B;UAoI7BD,EAlIA,CAAA8B,UAAA,IAAA0I,8CAAA,iBAAiD,IAAAC,8CAAA,iBAMQ,KAAAC,+CAAA,kBAO0C,KAAAC,+CAAA,iBAqHR;UAK/F3K,EADE,CAAAI,YAAA,EAAM,EACF;;;UAvIIJ,EAAA,CAAAa,SAAA,GAAe;UAAfb,EAAA,CAAAyB,UAAA,SAAA6I,GAAA,CAAAnF,SAAA,CAAe;UAMfnF,EAAA,CAAAa,SAAA,EAAyB;UAAzBb,EAAA,CAAAyB,UAAA,SAAA6I,GAAA,CAAAvJ,KAAA,KAAAuJ,GAAA,CAAAnF,SAAA,CAAyB;UAOzBnF,EAAA,CAAAa,SAAA,EAAyD;UAAzDb,EAAA,CAAAyB,UAAA,UAAA6I,GAAA,CAAAnF,SAAA,KAAAmF,GAAA,CAAAvJ,KAAA,IAAAuJ,GAAA,CAAA3F,gBAAA,CAAA8C,MAAA,KAAyD;UAqHzDzH,EAAA,CAAAa,SAAA,EAA2D;UAA3Db,EAAA,CAAAyB,UAAA,UAAA6I,GAAA,CAAAnF,SAAA,KAAAmF,GAAA,CAAAvJ,KAAA,IAAAuJ,GAAA,CAAA3F,gBAAA,CAAA8C,MAAA,OAA2D;;;qBD3GjE7H,YAAY,EAAAgL,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EACZjL,YAAY,EACZC,WAAW,EAAAiL,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,UAAA;MAAAC,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}