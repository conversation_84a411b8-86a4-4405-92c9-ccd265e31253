import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, BehaviorSubject } from 'rxjs';
import { environment } from '../../../environments/environment';
import { Product } from '../models/product.model';

export interface TrendingResponse {
  success: boolean;
  products: Product[];
  pagination: {
    currentPage: number;
    totalPages: number;
    totalItems: number;
    hasNextPage: boolean;
    hasPrevPage: boolean;
  };
}

export interface FeaturedBrand {
  brand: string;
  productCount: number;
  avgRating: number;
  totalViews: number;
  topProducts: Product[];
}

export interface FeaturedBrandsResponse {
  success: boolean;
  brands: FeaturedBrand[];
}

export interface Influencer {
  _id: string;
  username: string;
  fullName: string;
  avatar: string;
  bio: string;
  socialStats: {
    followersCount: number;
    postsCount: number;
    followingCount: number;
  };
  isInfluencer: boolean;
}

export interface InfluencersResponse {
  success: boolean;
  influencers: Influencer[];
  pagination: {
    currentPage: number;
    totalPages: number;
    totalItems: number;
    hasNextPage: boolean;
    hasPrevPage: boolean;
  };
}

@Injectable({
  providedIn: 'root'
})
export class TrendingService {
  private readonly API_URL = 'http://localhost:3001/api'; // Updated to correct port

  // BehaviorSubjects for caching
  private trendingProductsSubject = new BehaviorSubject<Product[]>([]);
  private suggestedProductsSubject = new BehaviorSubject<Product[]>([]);
  private newArrivalsSubject = new BehaviorSubject<Product[]>([]);
  private featuredBrandsSubject = new BehaviorSubject<FeaturedBrand[]>([]);
  private influencersSubject = new BehaviorSubject<Influencer[]>([]);

  // Public observables
  public trendingProducts$ = this.trendingProductsSubject.asObservable();
  public suggestedProducts$ = this.suggestedProductsSubject.asObservable();
  public newArrivals$ = this.newArrivalsSubject.asObservable();
  public featuredBrands$ = this.featuredBrandsSubject.asObservable();
  public influencers$ = this.influencersSubject.asObservable();

  constructor(private http: HttpClient) {}

  // Get trending products
  getTrendingProducts(page: number = 1, limit: number = 12): Observable<TrendingResponse> {
    return this.http.get<TrendingResponse>(`${this.API_URL}/v1/products/trending`, {
      params: { page: page.toString(), limit: limit.toString() }
    });
  }

  // Get suggested products
  getSuggestedProducts(page: number = 1, limit: number = 12): Observable<TrendingResponse> {
    return this.http.get<TrendingResponse>(`${this.API_URL}/v1/products/suggested`, {
      params: { page: page.toString(), limit: limit.toString() }
    });
  }

  // Get new arrivals
  getNewArrivals(page: number = 1, limit: number = 12): Observable<TrendingResponse> {
    return this.http.get<TrendingResponse>(`${this.API_URL}/v1/products/new-arrivals`, {
      params: { page: page.toString(), limit: limit.toString() }
    });
  }

  // Get featured brands
  getFeaturedBrands(): Observable<FeaturedBrandsResponse> {
    return this.http.get<FeaturedBrandsResponse>(`${this.API_URL}/v1/products/featured-brands`);
  }

  // Get influencers
  getInfluencers(page: number = 1, limit: number = 10): Observable<InfluencersResponse> {
    return this.http.get<InfluencersResponse>(`${this.API_URL}/v1/users/influencers`, {
      params: { page: page.toString(), limit: limit.toString() }
    });
  }

  // Load and cache trending products
  async loadTrendingProducts(page: number = 1, limit: number = 12): Promise<void> {
    try {
      const response = await this.getTrendingProducts(page, limit).toPromise();
      if (response?.success && response?.products) {
        this.trendingProductsSubject.next(response.products);
      }
    } catch (error) {
      console.error('Error loading trending products:', error);
      // Provide mock data as fallback
      const mockProducts: Product[] = [
        {
          _id: 'tp1',
          name: 'Trending Sneakers',
          description: 'Most popular sneakers this season',
          price: 5999,
          originalPrice: 7999,
          discount: 25,
          category: 'men',
          subcategory: 'shoes',
          brand: 'Nike',
          images: [{
            url: 'https://images.unsplash.com/photo-1549298916-b41d501d3772?w=400&h=400&fit=crop',
            alt: 'Trending Sneakers',
            isPrimary: true
          }],
          sizes: [
            { size: '7', stock: 10 },
            { size: '8', stock: 15 },
            { size: '9', stock: 20 },
            { size: '10', stock: 12 },
            { size: '11', stock: 8 }
          ],
          colors: [
            { name: 'White', code: '#FFFFFF' },
            { name: 'Black', code: '#000000' },
            { name: 'Red', code: '#FF0000' }
          ],
          vendor: {
            _id: 'vendor1',
            username: 'nike_official',
            fullName: 'Nike Official Store',
            avatar: 'https://images.unsplash.com/photo-1542291026-7eec264c27ff?w=100&h=100&fit=crop'
          },
          tags: ['trending', 'sneakers', 'sports'],
          features: ['Comfortable', 'Durable', 'Stylish'],
          isActive: true,
          isFeatured: true,
          rating: {
            average: 4.7,
            count: 256
          },
          reviews: [],
          seo: {
            slug: 'trending-sneakers',
            metaTitle: 'Trending Sneakers - Nike',
            metaDescription: 'Most popular sneakers this season'
          },
          analytics: {
            views: 1250,
            likes: 89,
            shares: 23,
            purchases: 156
          },
          createdAt: new Date(),
          updatedAt: new Date()
        },
        {
          _id: 'tp2',
          name: 'Stylish Handbag',
          description: 'Elegant handbag for every occasion',
          price: 4999,
          originalPrice: 6999,
          discount: 29,
          category: 'women',
          subcategory: 'bags',
          brand: 'Zara',
          images: [{
            url: 'https://images.unsplash.com/photo-1553062407-98eeb64c6a62?w=400&h=400&fit=crop',
            alt: 'Stylish Handbag',
            isPrimary: true
          }],
          sizes: [
            { size: 'One Size', stock: 25 }
          ],
          colors: [
            { name: 'Brown', code: '#8B4513' },
            { name: 'Black', code: '#000000' },
            { name: 'Tan', code: '#D2B48C' }
          ],
          vendor: {
            _id: 'vendor2',
            username: 'zara_official',
            fullName: 'Zara Official Store',
            avatar: 'https://images.unsplash.com/photo-1441986300917-64674bd600d8?w=100&h=100&fit=crop'
          },
          tags: ['handbag', 'fashion', 'women'],
          features: ['Spacious', 'Elegant', 'Versatile'],
          isActive: true,
          isFeatured: true,
          rating: {
            average: 4.4,
            count: 189
          },
          reviews: [],
          seo: {
            slug: 'stylish-handbag',
            metaTitle: 'Stylish Handbag - Zara',
            metaDescription: 'Elegant handbag for every occasion'
          },
          analytics: {
            views: 890,
            likes: 67,
            shares: 15,
            purchases: 98
          },
          createdAt: new Date(),
          updatedAt: new Date()
        }
      ];
      this.trendingProductsSubject.next(mockProducts);
    }
  }

  // Load and cache suggested products
  async loadSuggestedProducts(page: number = 1, limit: number = 12): Promise<void> {
    try {
      const response = await this.getSuggestedProducts(page, limit).toPromise();
      if (response?.success && response?.products) {
        this.suggestedProductsSubject.next(response.products);
      }
    } catch (error) {
      console.error('Error loading suggested products:', error);
    }
  }

  // Load and cache new arrivals
  async loadNewArrivals(page: number = 1, limit: number = 12): Promise<void> {
    try {
      const response = await this.getNewArrivals(page, limit).toPromise();
      if (response?.success && response?.products) {
        this.newArrivalsSubject.next(response.products);
      }
    } catch (error) {
      console.error('Error loading new arrivals:', error);
      // Provide mock data as fallback
      const mockProducts: Product[] = [
        {
          _id: 'na1',
          name: 'Summer Floral Dress',
          description: 'Beautiful summer floral dress perfect for any occasion',
          price: 2999,
          originalPrice: 3999,
          discount: 25,
          category: 'women',
          subcategory: 'dresses',
          brand: 'Zara',
          images: [{
            url: 'https://images.unsplash.com/photo-1515372039744-b8f02a3ae446?w=400&h=400&fit=crop',
            alt: 'Summer Floral Dress',
            isPrimary: true
          }],
          sizes: [
            { size: 'S', stock: 15 },
            { size: 'M', stock: 20 },
            { size: 'L', stock: 18 },
            { size: 'XL', stock: 12 }
          ],
          colors: [
            { name: 'Red', code: '#FF0000' },
            { name: 'Blue', code: '#0000FF' },
            { name: 'Green', code: '#008000' }
          ],
          vendor: {
            _id: 'vendor2',
            username: 'zara_official',
            fullName: 'Zara Official Store',
            avatar: 'https://images.unsplash.com/photo-1441986300917-64674bd600d8?w=100&h=100&fit=crop'
          },
          tags: ['new', 'dress', 'summer', 'floral'],
          features: ['Lightweight', 'Breathable', 'Elegant'],
          isActive: true,
          isFeatured: false,
          rating: {
            average: 4.5,
            count: 128
          },
          reviews: [],
          seo: {
            slug: 'summer-floral-dress',
            metaTitle: 'Summer Floral Dress - Zara',
            metaDescription: 'Beautiful summer floral dress perfect for any occasion'
          },
          analytics: {
            views: 756,
            likes: 45,
            shares: 12,
            purchases: 67
          },
          createdAt: new Date(),
          updatedAt: new Date()
        },
        {
          _id: 'na2',
          name: 'Casual Denim Jacket',
          description: 'Classic denim jacket for casual wear',
          price: 3499,
          originalPrice: 4499,
          discount: 22,
          category: 'women',
          subcategory: 'jackets',
          brand: 'H&M',
          images: [{
            url: 'https://images.unsplash.com/photo-1551698618-1dfe5d97d256?w=400&h=400&fit=crop',
            alt: 'Casual Denim Jacket',
            isPrimary: true
          }],
          sizes: [
            { size: 'S', stock: 10 },
            { size: 'M', stock: 15 },
            { size: 'L', stock: 12 },
            { size: 'XL', stock: 8 }
          ],
          colors: [
            { name: 'Blue', code: '#0000FF' },
            { name: 'Black', code: '#000000' }
          ],
          vendor: {
            _id: 'vendor3',
            username: 'hm_official',
            fullName: 'H&M Official Store',
            avatar: 'https://images.unsplash.com/photo-1445205170230-053b83016050?w=100&h=100&fit=crop'
          },
          tags: ['new', 'jacket', 'denim', 'casual'],
          features: ['Durable', 'Comfortable', 'Versatile'],
          isActive: true,
          isFeatured: false,
          rating: {
            average: 4.3,
            count: 89
          },
          reviews: [],
          seo: {
            slug: 'casual-denim-jacket',
            metaTitle: 'Casual Denim Jacket - H&M',
            metaDescription: 'Classic denim jacket for casual wear'
          },
          analytics: {
            views: 543,
            likes: 32,
            shares: 8,
            purchases: 45
          },
          createdAt: new Date(),
          updatedAt: new Date()
        }
      ];
      this.newArrivalsSubject.next(mockProducts);
    }
  }

  // Load and cache featured brands
  async loadFeaturedBrands(): Promise<void> {
    try {
      const response = await this.getFeaturedBrands().toPromise();
      if (response?.success && response?.brands) {
        this.featuredBrandsSubject.next(response.brands);
      }
    } catch (error) {
      console.error('Error loading featured brands:', error);
      // Provide mock data as fallback
      const mockBrands: FeaturedBrand[] = [
        {
          brand: 'Zara',
          productCount: 1250,
          avgRating: 4.3,
          totalViews: 125000,
          topProducts: [
            {
              _id: 'zara1',
              name: 'Zara Elegant Dress',
              description: 'Beautiful elegant dress for special occasions',
              price: 3999,
              originalPrice: 4999,
              discount: 20,
              category: 'women',
              subcategory: 'dresses',
              brand: 'Zara',
              images: [{
                url: 'https://images.unsplash.com/photo-1515372039744-b8f02a3ae446?w=400&h=400&fit=crop',
                alt: 'Zara Elegant Dress',
                isPrimary: true
              }],
              sizes: [
                { size: 'S', stock: 10 },
                { size: 'M', stock: 15 },
                { size: 'L', stock: 8 }
              ],
              colors: [
                { name: 'Black', code: '#000000' },
                { name: 'Navy', code: '#000080' }
              ],
              vendor: {
                _id: 'zara_vendor',
                username: 'zara_official',
                fullName: 'Zara Official Store',
                avatar: 'https://images.unsplash.com/photo-1441986300917-64674bd600d8?w=100&h=100&fit=crop'
              },
              tags: ['elegant', 'dress', 'formal'],
              features: ['Premium fabric', 'Elegant design', 'Comfortable fit'],
              isActive: true,
              isFeatured: true,
              rating: { average: 4.3, count: 89 },
              reviews: [],
              seo: {
                slug: 'zara-elegant-dress',
                metaTitle: 'Zara Elegant Dress',
                metaDescription: 'Beautiful elegant dress for special occasions'
              },
              analytics: { views: 890, likes: 67, shares: 12, purchases: 45 },
              createdAt: new Date(),
              updatedAt: new Date()
            }
          ]
        },
        {
          brand: 'H&M',
          productCount: 980,
          avgRating: 4.1,
          totalViews: 98000,
          topProducts: [
            {
              _id: 'hm1',
              name: 'H&M Casual T-Shirt',
              description: 'Comfortable casual t-shirt for everyday wear',
              price: 1299,
              originalPrice: 1599,
              discount: 19,
              category: 'men',
              subcategory: 'tops',
              brand: 'H&M',
              images: [{
                url: 'https://images.unsplash.com/photo-1521572163474-6864f9cf17ab?w=400&h=400&fit=crop',
                alt: 'H&M Casual T-Shirt',
                isPrimary: true
              }],
              sizes: [
                { size: 'S', stock: 20 },
                { size: 'M', stock: 25 },
                { size: 'L', stock: 18 },
                { size: 'XL', stock: 12 }
              ],
              colors: [
                { name: 'White', code: '#FFFFFF' },
                { name: 'Gray', code: '#808080' },
                { name: 'Blue', code: '#0000FF' }
              ],
              vendor: {
                _id: 'hm_vendor',
                username: 'hm_official',
                fullName: 'H&M Official Store',
                avatar: 'https://images.unsplash.com/photo-1441986300917-64674bd600d8?w=100&h=100&fit=crop'
              },
              tags: ['casual', 't-shirt', 'comfortable'],
              features: ['Soft cotton', 'Casual fit', 'Versatile'],
              isActive: true,
              isFeatured: true,
              rating: { average: 4.1, count: 156 },
              reviews: [],
              seo: {
                slug: 'hm-casual-tshirt',
                metaTitle: 'H&M Casual T-Shirt',
                metaDescription: 'Comfortable casual t-shirt for everyday wear'
              },
              analytics: { views: 567, likes: 43, shares: 8, purchases: 78 },
              createdAt: new Date(),
              updatedAt: new Date()
            }
          ]
        },
        {
          brand: 'Nike',
          productCount: 750,
          avgRating: 4.6,
          totalViews: 156000,
          topProducts: [
            {
              _id: 'nike1',
              name: 'Nike Air Max',
              description: 'Premium athletic shoes with air cushioning',
              price: 8999,
              originalPrice: 10999,
              discount: 18,
              category: 'men',
              subcategory: 'shoes',
              brand: 'Nike',
              images: [{
                url: 'https://images.unsplash.com/photo-1542291026-7eec264c27ff?w=400&h=400&fit=crop',
                alt: 'Nike Air Max',
                isPrimary: true
              }],
              sizes: [
                { size: '7', stock: 8 },
                { size: '8', stock: 12 },
                { size: '9', stock: 15 },
                { size: '10', stock: 10 },
                { size: '11', stock: 6 }
              ],
              colors: [
                { name: 'White', code: '#FFFFFF' },
                { name: 'Black', code: '#000000' },
                { name: 'Red', code: '#FF0000' }
              ],
              vendor: {
                _id: 'nike_vendor',
                username: 'nike_official',
                fullName: 'Nike Official Store',
                avatar: 'https://images.unsplash.com/photo-1542291026-7eec264c27ff?w=100&h=100&fit=crop'
              },
              tags: ['athletic', 'shoes', 'air-max'],
              features: ['Air cushioning', 'Durable', 'Comfortable'],
              isActive: true,
              isFeatured: true,
              rating: { average: 4.6, count: 234 },
              reviews: [],
              seo: {
                slug: 'nike-air-max',
                metaTitle: 'Nike Air Max',
                metaDescription: 'Premium athletic shoes with air cushioning'
              },
              analytics: { views: 1234, likes: 98, shares: 25, purchases: 123 },
              createdAt: new Date(),
              updatedAt: new Date()
            }
          ]
        },
        {
          brand: 'Adidas',
          productCount: 680,
          avgRating: 4.4,
          totalViews: 134000,
          topProducts: [
            {
              _id: 'adidas1',
              name: 'Adidas Ultraboost',
              description: 'High-performance running shoes with boost technology',
              price: 9999,
              originalPrice: 12999,
              discount: 23,
              category: 'men',
              subcategory: 'shoes',
              brand: 'Adidas',
              images: [{
                url: 'https://images.unsplash.com/photo-1551107696-a4b0c5a0d9a2?w=400&h=400&fit=crop',
                alt: 'Adidas Ultraboost',
                isPrimary: true
              }],
              sizes: [
                { size: '7', stock: 5 },
                { size: '8', stock: 10 },
                { size: '9', stock: 12 },
                { size: '10', stock: 8 },
                { size: '11', stock: 4 }
              ],
              colors: [
                { name: 'Black', code: '#000000' },
                { name: 'White', code: '#FFFFFF' },
                { name: 'Blue', code: '#0000FF' }
              ],
              vendor: {
                _id: 'adidas_vendor',
                username: 'adidas_official',
                fullName: 'Adidas Official Store',
                avatar: 'https://images.unsplash.com/photo-1551107696-a4b0c5a0d9a2?w=100&h=100&fit=crop'
              },
              tags: ['running', 'shoes', 'boost'],
              features: ['Boost technology', 'Lightweight', 'Responsive'],
              isActive: true,
              isFeatured: true,
              rating: { average: 4.4, count: 187 },
              reviews: [],
              seo: {
                slug: 'adidas-ultraboost',
                metaTitle: 'Adidas Ultraboost',
                metaDescription: 'High-performance running shoes with boost technology'
              },
              analytics: { views: 987, likes: 76, shares: 19, purchases: 89 },
              createdAt: new Date(),
              updatedAt: new Date()
            }
          ]
        }
      ];
      this.featuredBrandsSubject.next(mockBrands);
    }
  }

  // Load and cache influencers
  async loadInfluencers(page: number = 1, limit: number = 10): Promise<void> {
    try {
      const response = await this.getInfluencers(page, limit).toPromise();
      if (response?.success && response?.influencers) {
        this.influencersSubject.next(response.influencers);
      }
    } catch (error) {
      console.error('Error loading influencers:', error);
      // Provide mock data as fallback
      const mockInfluencers: Influencer[] = [
        {
          _id: 'inf1',
          username: 'fashionista_maya',
          fullName: 'Maya Chen',
          avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b47c?w=150&h=150&fit=crop&crop=face',
          bio: 'Fashion enthusiast & style blogger',
          socialStats: {
            followersCount: 125000,
            followingCount: 890,
            postsCount: 456
          },
          isInfluencer: true
        },
        {
          _id: 'inf2',
          username: 'style_guru_alex',
          fullName: 'Alex Rodriguez',
          avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face',
          bio: 'Street style & urban fashion',
          socialStats: {
            followersCount: 89000,
            followingCount: 567,
            postsCount: 234
          },
          isInfluencer: true
        }
      ];
      this.influencersSubject.next(mockInfluencers);
    }
  }

  // Clear all cached data
  clearCache(): void {
    this.trendingProductsSubject.next([]);
    this.suggestedProductsSubject.next([]);
    this.newArrivalsSubject.next([]);
    this.featuredBrandsSubject.next([]);
    this.influencersSubject.next([]);
  }

  // Get current cached data
  getCurrentTrendingProducts(): Product[] {
    return this.trendingProductsSubject.value;
  }

  getCurrentSuggestedProducts(): Product[] {
    return this.suggestedProductsSubject.value;
  }

  getCurrentNewArrivals(): Product[] {
    return this.newArrivalsSubject.value;
  }

  getCurrentFeaturedBrands(): FeaturedBrand[] {
    return this.featuredBrandsSubject.value;
  }

  getCurrentInfluencers(): Influencer[] {
    return this.influencersSubject.value;
  }
}
