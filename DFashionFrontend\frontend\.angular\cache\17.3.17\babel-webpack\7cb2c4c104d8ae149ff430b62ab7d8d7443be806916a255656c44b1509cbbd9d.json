{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { IonicModule } from '@ionic/angular';\nimport { ViewAddStoriesComponent } from '../../components/view-add-stories/view-add-stories.component';\nimport { FeedComponent } from '../../components/feed/feed.component';\nimport { SidebarComponent } from '../../components/sidebar/sidebar.component';\nimport { TrendingNowComponent } from '../../components/trending-now/trending-now.component';\nimport { TrendingProductsComponent } from '../../components/trending-products/trending-products.component';\nimport { FeaturedBrandsComponent } from '../../components/featured-brands/featured-brands.component';\nimport { NewArrivalsComponent } from '../../components/new-arrivals/new-arrivals.component';\nimport { SuggestedForYouComponent } from '../../components/suggested-for-you/suggested-for-you.component';\nimport { TopFashionInfluencersComponent } from '../../components/top-fashion-influencers/top-fashion-influencers.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common\";\nimport * as i2 from \"@ionic/angular\";\nfunction HomeComponent_div_1_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 20);\n    i0.ɵɵtext(1, \"3\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction HomeComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 10)(1, \"div\", 11)(2, \"h1\", 12);\n    i0.ɵɵtext(3, \"DFashion\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(4, \"ion-icon\", 13);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 14);\n    i0.ɵɵelement(6, \"ion-icon\", 15);\n    i0.ɵɵelementStart(7, \"div\", 16);\n    i0.ɵɵlistener(\"click\", function HomeComponent_div_1_Template_div_click_7_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.toggleTabMenu());\n    });\n    i0.ɵɵelement(8, \"ion-icon\", 17);\n    i0.ɵɵtemplate(9, HomeComponent_div_1_div_9_Template, 2, 0, \"div\", 18);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"div\", 16);\n    i0.ɵɵlistener(\"click\", function HomeComponent_div_1_Template_div_click_10_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.toggleSidebar());\n    });\n    i0.ɵɵelement(11, \"ion-icon\", 19);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(9);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.hasNotifications);\n  }\n}\nfunction HomeComponent_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 21)(1, \"div\", 22);\n    i0.ɵɵelement(2, \"app-view-add-stories\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 23);\n    i0.ɵɵelement(4, \"app-trending-now\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 24)(6, \"div\", 25);\n    i0.ɵɵelement(7, \"app-feed\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"div\", 26);\n    i0.ɵɵelement(9, \"app-sidebar\", 27);\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction HomeComponent_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 28)(1, \"div\", 29)(2, \"div\", 30);\n    i0.ɵɵelement(3, \"app-view-add-stories\");\n    i0.ɵɵelementStart(4, \"div\", 31);\n    i0.ɵɵelement(5, \"app-trending-now\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(6, \"app-feed\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(7, \"app-sidebar\", 27);\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction HomeComponent_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 32);\n    i0.ɵɵlistener(\"click\", function HomeComponent_div_4_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.closeTabMenu());\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵclassProp(\"active\", ctx_r1.isTabMenuOpen);\n  }\n}\nfunction HomeComponent_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 33);\n    i0.ɵɵlistener(\"click\", function HomeComponent_div_5_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.closeSidebar());\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵclassProp(\"active\", ctx_r1.isSidebarOpen);\n  }\n}\nfunction HomeComponent_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 34)(1, \"div\", 35)(2, \"h3\");\n    i0.ɵɵtext(3, \"Discover\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"ion-icon\", 36);\n    i0.ɵɵlistener(\"click\", function HomeComponent_div_6_Template_ion_icon_click_4_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.closeTabMenu());\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"div\", 37)(6, \"div\", 38);\n    i0.ɵɵlistener(\"click\", function HomeComponent_div_6_Template_div_click_6_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.openSidebarTab(\"trending\"));\n    });\n    i0.ɵɵelementStart(7, \"div\", 39);\n    i0.ɵɵelement(8, \"ion-icon\", 40);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"span\", 41);\n    i0.ɵɵtext(10, \"Trending\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"div\", 42);\n    i0.ɵɵtext(12, \"Hot products right now\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(13, \"div\", 38);\n    i0.ɵɵlistener(\"click\", function HomeComponent_div_6_Template_div_click_13_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.openSidebarTab(\"brands\"));\n    });\n    i0.ɵɵelementStart(14, \"div\", 43);\n    i0.ɵɵelement(15, \"ion-icon\", 44);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"span\", 41);\n    i0.ɵɵtext(17, \"Brands\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"div\", 42);\n    i0.ɵɵtext(19, \"Top fashion brands\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(20, \"div\", 38);\n    i0.ɵɵlistener(\"click\", function HomeComponent_div_6_Template_div_click_20_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.openSidebarTab(\"arrivals\"));\n    });\n    i0.ɵɵelementStart(21, \"div\", 45);\n    i0.ɵɵelement(22, \"ion-icon\", 46);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"span\", 41);\n    i0.ɵɵtext(24, \"New\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(25, \"div\", 42);\n    i0.ɵɵtext(26, \"Latest arrivals\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(27, \"div\", 38);\n    i0.ɵɵlistener(\"click\", function HomeComponent_div_6_Template_div_click_27_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.openSidebarTab(\"suggested\"));\n    });\n    i0.ɵɵelementStart(28, \"div\", 47);\n    i0.ɵɵelement(29, \"ion-icon\", 48);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(30, \"span\", 41);\n    i0.ɵɵtext(31, \"For You\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(32, \"div\", 42);\n    i0.ɵɵtext(33, \"Personalized picks\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(34, \"div\", 38);\n    i0.ɵɵlistener(\"click\", function HomeComponent_div_6_Template_div_click_34_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.openSidebarTab(\"influencers\"));\n    });\n    i0.ɵɵelementStart(35, \"div\", 49);\n    i0.ɵɵelement(36, \"ion-icon\", 50);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(37, \"span\", 41);\n    i0.ɵɵtext(38, \"Influencers\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(39, \"div\", 42);\n    i0.ɵɵtext(40, \"Top fashion creators\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(41, \"div\", 38);\n    i0.ɵɵlistener(\"click\", function HomeComponent_div_6_Template_div_click_41_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.openSidebarTab(\"categories\"));\n    });\n    i0.ɵɵelementStart(42, \"div\", 51);\n    i0.ɵɵelement(43, \"ion-icon\", 52);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(44, \"span\", 41);\n    i0.ɵɵtext(45, \"Categories\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(46, \"div\", 42);\n    i0.ɵɵtext(47, \"Browse by category\");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵclassProp(\"active\", ctx_r1.isTabMenuOpen);\n  }\n}\nfunction HomeComponent_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 53)(1, \"div\", 54)(2, \"div\", 55)(3, \"div\", 56);\n    i0.ɵɵelement(4, \"img\", 57);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 58)(6, \"h3\");\n    i0.ɵɵtext(7, \"Your Profile\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"p\");\n    i0.ɵɵtext(9, \"@username\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(10, \"ion-icon\", 36);\n    i0.ɵɵlistener(\"click\", function HomeComponent_div_7_Template_ion_icon_click_10_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.closeSidebar());\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"div\", 59);\n    i0.ɵɵelement(12, \"app-sidebar\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵclassProp(\"active\", ctx_r1.isSidebarOpen);\n  }\n}\nfunction HomeComponent_div_8_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 26);\n    i0.ɵɵelement(1, \"app-trending-products\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction HomeComponent_div_8_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 26);\n    i0.ɵɵelement(1, \"app-featured-brands\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction HomeComponent_div_8_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 26);\n    i0.ɵɵelement(1, \"app-new-arrivals\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction HomeComponent_div_8_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 26);\n    i0.ɵɵelement(1, \"app-suggested-for-you\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction HomeComponent_div_8_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 26);\n    i0.ɵɵelement(1, \"app-top-fashion-influencers\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction HomeComponent_div_8_div_11_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 66)(1, \"div\", 67);\n    i0.ɵɵelement(2, \"ion-icon\", 68);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const category_r8 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"name\", category_r8.icon);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(category_r8.name);\n  }\n}\nfunction HomeComponent_div_8_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 26)(1, \"div\", 64);\n    i0.ɵɵtemplate(2, HomeComponent_div_8_div_11_div_2_Template, 5, 2, \"div\", 65);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.categories);\n  }\n}\nfunction HomeComponent_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 60)(1, \"div\", 61)(2, \"h3\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"ion-icon\", 36);\n    i0.ɵɵlistener(\"click\", function HomeComponent_div_8_Template_ion_icon_click_4_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.closeSidebarContent());\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"div\", 62);\n    i0.ɵɵtemplate(6, HomeComponent_div_8_div_6_Template, 2, 0, \"div\", 63)(7, HomeComponent_div_8_div_7_Template, 2, 0, \"div\", 63)(8, HomeComponent_div_8_div_8_Template, 2, 0, \"div\", 63)(9, HomeComponent_div_8_div_9_Template, 2, 0, \"div\", 63)(10, HomeComponent_div_8_div_10_Template, 2, 0, \"div\", 63)(11, HomeComponent_div_8_div_11_Template, 3, 1, \"div\", 63);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵclassProp(\"active\", ctx_r1.isSidebarContentOpen);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r1.currentSidebarTitle);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.currentSidebarTab === \"trending\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.currentSidebarTab === \"brands\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.currentSidebarTab === \"arrivals\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.currentSidebarTab === \"suggested\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.currentSidebarTab === \"influencers\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.currentSidebarTab === \"categories\");\n  }\n}\nfunction HomeComponent_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 69)(1, \"div\", 70);\n    i0.ɵɵelement(2, \"ion-icon\", 71);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 72);\n    i0.ɵɵelement(4, \"ion-icon\", 73);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 72);\n    i0.ɵɵelement(6, \"ion-icon\", 74);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\", 72);\n    i0.ɵɵelement(8, \"ion-icon\", 75);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"div\", 72)(10, \"div\", 76);\n    i0.ɵɵelement(11, \"img\", 57);\n    i0.ɵɵelementEnd()()();\n  }\n}\nexport class HomeComponent {\n  constructor() {\n    this.isMobile = false;\n    this.isSidebarOpen = false;\n    this.isTabMenuOpen = false;\n    this.isSidebarContentOpen = false;\n    this.currentSidebarTab = '';\n    this.currentSidebarTitle = '';\n    this.hasNotifications = true; // Example notification state\n    this.window = window; // For template access\n    // TikTok-style interaction states\n    this.isLiked = false;\n    // Instagram Stories Data - Enhanced for responsive design and mobile app\n    this.instagramStories = [{\n      id: 1,\n      username: 'zara',\n      avatar: '/assets/images/default-avatar.svg',\n      hasStory: true,\n      viewed: false,\n      touching: false\n    }, {\n      id: 2,\n      username: 'nike',\n      avatar: '/assets/images/default-avatar.svg',\n      hasStory: true,\n      viewed: false,\n      touching: false\n    }, {\n      id: 3,\n      username: 'adidas',\n      avatar: '/assets/images/default-avatar.svg',\n      hasStory: true,\n      viewed: true,\n      touching: false\n    }, {\n      id: 4,\n      username: 'h&m',\n      avatar: '/assets/images/default-avatar.svg',\n      hasStory: true,\n      viewed: false,\n      touching: false\n    }, {\n      id: 5,\n      username: 'uniqlo',\n      avatar: '/assets/images/default-avatar.svg',\n      hasStory: true,\n      viewed: false,\n      touching: false\n    }, {\n      id: 6,\n      username: 'gucci',\n      avatar: '/assets/images/default-avatar.svg',\n      hasStory: true,\n      viewed: true,\n      touching: false\n    }, {\n      id: 7,\n      username: 'prada',\n      avatar: '/assets/images/default-avatar.svg',\n      hasStory: true,\n      viewed: false,\n      touching: false\n    }, {\n      id: 8,\n      username: 'versace',\n      avatar: '/assets/images/default-avatar.svg',\n      hasStory: true,\n      viewed: false,\n      touching: false\n    }];\n    // Categories Data\n    this.categories = [{\n      name: 'Women',\n      icon: 'woman'\n    }, {\n      name: 'Men',\n      icon: 'man'\n    }, {\n      name: 'Kids',\n      icon: 'happy'\n    }, {\n      name: 'Shoes',\n      icon: 'footsteps'\n    }, {\n      name: 'Bags',\n      icon: 'bag'\n    }, {\n      name: 'Accessories',\n      icon: 'watch'\n    }, {\n      name: 'Beauty',\n      icon: 'flower'\n    }, {\n      name: 'Sports',\n      icon: 'fitness'\n    }];\n    this.preventScroll = e => {\n      if (this.isSidebarOpen || this.isTabMenuOpen || this.isSidebarContentOpen) {\n        e.preventDefault();\n      }\n    };\n  }\n  ngOnInit() {\n    this.checkScreenSize();\n    console.log('Home component initialized:', {\n      isMobile: this.isMobile,\n      instagramStories: this.instagramStories.length\n    });\n    // Prevent body scroll when sidebar is open\n    document.addEventListener('touchmove', this.preventScroll, {\n      passive: false\n    });\n  }\n  ngOnDestroy() {\n    document.removeEventListener('touchmove', this.preventScroll);\n  }\n  onResize(event) {\n    this.checkScreenSize();\n    if (!this.isMobile && this.isSidebarOpen) {\n      this.closeSidebar();\n    }\n  }\n  checkScreenSize() {\n    // More comprehensive mobile detection\n    const width = window.innerWidth;\n    const userAgent = navigator.userAgent;\n    const isMobileUserAgent = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(userAgent);\n    // Consider it mobile if width <= 768px OR if it's a mobile user agent\n    this.isMobile = width <= 768 || isMobileUserAgent;\n    console.log('Screen size check:', {\n      width: width,\n      height: window.innerHeight,\n      isMobile: this.isMobile,\n      isMobileUserAgent: isMobileUserAgent,\n      userAgent: userAgent\n    });\n  }\n  toggleSidebar() {\n    this.isSidebarOpen = !this.isSidebarOpen;\n    this.toggleBodyScroll();\n  }\n  closeSidebar() {\n    this.isSidebarOpen = false;\n    this.toggleBodyScroll();\n  }\n  toggleBodyScroll() {\n    if (this.isSidebarOpen) {\n      document.body.style.overflow = 'hidden';\n    } else {\n      document.body.style.overflow = '';\n    }\n  }\n  // Tab Menu Methods\n  toggleTabMenu() {\n    this.isTabMenuOpen = !this.isTabMenuOpen;\n    this.toggleBodyScroll();\n  }\n  closeTabMenu() {\n    this.isTabMenuOpen = false;\n    this.toggleBodyScroll();\n  }\n  openSidebarTab(tabType) {\n    this.currentSidebarTab = tabType;\n    this.isSidebarContentOpen = true;\n    this.isTabMenuOpen = false;\n    // Set title based on tab type\n    const titles = {\n      'trending': 'Trending Products',\n      'brands': 'Featured Brands',\n      'arrivals': 'New Arrivals',\n      'suggested': 'Suggested for You',\n      'influencers': 'Fashion Influencers',\n      'categories': 'Categories'\n    };\n    this.currentSidebarTitle = titles[tabType] || 'Discover';\n    this.toggleBodyScroll();\n  }\n  closeSidebarContent() {\n    this.isSidebarContentOpen = false;\n    this.currentSidebarTab = '';\n    this.toggleBodyScroll();\n  }\n  // TikTok-style interaction methods\n  toggleLike() {\n    this.isLiked = !this.isLiked;\n    // TODO: Implement like functionality with backend\n    console.log('Like toggled:', this.isLiked);\n  }\n  openComments() {\n    // TODO: Implement comments modal/page\n    console.log('Opening comments...');\n  }\n  shareContent() {\n    // TODO: Implement share functionality\n    console.log('Sharing content...');\n    if (navigator.share) {\n      navigator.share({\n        title: 'DFashion',\n        text: 'Check out this amazing fashion content!',\n        url: window.location.href\n      });\n    }\n  }\n  openMusic() {\n    // TODO: Implement music/audio functionality\n    console.log('Opening music...');\n  }\n  // Stories functionality\n  createStory() {\n    console.log('Create story clicked');\n    // TODO: Implement story creation\n  }\n  viewStory(story) {\n    console.log('View story:', story);\n    // TODO: Implement story viewer\n  }\n  trackByStoryId(index, story) {\n    return story.id || index;\n  }\n  // Enhanced touch interactions for mobile app\n  onStoryTouchStart(event, story) {\n    story.touching = true;\n    // Add haptic feedback if available\n    if ('vibrate' in navigator) {\n      navigator.vibrate(10);\n    }\n  }\n  onStoryTouchEnd(event, story) {\n    story.touching = false;\n  }\n  // TikTok-style interaction methods\n  onLikeClick() {\n    this.isLiked = !this.isLiked;\n    console.log('Like clicked:', this.isLiked);\n    // TODO: Implement like functionality with backend\n  }\n  onCommentClick() {\n    console.log('Comment clicked');\n    // TODO: Implement comment functionality\n  }\n  onShareClick() {\n    console.log('Share clicked');\n    // TODO: Implement share functionality\n  }\n  onBookmarkClick() {\n    console.log('Bookmark clicked');\n    // TODO: Implement bookmark functionality\n  }\n  // Mobile quick actions navigation methods\n  navigateToTrending() {\n    console.log('Navigate to trending');\n    // TODO: Implement navigation to trending page\n  }\n  navigateToNewArrivals() {\n    console.log('Navigate to new arrivals');\n    // TODO: Implement navigation to new arrivals page\n  }\n  navigateToOffers() {\n    console.log('Navigate to offers');\n    // TODO: Implement navigation to offers page\n  }\n  navigateToCategories() {\n    console.log('Navigate to categories');\n    // TODO: Implement navigation to categories page\n  }\n  navigateToWishlist() {\n    console.log('Navigate to wishlist');\n    // TODO: Implement navigation to wishlist page\n  }\n  navigateToCart() {\n    console.log('Navigate to cart');\n    // TODO: Implement navigation to cart page\n  }\n  static {\n    this.ɵfac = function HomeComponent_Factory(t) {\n      return new (t || HomeComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: HomeComponent,\n      selectors: [[\"app-home\"]],\n      hostBindings: function HomeComponent_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"resize\", function HomeComponent_resize_HostBindingHandler($event) {\n            return ctx.onResize($event);\n          }, false, i0.ɵɵresolveWindow);\n        }\n      },\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 10,\n      vars: 15,\n      consts: [[1, \"home-container\"], [\"class\", \"mobile-header instagram-style\", 4, \"ngIf\"], [\"class\", \"web-layout\", 4, \"ngIf\"], [\"class\", \"mobile-layout\", 4, \"ngIf\"], [\"class\", \"tab-menu-overlay\", 3, \"active\", \"click\", 4, \"ngIf\"], [\"class\", \"sidebar-overlay\", 3, \"active\", \"click\", 4, \"ngIf\"], [\"class\", \"instagram-tab-menu\", 3, \"active\", 4, \"ngIf\"], [\"class\", \"mobile-sidebar\", 3, \"active\", 4, \"ngIf\"], [\"class\", \"sidebar-content-modal\", 3, \"active\", 4, \"ngIf\"], [\"class\", \"instagram-bottom-nav\", 4, \"ngIf\"], [1, \"mobile-header\", \"instagram-style\"], [1, \"header-left\"], [1, \"app-logo\"], [\"name\", \"chevron-down\", 1, \"logo-dropdown\"], [1, \"header-right\"], [\"name\", \"heart-outline\", 1, \"header-icon\"], [1, \"menu-icon-container\", 3, \"click\"], [\"name\", \"grid-outline\", 1, \"header-icon\", \"menu-icon\"], [\"class\", \"notification-dot\", 4, \"ngIf\"], [\"name\", \"menu-outline\", 1, \"header-icon\", \"menu-icon\"], [1, \"notification-dot\"], [1, \"web-layout\"], [1, \"stories-section-container\"], [1, \"trending-now-section-container\"], [1, \"two-column-layout\"], [1, \"post-section\"], [1, \"sidebar-section\"], [1, \"desktop-sidebar\"], [1, \"mobile-layout\"], [1, \"content-grid\"], [1, \"main-content\"], [1, \"mobile-trending-now-section\"], [1, \"tab-menu-overlay\", 3, \"click\"], [1, \"sidebar-overlay\", 3, \"click\"], [1, \"instagram-tab-menu\"], [1, \"tab-menu-header\"], [\"name\", \"close-outline\", 1, \"close-icon\", 3, \"click\"], [1, \"tab-menu-grid\"], [1, \"tab-item\", 3, \"click\"], [1, \"tab-icon\", \"trending\"], [\"name\", \"trending-up\"], [1, \"tab-label\"], [1, \"tab-tooltip\"], [1, \"tab-icon\", \"brands\"], [\"name\", \"diamond\"], [1, \"tab-icon\", \"arrivals\"], [\"name\", \"sparkles\"], [1, \"tab-icon\", \"suggested\"], [\"name\", \"heart\"], [1, \"tab-icon\", \"influencers\"], [\"name\", \"people\"], [1, \"tab-icon\", \"categories\"], [\"name\", \"grid\"], [1, \"mobile-sidebar\"], [1, \"sidebar-header\"], [1, \"user-profile\"], [1, \"profile-avatar\"], [\"src\", \"assets/images/default-avatar.svg\", \"alt\", \"Profile\"], [1, \"profile-info\"], [1, \"sidebar-content\"], [1, \"sidebar-content-modal\"], [1, \"modal-header\"], [1, \"modal-content\"], [\"class\", \"sidebar-section\", 4, \"ngIf\"], [1, \"categories-grid\"], [\"class\", \"category-item\", 4, \"ngFor\", \"ngForOf\"], [1, \"category-item\"], [1, \"category-icon\"], [3, \"name\"], [1, \"instagram-bottom-nav\"], [1, \"nav-item\", \"active\"], [\"name\", \"home\"], [1, \"nav-item\"], [\"name\", \"search\"], [\"name\", \"add-circle-outline\"], [\"name\", \"play-circle-outline\"], [1, \"profile-avatar-nav\"]],\n      template: function HomeComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0);\n          i0.ɵɵtemplate(1, HomeComponent_div_1_Template, 12, 1, \"div\", 1)(2, HomeComponent_div_2_Template, 10, 0, \"div\", 2)(3, HomeComponent_div_3_Template, 8, 0, \"div\", 3)(4, HomeComponent_div_4_Template, 1, 2, \"div\", 4)(5, HomeComponent_div_5_Template, 1, 2, \"div\", 5)(6, HomeComponent_div_6_Template, 48, 2, \"div\", 6)(7, HomeComponent_div_7_Template, 13, 2, \"div\", 7)(8, HomeComponent_div_8_Template, 12, 9, \"div\", 8)(9, HomeComponent_div_9_Template, 12, 0, \"div\", 9);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"mobile-instagram\", ctx.isMobile)(\"sidebar-open\", ctx.isSidebarOpen)(\"mobile\", ctx.isMobile);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isMobile);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.isMobile);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isMobile);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isMobile);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isMobile);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isMobile);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isMobile);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isMobile);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isMobile);\n        }\n      },\n      dependencies: [CommonModule, i1.NgForOf, i1.NgIf, IonicModule, i2.IonIcon, ViewAddStoriesComponent, FeedComponent, SidebarComponent, TrendingNowComponent, TrendingProductsComponent, FeaturedBrandsComponent, NewArrivalsComponent, SuggestedForYouComponent, TopFashionInfluencersComponent],\n      styles: [\".home-container[_ngcontent-%COMP%] {\\n  padding: 20px 0;\\n  min-height: calc(100vh - 60px);\\n  position: relative;\\n  background: #ffffff;\\n}\\n.home-container.mobile-instagram[_ngcontent-%COMP%] {\\n  background: #ffffff !important;\\n  color: #262626 !important;\\n  padding: 0 !important;\\n  min-height: 100vh !important;\\n}\\n\\n.trending-now-section-container[_ngcontent-%COMP%] {\\n  width: 100%;\\n  max-width: 1200px;\\n  margin: 0 auto 24px auto;\\n  padding: 0 20px;\\n}\\n.trending-now-section-container[_ngcontent-%COMP%]   app-trending-now[_ngcontent-%COMP%] {\\n  display: block;\\n  width: 100%;\\n}\\n\\n.mobile-trending-now-section[_ngcontent-%COMP%] {\\n  margin: 16px 0;\\n  padding: 0 16px;\\n}\\n.mobile-trending-now-section[_ngcontent-%COMP%]   app-trending-now[_ngcontent-%COMP%] {\\n  display: block;\\n  width: 100%;\\n}\\n\\n@media (max-width: 1200px) {\\n  .trending-now-section-container[_ngcontent-%COMP%] {\\n    max-width: 100%;\\n    padding: 0 16px;\\n  }\\n}\\n@media (max-width: 768px) {\\n  .trending-now-section-container[_ngcontent-%COMP%] {\\n    margin-bottom: 16px;\\n    padding: 0 12px;\\n  }\\n  .mobile-trending-now-section[_ngcontent-%COMP%] {\\n    margin: 12px 0;\\n    padding: 0 12px;\\n  }\\n}\\n@media (max-width: 425px) {\\n  .trending-now-section-container[_ngcontent-%COMP%] {\\n    margin-bottom: 12px;\\n    padding: 0 8px;\\n  }\\n  .mobile-trending-now-section[_ngcontent-%COMP%] {\\n    margin: 8px 0;\\n    padding: 0 8px;\\n  }\\n}\\n.mobile-header[_ngcontent-%COMP%] {\\n  display: none;\\n  position: fixed;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  height: 60px;\\n  background: #ffffff;\\n  border-bottom: 1px solid #dbdbdb;\\n  z-index: 1001; \\n\\n  padding: 0 16px;\\n  align-items: center;\\n  justify-content: space-between;\\n}\\n.mobile-header.instagram-style[_ngcontent-%COMP%] {\\n  background: #ffffff;\\n  border-bottom: 1px solid #dbdbdb;\\n}\\n.mobile-header[_ngcontent-%COMP%]   .header-left[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n}\\n.mobile-header[_ngcontent-%COMP%]   .header-left[_ngcontent-%COMP%]   .app-logo[_ngcontent-%COMP%] {\\n  font-size: 28px;\\n  font-weight: 400;\\n  color: #262626;\\n  margin: 0;\\n  font-family: \\\"Billabong\\\", cursive, -apple-system, BlinkMacSystemFont, sans-serif;\\n  letter-spacing: 0.5px;\\n}\\n.mobile-header[_ngcontent-%COMP%]   .header-left[_ngcontent-%COMP%]   .logo-dropdown[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  color: #262626;\\n  margin-top: 2px;\\n}\\n.mobile-header[_ngcontent-%COMP%]   .header-right[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 16px;\\n}\\n.mobile-header[_ngcontent-%COMP%]   .header-right[_ngcontent-%COMP%]   .header-icon[_ngcontent-%COMP%] {\\n  font-size: 24px;\\n  color: #262626;\\n  cursor: pointer;\\n  transition: all 0.2s ease;\\n  padding: 8px;\\n  border-radius: 50%;\\n}\\n.mobile-header[_ngcontent-%COMP%]   .header-right[_ngcontent-%COMP%]   .header-icon[_ngcontent-%COMP%]:hover {\\n  background-color: rgba(0, 0, 0, 0.05);\\n}\\n.mobile-header[_ngcontent-%COMP%]   .header-right[_ngcontent-%COMP%]   .menu-icon-container[_ngcontent-%COMP%] {\\n  position: relative;\\n  cursor: pointer;\\n}\\n.mobile-header[_ngcontent-%COMP%]   .header-right[_ngcontent-%COMP%]   .menu-icon-container[_ngcontent-%COMP%]   .notification-dot[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: -2px;\\n  right: -2px;\\n  width: 8px;\\n  height: 8px;\\n  background: #ff3040;\\n  border-radius: 50%;\\n  border: 2px solid #ffffff;\\n}\\n\\n.instagram-stories-section[_ngcontent-%COMP%] {\\n  display: none;\\n  position: fixed;\\n  top: 60px;\\n  left: 0;\\n  right: 0;\\n  background: #ffffff;\\n  border-bottom: 1px solid #dbdbdb;\\n  z-index: 999;\\n  padding: 12px 0;\\n  height: 100px; \\n\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);\\n  backdrop-filter: blur(10px);\\n  -webkit-backdrop-filter: blur(10px);\\n  \\n\\n}\\n@supports ((-webkit-backdrop-filter: blur(10px)) or (backdrop-filter: blur(10px))) {\\n  .instagram-stories-section[_ngcontent-%COMP%] {\\n    background: rgba(255, 255, 255, 0.95);\\n  }\\n}\\n.instagram-stories-section[_ngcontent-%COMP%]   .stories-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 12px;\\n  padding: 0 16px;\\n  overflow-x: auto;\\n  overflow-y: hidden;\\n  scrollbar-width: none;\\n  -ms-overflow-style: none;\\n  height: 100%;\\n  align-items: center;\\n  min-width: max-content;\\n  scroll-behavior: smooth;\\n  position: relative;\\n  z-index: 998; \\n\\n  \\n\\n  -webkit-overflow-scrolling: touch;\\n  overscroll-behavior-x: contain;\\n  scroll-snap-type: x proximity;\\n  \\n\\n  will-change: scroll-position;\\n  transform: translateZ(0); \\n\\n  \\n\\n}\\n.instagram-stories-section[_ngcontent-%COMP%]   .stories-container[_ngcontent-%COMP%]::-webkit-scrollbar {\\n  display: none;\\n}\\n@media (min-width: 320px) and (max-width: 768px) {\\n  .instagram-stories-section[_ngcontent-%COMP%]   .stories-container[_ngcontent-%COMP%] {\\n    padding: 0 12px;\\n    gap: 10px;\\n    scroll-snap-type: x mandatory;\\n    \\n\\n    touch-action: pan-x;\\n    -webkit-overflow-scrolling: touch;\\n    overscroll-behavior-x: contain;\\n    \\n\\n    contain: layout style paint;\\n  }\\n}\\n@media (min-width: 320px) and (max-width: 480px) {\\n  .instagram-stories-section[_ngcontent-%COMP%]   .stories-container[_ngcontent-%COMP%] {\\n    padding: 0 8px;\\n    gap: 8px;\\n    \\n\\n    scroll-padding-left: 8px;\\n    scroll-padding-right: 8px;\\n  }\\n}\\n@media (min-width: 320px) and (max-width: 400px) {\\n  .instagram-stories-section[_ngcontent-%COMP%]   .stories-container[_ngcontent-%COMP%] {\\n    padding: 0 6px;\\n    gap: 6px;\\n    \\n\\n    scroll-padding-left: 6px;\\n    scroll-padding-right: 6px;\\n  }\\n}\\n.instagram-stories-section[_ngcontent-%COMP%]   .stories-container[_ngcontent-%COMP%]   .story-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  gap: 6px;\\n  min-width: 70px;\\n  max-width: 70px;\\n  cursor: pointer;\\n  flex-shrink: 0;\\n  transition: transform 0.2s ease;\\n  scroll-snap-align: start;\\n  scroll-snap-stop: normal;\\n  position: relative;\\n  \\n\\n}\\n.instagram-stories-section[_ngcontent-%COMP%]   .stories-container[_ngcontent-%COMP%]   .story-item[_ngcontent-%COMP%]:hover {\\n  transform: scale(1.05);\\n}\\n.instagram-stories-section[_ngcontent-%COMP%]   .stories-container[_ngcontent-%COMP%]   .story-item[_ngcontent-%COMP%]:active {\\n  transform: scale(0.95);\\n}\\n@media (min-width: 320px) and (max-width: 768px) {\\n  .instagram-stories-section[_ngcontent-%COMP%]   .stories-container[_ngcontent-%COMP%]   .story-item[_ngcontent-%COMP%] {\\n    min-width: 65px;\\n    max-width: 65px;\\n    gap: 5px;\\n    \\n\\n    padding: 4px;\\n    margin: -4px;\\n  }\\n}\\n@media (min-width: 320px) and (max-width: 480px) {\\n  .instagram-stories-section[_ngcontent-%COMP%]   .stories-container[_ngcontent-%COMP%]   .story-item[_ngcontent-%COMP%] {\\n    min-width: 60px;\\n    max-width: 60px;\\n    gap: 4px;\\n  }\\n}\\n@media (min-width: 320px) and (max-width: 400px) {\\n  .instagram-stories-section[_ngcontent-%COMP%]   .stories-container[_ngcontent-%COMP%]   .story-item[_ngcontent-%COMP%] {\\n    min-width: 55px;\\n    max-width: 55px;\\n    gap: 3px;\\n  }\\n}\\n@media (max-width: 320px) {\\n  .instagram-stories-section[_ngcontent-%COMP%]   .stories-container[_ngcontent-%COMP%]   .story-item[_ngcontent-%COMP%] {\\n    min-width: 50px;\\n    max-width: 50px;\\n    gap: 2px;\\n  }\\n}\\n.instagram-stories-section[_ngcontent-%COMP%]   .stories-container[_ngcontent-%COMP%]   .story-item.your-story[_ngcontent-%COMP%]   .story-avatar[_ngcontent-%COMP%] {\\n  position: relative;\\n}\\n.instagram-stories-section[_ngcontent-%COMP%]   .stories-container[_ngcontent-%COMP%]   .story-item.your-story[_ngcontent-%COMP%]   .story-avatar[_ngcontent-%COMP%]   .add-story-btn[_ngcontent-%COMP%] {\\n  position: absolute;\\n  bottom: -2px;\\n  right: -2px;\\n  width: 20px;\\n  height: 20px;\\n  background: #0095f6;\\n  border: 2px solid #ffffff;\\n  border-radius: 50%;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n}\\n.instagram-stories-section[_ngcontent-%COMP%]   .stories-container[_ngcontent-%COMP%]   .story-item.your-story[_ngcontent-%COMP%]   .story-avatar[_ngcontent-%COMP%]   .add-story-btn[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  color: white;\\n}\\n.instagram-stories-section[_ngcontent-%COMP%]   .stories-container[_ngcontent-%COMP%]   .story-item[_ngcontent-%COMP%]   .story-avatar[_ngcontent-%COMP%] {\\n  width: 60px;\\n  height: 60px;\\n  border-radius: 50%;\\n  overflow: hidden;\\n  border: 2px solid #dbdbdb;\\n  position: relative;\\n  flex-shrink: 0;\\n  transition: all 0.2s ease;\\n  \\n\\n}\\n.instagram-stories-section[_ngcontent-%COMP%]   .stories-container[_ngcontent-%COMP%]   .story-item[_ngcontent-%COMP%]   .story-avatar.has-story[_ngcontent-%COMP%] {\\n  border: 2px solid transparent;\\n  background: linear-gradient(45deg, #f09433 0%, #e6683c 25%, #dc2743 50%, #cc2366 75%, #bc1888 100%);\\n  padding: 2px;\\n  \\n\\n}\\n.instagram-stories-section[_ngcontent-%COMP%]   .stories-container[_ngcontent-%COMP%]   .story-item[_ngcontent-%COMP%]   .story-avatar.has-story[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  border-radius: 50%;\\n  border: 2px solid #ffffff;\\n}\\n.instagram-stories-section[_ngcontent-%COMP%]   .stories-container[_ngcontent-%COMP%]   .story-item[_ngcontent-%COMP%]   .story-avatar.has-story[_ngcontent-%COMP%]:not(.viewed) {\\n  animation: _ngcontent-%COMP%_storyPulse 2s infinite;\\n}\\n.instagram-stories-section[_ngcontent-%COMP%]   .stories-container[_ngcontent-%COMP%]   .story-item[_ngcontent-%COMP%]   .story-avatar.viewed[_ngcontent-%COMP%] {\\n  border: 2px solid #c7c7c7;\\n  background: #c7c7c7;\\n}\\n.instagram-stories-section[_ngcontent-%COMP%]   .stories-container[_ngcontent-%COMP%]   .story-item[_ngcontent-%COMP%]   .story-avatar.touching[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_storyTouchFeedback 0.2s ease;\\n}\\n.instagram-stories-section[_ngcontent-%COMP%]   .stories-container[_ngcontent-%COMP%]   .story-item[_ngcontent-%COMP%]   .story-avatar[_ngcontent-%COMP%]   .story-ring[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: -3px;\\n  left: -3px;\\n  right: -3px;\\n  bottom: -3px;\\n  border-radius: 50%;\\n  background: linear-gradient(45deg, #f09433 0%, #e6683c 25%, #dc2743 50%, #cc2366 75%, #bc1888 100%);\\n  z-index: -1;\\n  opacity: 0.8;\\n}\\n.instagram-stories-section[_ngcontent-%COMP%]   .stories-container[_ngcontent-%COMP%]   .story-item[_ngcontent-%COMP%]   .story-avatar[_ngcontent-%COMP%]   .story-gradient-ring[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: -5px;\\n  left: -5px;\\n  right: -5px;\\n  bottom: -5px;\\n  border-radius: 50%;\\n  background: linear-gradient(45deg, #f09433 0%, #e6683c 25%, #dc2743 50%, #cc2366 75%, #bc1888 100%);\\n  z-index: -2;\\n  opacity: 0.3;\\n  animation: _ngcontent-%COMP%_storyRingGradient 3s infinite;\\n  filter: blur(2px);\\n}\\n.instagram-stories-section[_ngcontent-%COMP%]   .stories-container[_ngcontent-%COMP%]   .story-item[_ngcontent-%COMP%]   .story-avatar[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 100%;\\n  object-fit: cover;\\n  border-radius: 50%;\\n  display: block;\\n  transition: transform 0.2s ease;\\n  position: relative;\\n  z-index: 1;\\n}\\n@media (min-width: 320px) and (max-width: 768px) {\\n  .instagram-stories-section[_ngcontent-%COMP%]   .stories-container[_ngcontent-%COMP%]   .story-item[_ngcontent-%COMP%]   .story-avatar[_ngcontent-%COMP%] {\\n    width: 55px;\\n    height: 55px;\\n  }\\n}\\n@media (min-width: 320px) and (max-width: 480px) {\\n  .instagram-stories-section[_ngcontent-%COMP%]   .stories-container[_ngcontent-%COMP%]   .story-item[_ngcontent-%COMP%]   .story-avatar[_ngcontent-%COMP%] {\\n    width: 50px;\\n    height: 50px;\\n  }\\n}\\n@media (min-width: 320px) and (max-width: 400px) {\\n  .instagram-stories-section[_ngcontent-%COMP%]   .stories-container[_ngcontent-%COMP%]   .story-item[_ngcontent-%COMP%]   .story-avatar[_ngcontent-%COMP%] {\\n    width: 45px;\\n    height: 45px;\\n  }\\n}\\n@media (max-width: 320px) {\\n  .instagram-stories-section[_ngcontent-%COMP%]   .stories-container[_ngcontent-%COMP%]   .story-item[_ngcontent-%COMP%]   .story-avatar[_ngcontent-%COMP%] {\\n    width: 40px;\\n    height: 40px;\\n  }\\n}\\n.instagram-stories-section[_ngcontent-%COMP%]   .stories-container[_ngcontent-%COMP%]   .story-item[_ngcontent-%COMP%]   .story-username[_ngcontent-%COMP%] {\\n  font-size: 11px;\\n  color: #262626;\\n  text-align: center;\\n  max-width: 70px;\\n  overflow: hidden;\\n  text-overflow: ellipsis;\\n  white-space: nowrap;\\n  line-height: 1.2;\\n  font-weight: 400;\\n  margin-top: 4px;\\n  \\n\\n  -webkit-font-smoothing: antialiased;\\n  -moz-osx-font-smoothing: grayscale;\\n  text-rendering: optimizeLegibility;\\n  \\n\\n}\\n@media (min-width: 320px) and (max-width: 768px) {\\n  .instagram-stories-section[_ngcontent-%COMP%]   .stories-container[_ngcontent-%COMP%]   .story-item[_ngcontent-%COMP%]   .story-username[_ngcontent-%COMP%] {\\n    font-size: 10px;\\n    max-width: 65px;\\n    font-weight: 500; \\n\\n  }\\n}\\n@media (min-width: 320px) and (max-width: 480px) {\\n  .instagram-stories-section[_ngcontent-%COMP%]   .stories-container[_ngcontent-%COMP%]   .story-item[_ngcontent-%COMP%]   .story-username[_ngcontent-%COMP%] {\\n    font-size: 9px;\\n    max-width: 60px;\\n    line-height: 1.1;\\n  }\\n}\\n@media (min-width: 320px) and (max-width: 400px) {\\n  .instagram-stories-section[_ngcontent-%COMP%]   .stories-container[_ngcontent-%COMP%]   .story-item[_ngcontent-%COMP%]   .story-username[_ngcontent-%COMP%] {\\n    font-size: 8px;\\n    max-width: 55px;\\n    line-height: 1.1;\\n  }\\n}\\n@media (max-width: 320px) {\\n  .instagram-stories-section[_ngcontent-%COMP%]   .stories-container[_ngcontent-%COMP%]   .story-item[_ngcontent-%COMP%]   .story-username[_ngcontent-%COMP%] {\\n    font-size: 7px;\\n    max-width: 50px;\\n    line-height: 1;\\n    font-weight: 600; \\n\\n  }\\n}\\n\\n@keyframes _ngcontent-%COMP%_storyPulse {\\n  0% {\\n    transform: scale(1) translateZ(0);\\n    box-shadow: 0 0 0 0 rgba(240, 148, 51, 0.7);\\n  }\\n  70% {\\n    transform: scale(1.05) translateZ(0);\\n    box-shadow: 0 0 0 10px rgba(240, 148, 51, 0);\\n  }\\n  100% {\\n    transform: scale(1) translateZ(0);\\n    box-shadow: 0 0 0 0 rgba(240, 148, 51, 0);\\n  }\\n}\\n@keyframes _ngcontent-%COMP%_storyRingGradient {\\n  0% {\\n    background: linear-gradient(45deg, #f09433 0%, #e6683c 25%, #dc2743 50%, #cc2366 75%, #bc1888 100%);\\n  }\\n  25% {\\n    background: linear-gradient(90deg, #f09433 0%, #e6683c 25%, #dc2743 50%, #cc2366 75%, #bc1888 100%);\\n  }\\n  50% {\\n    background: linear-gradient(135deg, #f09433 0%, #e6683c 25%, #dc2743 50%, #cc2366 75%, #bc1888 100%);\\n  }\\n  75% {\\n    background: linear-gradient(180deg, #f09433 0%, #e6683c 25%, #dc2743 50%, #cc2366 75%, #bc1888 100%);\\n  }\\n  100% {\\n    background: linear-gradient(45deg, #f09433 0%, #e6683c 25%, #dc2743 50%, #cc2366 75%, #bc1888 100%);\\n  }\\n}\\n@keyframes _ngcontent-%COMP%_storyTouchFeedback {\\n  0% {\\n    transform: scale(1) translateZ(0);\\n  }\\n  50% {\\n    transform: scale(0.95) translateZ(0);\\n  }\\n  100% {\\n    transform: scale(1) translateZ(0);\\n  }\\n}\\n.instagram-bottom-nav[_ngcontent-%COMP%] {\\n  display: none;\\n  position: fixed;\\n  bottom: 0;\\n  left: 0;\\n  right: 0;\\n  background: #ffffff;\\n  border-top: 1px solid #dbdbdb;\\n  justify-content: space-around;\\n  align-items: center;\\n  padding: 8px 0;\\n  z-index: 1000;\\n  height: 60px;\\n  box-shadow: 0 -1px 3px rgba(0, 0, 0, 0.1);\\n  padding-bottom: max(8px, env(safe-area-inset-bottom));\\n}\\n.instagram-bottom-nav[_ngcontent-%COMP%]   .nav-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n  cursor: pointer;\\n  padding: 4px 8px;\\n  border-radius: 8px;\\n  transition: all 0.2s ease;\\n  min-width: 44px;\\n  min-height: 44px;\\n  position: relative;\\n}\\n.instagram-bottom-nav[_ngcontent-%COMP%]   .nav-item[_ngcontent-%COMP%]:hover {\\n  background-color: rgba(0, 0, 0, 0.05);\\n}\\n.instagram-bottom-nav[_ngcontent-%COMP%]   .nav-item.active[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n  color: #262626;\\n  transform: scale(1.1);\\n}\\n.instagram-bottom-nav[_ngcontent-%COMP%]   .nav-item[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n  font-size: 24px;\\n  color: #8e8e8e;\\n  transition: all 0.2s ease;\\n}\\n.instagram-bottom-nav[_ngcontent-%COMP%]   .nav-item[_ngcontent-%COMP%]   .profile-avatar-nav[_ngcontent-%COMP%] {\\n  width: 24px;\\n  height: 24px;\\n  border-radius: 50%;\\n  overflow: hidden;\\n  border: 1px solid #8e8e8e;\\n}\\n.instagram-bottom-nav[_ngcontent-%COMP%]   .nav-item[_ngcontent-%COMP%]   .profile-avatar-nav.active[_ngcontent-%COMP%] {\\n  border: 2px solid #262626;\\n}\\n.instagram-bottom-nav[_ngcontent-%COMP%]   .nav-item[_ngcontent-%COMP%]   .profile-avatar-nav[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 100%;\\n  object-fit: cover;\\n}\\n.instagram-bottom-nav[_ngcontent-%COMP%]   .nav-item[_ngcontent-%COMP%]   .nav-badge[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 2px;\\n  right: 2px;\\n  background: #ff3040;\\n  color: white;\\n  font-size: 10px;\\n  font-weight: 600;\\n  padding: 2px 6px;\\n  border-radius: 10px;\\n  min-width: 16px;\\n  text-align: center;\\n  line-height: 1.2;\\n}\\n\\n.tab-menu-overlay[_ngcontent-%COMP%] {\\n  position: fixed;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  background: rgba(0, 0, 0, 0.8);\\n  z-index: 1500;\\n  opacity: 0;\\n  visibility: hidden;\\n  transition: all 0.3s ease;\\n}\\n.tab-menu-overlay.active[_ngcontent-%COMP%] {\\n  opacity: 1;\\n  visibility: visible;\\n}\\n\\n.instagram-tab-menu[_ngcontent-%COMP%] {\\n  position: fixed;\\n  bottom: 0;\\n  left: 0;\\n  right: 0;\\n  background: #000000;\\n  border-top-left-radius: 20px;\\n  border-top-right-radius: 20px;\\n  z-index: 1600;\\n  transform: translateY(100%);\\n  transition: transform 0.3s ease;\\n  max-height: 70vh;\\n  overflow-y: auto;\\n}\\n.instagram-tab-menu.active[_ngcontent-%COMP%] {\\n  transform: translateY(0);\\n}\\n.instagram-tab-menu[_ngcontent-%COMP%]   .tab-menu-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  padding: 20px 24px 16px;\\n  border-bottom: 1px solid #262626;\\n}\\n.instagram-tab-menu[_ngcontent-%COMP%]   .tab-menu-header[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  font-size: 20px;\\n  font-weight: 600;\\n  color: white;\\n  margin: 0;\\n}\\n.instagram-tab-menu[_ngcontent-%COMP%]   .tab-menu-header[_ngcontent-%COMP%]   .close-icon[_ngcontent-%COMP%] {\\n  font-size: 24px;\\n  color: #8e8e8e;\\n  cursor: pointer;\\n  padding: 8px;\\n  border-radius: 50%;\\n  transition: all 0.2s ease;\\n}\\n.instagram-tab-menu[_ngcontent-%COMP%]   .tab-menu-header[_ngcontent-%COMP%]   .close-icon[_ngcontent-%COMP%]:hover {\\n  background-color: rgba(255, 255, 255, 0.1);\\n  color: white;\\n}\\n.instagram-tab-menu[_ngcontent-%COMP%]   .tab-menu-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(3, 1fr);\\n  gap: 24px;\\n  padding: 24px;\\n}\\n.instagram-tab-menu[_ngcontent-%COMP%]   .tab-menu-grid[_ngcontent-%COMP%]   .tab-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  gap: 12px;\\n  cursor: pointer;\\n  padding: 16px;\\n  border-radius: 16px;\\n  transition: all 0.2s ease;\\n  position: relative;\\n}\\n.instagram-tab-menu[_ngcontent-%COMP%]   .tab-menu-grid[_ngcontent-%COMP%]   .tab-item[_ngcontent-%COMP%]:hover {\\n  background-color: rgba(255, 255, 255, 0.05);\\n  transform: scale(1.05);\\n}\\n.instagram-tab-menu[_ngcontent-%COMP%]   .tab-menu-grid[_ngcontent-%COMP%]   .tab-item[_ngcontent-%COMP%]   .tab-icon[_ngcontent-%COMP%] {\\n  width: 56px;\\n  height: 56px;\\n  border-radius: 50%;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  position: relative;\\n}\\n.instagram-tab-menu[_ngcontent-%COMP%]   .tab-menu-grid[_ngcontent-%COMP%]   .tab-item[_ngcontent-%COMP%]   .tab-icon[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n  font-size: 24px;\\n  color: white;\\n}\\n.instagram-tab-menu[_ngcontent-%COMP%]   .tab-menu-grid[_ngcontent-%COMP%]   .tab-item[_ngcontent-%COMP%]   .tab-icon.trending[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #ff6b6b, #ff8e53);\\n}\\n.instagram-tab-menu[_ngcontent-%COMP%]   .tab-menu-grid[_ngcontent-%COMP%]   .tab-item[_ngcontent-%COMP%]   .tab-icon.brands[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #4ecdc4, #44a08d);\\n}\\n.instagram-tab-menu[_ngcontent-%COMP%]   .tab-menu-grid[_ngcontent-%COMP%]   .tab-item[_ngcontent-%COMP%]   .tab-icon.arrivals[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #a8edea, #fed6e3);\\n}\\n.instagram-tab-menu[_ngcontent-%COMP%]   .tab-menu-grid[_ngcontent-%COMP%]   .tab-item[_ngcontent-%COMP%]   .tab-icon.arrivals[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n  color: #333;\\n}\\n.instagram-tab-menu[_ngcontent-%COMP%]   .tab-menu-grid[_ngcontent-%COMP%]   .tab-item[_ngcontent-%COMP%]   .tab-icon.suggested[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #ff9a9e, #fecfef);\\n}\\n.instagram-tab-menu[_ngcontent-%COMP%]   .tab-menu-grid[_ngcontent-%COMP%]   .tab-item[_ngcontent-%COMP%]   .tab-icon.suggested[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n  color: #333;\\n}\\n.instagram-tab-menu[_ngcontent-%COMP%]   .tab-menu-grid[_ngcontent-%COMP%]   .tab-item[_ngcontent-%COMP%]   .tab-icon.influencers[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #667eea, #764ba2);\\n}\\n.instagram-tab-menu[_ngcontent-%COMP%]   .tab-menu-grid[_ngcontent-%COMP%]   .tab-item[_ngcontent-%COMP%]   .tab-icon.categories[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #f093fb, #f5576c);\\n}\\n.instagram-tab-menu[_ngcontent-%COMP%]   .tab-menu-grid[_ngcontent-%COMP%]   .tab-item[_ngcontent-%COMP%]   .tab-label[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  font-weight: 600;\\n  color: white;\\n  text-align: center;\\n}\\n.instagram-tab-menu[_ngcontent-%COMP%]   .tab-menu-grid[_ngcontent-%COMP%]   .tab-item[_ngcontent-%COMP%]   .tab-tooltip[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  color: #8e8e8e;\\n  text-align: center;\\n  line-height: 1.3;\\n}\\n\\n.sidebar-content-modal[_ngcontent-%COMP%] {\\n  position: fixed;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  background: #000000;\\n  z-index: 1700;\\n  transform: translateX(100%);\\n  transition: transform 0.3s ease;\\n  overflow-y: auto;\\n}\\n.sidebar-content-modal.active[_ngcontent-%COMP%] {\\n  transform: translateX(0);\\n}\\n.sidebar-content-modal[_ngcontent-%COMP%]   .modal-header[_ngcontent-%COMP%] {\\n  position: sticky;\\n  top: 0;\\n  background: #000000;\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  padding: 20px 24px;\\n  border-bottom: 1px solid #262626;\\n  z-index: 10;\\n}\\n.sidebar-content-modal[_ngcontent-%COMP%]   .modal-header[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  font-size: 18px;\\n  font-weight: 600;\\n  color: white;\\n  margin: 0;\\n}\\n.sidebar-content-modal[_ngcontent-%COMP%]   .modal-header[_ngcontent-%COMP%]   .close-icon[_ngcontent-%COMP%] {\\n  font-size: 24px;\\n  color: #8e8e8e;\\n  cursor: pointer;\\n  padding: 8px;\\n  border-radius: 50%;\\n  transition: all 0.2s ease;\\n}\\n.sidebar-content-modal[_ngcontent-%COMP%]   .modal-header[_ngcontent-%COMP%]   .close-icon[_ngcontent-%COMP%]:hover {\\n  background-color: rgba(255, 255, 255, 0.1);\\n  color: white;\\n}\\n.sidebar-content-modal[_ngcontent-%COMP%]   .modal-content[_ngcontent-%COMP%] {\\n  padding: 0;\\n}\\n.sidebar-content-modal[_ngcontent-%COMP%]   .modal-content[_ngcontent-%COMP%]   .sidebar-section[_ngcontent-%COMP%] {\\n  background: #000000;\\n  color: white;\\n  min-height: calc(100vh - 80px);\\n}\\n.sidebar-content-modal[_ngcontent-%COMP%]   .modal-content[_ngcontent-%COMP%]   .sidebar-section[_ngcontent-%COMP%]     * {\\n  background-color: transparent !important;\\n  color: white !important;\\n}\\n.sidebar-content-modal[_ngcontent-%COMP%]   .modal-content[_ngcontent-%COMP%]   .sidebar-section[_ngcontent-%COMP%]     .card, .sidebar-content-modal[_ngcontent-%COMP%]   .modal-content[_ngcontent-%COMP%]   .sidebar-section[_ngcontent-%COMP%]     .section, .sidebar-content-modal[_ngcontent-%COMP%]   .modal-content[_ngcontent-%COMP%]   .sidebar-section[_ngcontent-%COMP%]     .item {\\n  background: #1a1a1a !important;\\n  border: 1px solid #262626 !important;\\n}\\n.sidebar-content-modal[_ngcontent-%COMP%]   .modal-content[_ngcontent-%COMP%]   .sidebar-section[_ngcontent-%COMP%]     .text-dark {\\n  color: white !important;\\n}\\n.sidebar-content-modal[_ngcontent-%COMP%]   .modal-content[_ngcontent-%COMP%]   .sidebar-section[_ngcontent-%COMP%]     .bg-white {\\n  background: #1a1a1a !important;\\n}\\n.sidebar-content-modal[_ngcontent-%COMP%]   .modal-content[_ngcontent-%COMP%]   .categories-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(2, 1fr);\\n  gap: 16px;\\n  padding: 24px;\\n}\\n.sidebar-content-modal[_ngcontent-%COMP%]   .modal-content[_ngcontent-%COMP%]   .categories-grid[_ngcontent-%COMP%]   .category-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  gap: 12px;\\n  padding: 20px;\\n  background: #1a1a1a;\\n  border-radius: 16px;\\n  border: 1px solid #262626;\\n  cursor: pointer;\\n  transition: all 0.2s ease;\\n}\\n.sidebar-content-modal[_ngcontent-%COMP%]   .modal-content[_ngcontent-%COMP%]   .categories-grid[_ngcontent-%COMP%]   .category-item[_ngcontent-%COMP%]:hover {\\n  background: #262626;\\n  transform: scale(1.02);\\n}\\n.sidebar-content-modal[_ngcontent-%COMP%]   .modal-content[_ngcontent-%COMP%]   .categories-grid[_ngcontent-%COMP%]   .category-item[_ngcontent-%COMP%]   .category-icon[_ngcontent-%COMP%] {\\n  width: 48px;\\n  height: 48px;\\n  border-radius: 50%;\\n  background: linear-gradient(135deg, #667eea, #764ba2);\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n}\\n.sidebar-content-modal[_ngcontent-%COMP%]   .modal-content[_ngcontent-%COMP%]   .categories-grid[_ngcontent-%COMP%]   .category-item[_ngcontent-%COMP%]   .category-icon[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n  font-size: 24px;\\n  color: white;\\n}\\n.sidebar-content-modal[_ngcontent-%COMP%]   .modal-content[_ngcontent-%COMP%]   .categories-grid[_ngcontent-%COMP%]   .category-item[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  font-weight: 600;\\n  color: white;\\n  text-align: center;\\n}\\n\\n.web-layout[_ngcontent-%COMP%] {\\n  max-width: 1200px;\\n  margin: 0 auto;\\n  padding: 80px 20px 20px 20px;\\n  min-height: calc(100vh - 100px);\\n}\\n@media (min-width: 769px) {\\n  .web-layout[_ngcontent-%COMP%] {\\n    display: block;\\n  }\\n}\\n@media (max-width: 768px) {\\n  .web-layout[_ngcontent-%COMP%] {\\n    display: none;\\n  }\\n}\\n\\n.stories-section-container[_ngcontent-%COMP%] {\\n  width: 100%;\\n  margin-bottom: 20px;\\n}\\n\\n.two-column-layout[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 20px;\\n}\\n\\n.post-section[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n\\n.sidebar-section[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n\\n@media (min-width: 769px) {\\n  .mobile-layout[_ngcontent-%COMP%] {\\n    display: none;\\n  }\\n}\\n@media (max-width: 768px) {\\n  .mobile-layout[_ngcontent-%COMP%] {\\n    display: block;\\n  }\\n}\\n\\n.content-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: 1fr 400px;\\n  gap: 40px;\\n  max-width: 1000px;\\n  margin: 0 auto;\\n  background: #ffffff;\\n  padding: 0 20px;\\n}\\n\\n.main-content[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 24px;\\n}\\n\\n.desktop-sidebar[_ngcontent-%COMP%] {\\n  display: block;\\n}\\n\\n.sidebar-overlay[_ngcontent-%COMP%] {\\n  position: fixed;\\n  top: 0;\\n  left: 0;\\n  width: 100%;\\n  height: 100%;\\n  background: rgba(0, 0, 0, 0.65);\\n  z-index: 200;\\n  opacity: 0;\\n  visibility: hidden;\\n  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);\\n}\\n.sidebar-overlay.active[_ngcontent-%COMP%] {\\n  opacity: 1;\\n  visibility: visible;\\n}\\n\\n.mobile-sidebar[_ngcontent-%COMP%] {\\n  position: fixed;\\n  top: 0;\\n  right: -100%;\\n  width: 85%;\\n  max-width: 400px;\\n  height: 100%;\\n  background: #ffffff;\\n  z-index: 300;\\n  transition: right 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);\\n  box-shadow: -2px 0 10px rgba(0, 0, 0, 0.1);\\n  display: flex;\\n  flex-direction: column;\\n}\\n.mobile-sidebar.active[_ngcontent-%COMP%] {\\n  right: 0;\\n}\\n.mobile-sidebar[_ngcontent-%COMP%]   .sidebar-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: space-between;\\n  padding: 20px 16px;\\n  border-bottom: 1px solid #dbdbdb;\\n  background: #fafafa;\\n}\\n.mobile-sidebar[_ngcontent-%COMP%]   .sidebar-header[_ngcontent-%COMP%]   .user-profile[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 12px;\\n}\\n.mobile-sidebar[_ngcontent-%COMP%]   .sidebar-header[_ngcontent-%COMP%]   .user-profile[_ngcontent-%COMP%]   .profile-avatar[_ngcontent-%COMP%] {\\n  width: 40px;\\n  height: 40px;\\n  border-radius: 50%;\\n  overflow: hidden;\\n  border: 2px solid #dbdbdb;\\n}\\n.mobile-sidebar[_ngcontent-%COMP%]   .sidebar-header[_ngcontent-%COMP%]   .user-profile[_ngcontent-%COMP%]   .profile-avatar[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 100%;\\n  object-fit: cover;\\n}\\n.mobile-sidebar[_ngcontent-%COMP%]   .sidebar-header[_ngcontent-%COMP%]   .user-profile[_ngcontent-%COMP%]   .profile-info[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 16px;\\n  font-weight: 600;\\n  color: #262626;\\n}\\n.mobile-sidebar[_ngcontent-%COMP%]   .sidebar-header[_ngcontent-%COMP%]   .user-profile[_ngcontent-%COMP%]   .profile-info[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 14px;\\n  color: #8e8e8e;\\n}\\n.mobile-sidebar[_ngcontent-%COMP%]   .sidebar-header[_ngcontent-%COMP%]   .close-icon[_ngcontent-%COMP%] {\\n  font-size: 24px;\\n  color: #262626;\\n  cursor: pointer;\\n  padding: 8px;\\n  margin: -8px;\\n  transition: color 0.2s ease;\\n}\\n.mobile-sidebar[_ngcontent-%COMP%]   .sidebar-header[_ngcontent-%COMP%]   .close-icon[_ngcontent-%COMP%]:hover {\\n  color: #8e8e8e;\\n}\\n.mobile-sidebar[_ngcontent-%COMP%]   .sidebar-content[_ngcontent-%COMP%] {\\n  flex: 1;\\n  overflow-y: auto;\\n  padding: 16px 0;\\n}\\n.mobile-sidebar[_ngcontent-%COMP%]   .sidebar-content[_ngcontent-%COMP%]::-webkit-scrollbar {\\n  width: 4px;\\n}\\n.mobile-sidebar[_ngcontent-%COMP%]   .sidebar-content[_ngcontent-%COMP%]::-webkit-scrollbar-track {\\n  background: #f1f1f1;\\n}\\n.mobile-sidebar[_ngcontent-%COMP%]   .sidebar-content[_ngcontent-%COMP%]::-webkit-scrollbar-thumb {\\n  background: #c1c1c1;\\n  border-radius: 2px;\\n}\\n.mobile-sidebar[_ngcontent-%COMP%]   .sidebar-content[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover {\\n  background: #a8a8a8;\\n}\\n\\n@media (max-width: 1024px) {\\n  .content-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n    max-width: 100%;\\n    padding: 0 16px;\\n    \\n\\n  }\\n}\\n@media (max-width: 1024px) and (min-width: 768px) {\\n  .content-grid[_ngcontent-%COMP%] {\\n    max-width: 768px;\\n    margin: 0 auto;\\n    padding: 0 24px;\\n  }\\n}\\n@media (max-width: 1024px) {\\n  .desktop-sidebar[_ngcontent-%COMP%] {\\n    display: none;\\n  }\\n  .mobile-header[_ngcontent-%COMP%] {\\n    display: flex;\\n  }\\n  .home-container[_ngcontent-%COMP%] {\\n    padding-top: 80px;\\n    \\n\\n  }\\n}\\n@media (max-width: 1024px) and (min-width: 768px) {\\n  .home-container[_ngcontent-%COMP%] {\\n    padding: 80px 0 0 0;\\n  }\\n}\\n.home-container.mobile-instagram[_ngcontent-%COMP%] {\\n  background: #ffffff !important;\\n  min-height: 100vh !important;\\n}\\n.home-container.mobile-instagram[_ngcontent-%COMP%]   .mobile-header[_ngcontent-%COMP%] {\\n  display: flex !important;\\n  visibility: visible !important;\\n  opacity: 1 !important;\\n  width: 100% !important;\\n  height: 60px !important;\\n  background: #ffffff !important;\\n  border-bottom: 1px solid #dbdbdb !important;\\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1) !important;\\n}\\n.home-container.mobile-instagram[_ngcontent-%COMP%]   .instagram-stories-section[_ngcontent-%COMP%] {\\n  display: block !important;\\n  visibility: visible !important;\\n  opacity: 1 !important;\\n  width: 100% !important;\\n  height: 100px !important;\\n  padding: 8px 0 !important;\\n  background: #ffffff !important;\\n  border-bottom: 1px solid #dbdbdb !important;\\n  \\n\\n  transform: translateZ(0) !important;\\n  will-change: scroll-position !important;\\n  contain: layout style paint !important;\\n  \\n\\n  backdrop-filter: blur(10px) !important;\\n  -webkit-backdrop-filter: blur(10px) !important;\\n}\\n@supports ((-webkit-backdrop-filter: blur(10px)) or (backdrop-filter: blur(10px))) {\\n  .home-container.mobile-instagram[_ngcontent-%COMP%]   .instagram-stories-section[_ngcontent-%COMP%] {\\n    background: rgba(255, 255, 255, 0.95) !important;\\n  }\\n}\\n.home-container.mobile-instagram[_ngcontent-%COMP%]   .content-grid[_ngcontent-%COMP%] {\\n  \\n\\n  grid-template-columns: 1fr !important;\\n  padding: 160px 0 60px 0 !important; \\n\\n  background: #ffffff !important;\\n  gap: 0 !important;\\n  margin: 0 !important;\\n  max-width: 100% !important;\\n  min-height: calc(100vh - 220px) !important;\\n  overflow-x: hidden !important;\\n  position: relative !important;\\n  z-index: 1 !important; \\n\\n  \\n\\n  \\n\\n  \\n\\n  \\n\\n}\\n@media (min-width: 768px) {\\n  .home-container.mobile-instagram[_ngcontent-%COMP%]   .content-grid[_ngcontent-%COMP%] {\\n    padding: 160px 16px 60px 16px !important; \\n\\n    max-width: 768px !important;\\n    margin: 0 auto !important; \\n\\n    gap: 16px !important;\\n  }\\n}\\n@media (min-width: 1024px) {\\n  .home-container.mobile-instagram[_ngcontent-%COMP%]   .content-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr 300px !important; \\n\\n    padding: 160px 24px 60px 24px !important;\\n    max-width: 1200px !important;\\n    gap: 32px !important;\\n  }\\n}\\n@media (min-width: 1200px) {\\n  .home-container.mobile-instagram[_ngcontent-%COMP%]   .content-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr 400px !important; \\n\\n    padding: 160px 32px 60px 32px !important;\\n    max-width: 1400px !important;\\n    gap: 40px !important;\\n  }\\n}\\n@media (min-width: 1440px) {\\n  .home-container.mobile-instagram[_ngcontent-%COMP%]   .content-grid[_ngcontent-%COMP%] {\\n    padding: 160px 48px 60px 48px !important;\\n    max-width: 1600px !important;\\n    gap: 48px !important;\\n  }\\n}\\n.home-container.mobile-instagram[_ngcontent-%COMP%]   .mobile-bottom-nav[_ngcontent-%COMP%] {\\n  background: #ffffff !important;\\n  border-top: 1px solid #dbdbdb !important;\\n  box-shadow: 0 -1px 3px rgba(0, 0, 0, 0.1) !important;\\n}\\n\\n@media (min-width: 320px) and (max-width: 768px) {\\n  .home-container[_ngcontent-%COMP%] {\\n    background: #fafafa !important;\\n    padding: 0 !important;\\n  }\\n  .home-container.mobile-instagram[_ngcontent-%COMP%] {\\n    background: #fafafa !important;\\n  }\\n  .home-container.mobile-instagram[_ngcontent-%COMP%]   .content-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr !important;\\n    padding: 5px 0 !important;\\n    background: #ffffff !important;\\n    gap: 0 !important;\\n    margin: 0 !important;\\n    max-width: 100% !important;\\n    min-height: calc(100vh - 220px) !important;\\n    overflow-x: hidden !important;\\n    position: relative !important;\\n    z-index: 1 !important;\\n  }\\n  .content-grid[_ngcontent-%COMP%] {\\n    padding: 0;\\n    margin: 0;\\n    background: #fafafa;\\n  }\\n  .content-grid[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%] {\\n    background: white;\\n    border-radius: 0;\\n    box-shadow: none;\\n    margin: 0;\\n    padding: 0;\\n  }\\n  .mobile-header[_ngcontent-%COMP%] {\\n    display: flex !important;\\n    visibility: visible !important;\\n    opacity: 1 !important;\\n    width: 100% !important;\\n    height: 60px !important;\\n  }\\n  .instagram-stories-section[_ngcontent-%COMP%] {\\n    display: block !important;\\n    visibility: visible !important;\\n    opacity: 1 !important;\\n    width: 100% !important;\\n    height: 100px !important;\\n    padding: 8px 0 !important;\\n    background: #ffffff !important;\\n    border-bottom: 1px solid #dbdbdb !important;\\n  }\\n  app-view-add-stories[_ngcontent-%COMP%] {\\n    display: block !important;\\n    width: 100% !important;\\n    padding: 12px 0 !important;\\n    background: #ffffff !important;\\n    border-bottom: 1px solid #dbdbdb !important;\\n  }\\n  .instagram-bottom-nav[_ngcontent-%COMP%] {\\n    display: flex !important;\\n  }\\n  .desktop-sidebar[_ngcontent-%COMP%] {\\n    display: none !important;\\n  }\\n  .home-container[_ngcontent-%COMP%] {\\n    background: #ffffff !important;\\n    min-height: 100vh;\\n    padding: 0 !important;\\n  }\\n  .content-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr !important;\\n    padding: 160px 0 60px 0 !important;\\n    background: #ffffff !important;\\n    gap: 0 !important;\\n    margin: 0 !important;\\n    max-width: 100% !important;\\n    min-height: calc(100vh - 220px) !important;\\n    overflow-x: hidden !important;\\n  }\\n  .main-content[_ngcontent-%COMP%] {\\n    background: #ffffff !important;\\n    color: #262626 !important;\\n    gap: 0;\\n    padding: 0 8px 40px 8px;\\n    width: 100% !important;\\n    max-width: 100% !important;\\n    box-sizing: border-box;\\n    overflow: visible !important;\\n  }\\n  .mobile-sidebar[_ngcontent-%COMP%] {\\n    width: 90%;\\n    background: #ffffff;\\n    color: #262626;\\n  }\\n  .sidebar-overlay[_ngcontent-%COMP%] {\\n    background: rgba(0, 0, 0, 0.8);\\n  }\\n}\\n@media (max-width: 480px) {\\n  .mobile-sidebar[_ngcontent-%COMP%] {\\n    width: 95%;\\n  }\\n  .mobile-header[_ngcontent-%COMP%]   .header-right[_ngcontent-%COMP%] {\\n    gap: 12px;\\n  }\\n}\\n@media (max-width: 480px) and (max-width: 480px) {\\n  .content-grid[_ngcontent-%COMP%] {\\n    padding: 160px 0 60px 0 !important;\\n    min-height: calc(100vh - 220px) !important;\\n  }\\n  .instagram-stories-section[_ngcontent-%COMP%] {\\n    height: 100px !important;\\n    padding: 8px 0 !important;\\n  }\\n  app-view-add-stories[_ngcontent-%COMP%] {\\n    display: block !important;\\n    width: 100% !important;\\n    padding: 8px 0 !important;\\n  }\\n}\\n@media (max-width: 480px) and (max-width: 400px) {\\n  .content-grid[_ngcontent-%COMP%] {\\n    padding: 160px 0 60px 0 !important;\\n    min-height: calc(100vh - 220px) !important;\\n  }\\n  .instagram-stories-section[_ngcontent-%COMP%] {\\n    height: 100px !important;\\n    padding: 8px 0 !important;\\n  }\\n  app-view-add-stories[_ngcontent-%COMP%] {\\n    display: block !important;\\n    width: 100% !important;\\n    padding: 8px 0 !important;\\n  }\\n  .main-content[_ngcontent-%COMP%] {\\n    width: 100% !important;\\n    max-width: 100% !important;\\n    overflow-x: hidden !important;\\n  }\\n}\\n@media (max-width: 480px) and (max-width: 360px) {\\n  .content-grid[_ngcontent-%COMP%] {\\n    padding: 160px 0 80px 0 !important;\\n    min-height: calc(100vh - 240px) !important;\\n  }\\n  .main-content[_ngcontent-%COMP%] {\\n    padding: 0 4px 50px 4px !important;\\n    overflow: visible !important;\\n  }\\n  .instagram-stories-section[_ngcontent-%COMP%] {\\n    height: 100px !important;\\n    padding: 8px 0 !important;\\n  }\\n  app-view-add-stories[_ngcontent-%COMP%] {\\n    display: block !important;\\n    width: 100% !important;\\n    padding: 8px 0 !important;\\n  }\\n}\\n@media (max-width: 480px) and (max-width: 425px) and (min-width: 320px) {\\n  .content-grid[_ngcontent-%COMP%] {\\n    padding: 160px 0 100px 0 !important;\\n    min-height: calc(100vh - 260px) !important;\\n  }\\n  .main-content[_ngcontent-%COMP%] {\\n    padding: 0 6px 60px 6px !important;\\n    overflow: visible !important;\\n    position: relative;\\n  }\\n  app-feed[_ngcontent-%COMP%] {\\n    padding-bottom: 40px !important;\\n    overflow: visible !important;\\n  }\\n}\\n@media (max-width: 480px) and (max-width: 320px) {\\n  .content-grid[_ngcontent-%COMP%] {\\n    padding: 160px 0 120px 0 !important;\\n    min-height: calc(100vh - 280px) !important;\\n  }\\n  .main-content[_ngcontent-%COMP%] {\\n    padding: 0 2px 70px 2px !important;\\n    overflow: visible !important;\\n  }\\n  app-feed[_ngcontent-%COMP%] {\\n    padding-bottom: 50px !important;\\n    overflow: visible !important;\\n  }\\n}\\n@media (min-width: 769px) {\\n  .mobile-header[_ngcontent-%COMP%], .instagram-stories-section[_ngcontent-%COMP%], .instagram-bottom-nav[_ngcontent-%COMP%] {\\n    display: none !important;\\n    visibility: hidden !important;\\n    opacity: 0 !important;\\n  }\\n  .desktop-sidebar[_ngcontent-%COMP%] {\\n    display: block;\\n  }\\n  .mobile-sidebar[_ngcontent-%COMP%], .sidebar-overlay[_ngcontent-%COMP%] {\\n    display: none !important;\\n  }\\n  .home-container[_ngcontent-%COMP%] {\\n    background: #ffffff;\\n    padding: 20px 20px 0 20px;\\n  }\\n  .content-grid[_ngcontent-%COMP%] {\\n    background: #ffffff;\\n    color: #262626;\\n    padding: 0;\\n    margin: 0 auto;\\n    padding-top: 0;\\n    \\n\\n    grid-template-columns: 1fr 300px;\\n    gap: 32px;\\n    max-width: 1000px;\\n    \\n\\n    \\n\\n    \\n\\n  }\\n}\\n@media (min-width: 769px) and (min-width: 1024px) {\\n  .content-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr 350px;\\n    gap: 36px;\\n    max-width: 1200px;\\n  }\\n}\\n@media (min-width: 769px) and (min-width: 1200px) {\\n  .content-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr 400px;\\n    gap: 40px;\\n    max-width: 1400px;\\n  }\\n}\\n@media (min-width: 769px) and (min-width: 1440px) {\\n  .content-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr 450px;\\n    gap: 48px;\\n    max-width: 1600px;\\n  }\\n}\\n@media (min-width: 769px) {\\n  .main-content[_ngcontent-%COMP%] {\\n    background: #ffffff;\\n    color: #262626;\\n  }\\n  .instagram-tab-menu[_ngcontent-%COMP%], .tab-menu-overlay[_ngcontent-%COMP%], .sidebar-content-modal[_ngcontent-%COMP%] {\\n    display: none !important;\\n  }\\n}\\n@media (min-width: 320px) and (max-width: 768px) {\\n  .home-container.mobile-instagram[_ngcontent-%COMP%]   .content-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr !important;\\n    padding: 5px 0 !important;\\n    background: #ffffff !important;\\n    gap: 0 !important;\\n    margin: 0 !important;\\n    max-width: 100% !important;\\n    min-height: calc(100vh - 220px) !important;\\n    overflow-x: hidden !important;\\n    position: relative !important;\\n    z-index: 1 !important;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "IonicModule", "ViewAddStoriesComponent", "FeedComponent", "SidebarComponent", "TrendingNowComponent", "TrendingProductsComponent", "FeaturedBrandsComponent", "NewArrivalsComponent", "SuggestedForYouComponent", "TopFashionInfluencersComponent", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵelement", "ɵɵlistener", "HomeComponent_div_1_Template_div_click_7_listener", "ɵɵrestoreView", "_r1", "ctx_r1", "ɵɵnextContext", "ɵɵresetView", "toggleTabMenu", "ɵɵtemplate", "HomeComponent_div_1_div_9_Template", "HomeComponent_div_1_Template_div_click_10_listener", "toggleSidebar", "ɵɵadvance", "ɵɵproperty", "hasNotifications", "HomeComponent_div_4_Template_div_click_0_listener", "_r3", "closeTabMenu", "ɵɵclassProp", "isTabMenuOpen", "HomeComponent_div_5_Template_div_click_0_listener", "_r4", "closeSidebar", "isSidebarOpen", "HomeComponent_div_6_Template_ion_icon_click_4_listener", "_r5", "HomeComponent_div_6_Template_div_click_6_listener", "openSidebarTab", "HomeComponent_div_6_Template_div_click_13_listener", "HomeComponent_div_6_Template_div_click_20_listener", "HomeComponent_div_6_Template_div_click_27_listener", "HomeComponent_div_6_Template_div_click_34_listener", "HomeComponent_div_6_Template_div_click_41_listener", "HomeComponent_div_7_Template_ion_icon_click_10_listener", "_r6", "category_r8", "icon", "ɵɵtextInterpolate", "name", "HomeComponent_div_8_div_11_div_2_Template", "categories", "HomeComponent_div_8_Template_ion_icon_click_4_listener", "_r7", "closeSidebarContent", "HomeComponent_div_8_div_6_Template", "HomeComponent_div_8_div_7_Template", "HomeComponent_div_8_div_8_Template", "HomeComponent_div_8_div_9_Template", "HomeComponent_div_8_div_10_Template", "HomeComponent_div_8_div_11_Template", "isSidebarContentOpen", "currentSidebarTitle", "currentSidebarTab", "HomeComponent", "constructor", "isMobile", "window", "isLiked", "instagramStories", "id", "username", "avatar", "hasStory", "viewed", "touching", "preventScroll", "e", "preventDefault", "ngOnInit", "checkScreenSize", "console", "log", "length", "document", "addEventListener", "passive", "ngOnDestroy", "removeEventListener", "onResize", "event", "width", "innerWidth", "userAgent", "navigator", "isMobileUserAgent", "test", "height", "innerHeight", "toggleBodyScroll", "body", "style", "overflow", "tabType", "titles", "toggleLike", "openComments", "shareContent", "share", "title", "text", "url", "location", "href", "openMusic", "createStory", "viewStory", "story", "trackByStoryId", "index", "onStoryTouchStart", "vibrate", "onStoryTouchEnd", "onLikeClick", "onCommentClick", "onShareClick", "onBookmarkClick", "navigateToTrending", "navigateToNewArrivals", "navigateToOffers", "navigateToCategories", "navigateToWishlist", "navigateToCart", "selectors", "hostBindings", "HomeComponent_HostBindings", "rf", "ctx", "HomeComponent_resize_HostBindingHandler", "$event", "ɵɵresolveWindow", "HomeComponent_div_1_Template", "HomeComponent_div_2_Template", "HomeComponent_div_3_Template", "HomeComponent_div_4_Template", "HomeComponent_div_5_Template", "HomeComponent_div_6_Template", "HomeComponent_div_7_Template", "HomeComponent_div_8_Template", "HomeComponent_div_9_Template", "i1", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "i2", "IonIcon", "styles"], "sources": ["E:\\Fashion\\DFashionFrontend\\frontend\\src\\app\\features\\home\\pages\\home\\home.component.ts", "E:\\Fashion\\DFashionFrontend\\frontend\\src\\app\\features\\home\\pages\\home\\home.component.html"], "sourcesContent": ["import { <PERSON>mpo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, HostListener } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { IonicModule } from '@ionic/angular';\n\nimport { ViewAddStoriesComponent } from '../../components/view-add-stories/view-add-stories.component';\nimport { FeedComponent } from '../../components/feed/feed.component';\nimport { SidebarComponent } from '../../components/sidebar/sidebar.component';\nimport { TrendingNowComponent } from '../../components/trending-now/trending-now.component';\nimport { TrendingProductsComponent } from '../../components/trending-products/trending-products.component';\nimport { FeaturedBrandsComponent } from '../../components/featured-brands/featured-brands.component';\nimport { NewArrivalsComponent } from '../../components/new-arrivals/new-arrivals.component';\nimport { SuggestedForYouComponent } from '../../components/suggested-for-you/suggested-for-you.component';\nimport { TopFashionInfluencersComponent } from '../../components/top-fashion-influencers/top-fashion-influencers.component';\nimport { ShopByCategoryComponent } from '../../components/shop-by-category/shop-by-category.component';\n\n@Component({\n  selector: 'app-home',\n  standalone: true,\n  imports: [\n    CommonModule,\n    IonicModule,\n    ViewAddStoriesComponent,\n    FeedComponent,\n    SidebarComponent,\n    TrendingNowComponent,\n    TrendingProductsComponent,\n    FeaturedBrandsComponent,\n    NewArrivalsComponent,\n    SuggestedForYouComponent,\n    TopFashionInfluencersComponent,\n    ShopByCategoryComponent\n  ],\n  templateUrl: './home.component.html',\n  styleUrls: ['./home.component.scss']\n})\nexport class HomeComponent implements OnInit, OnDestroy {\n  isMobile = false;\n  isSidebarOpen = false;\n  isTabMenuOpen = false;\n  isSidebarContentOpen = false;\n  currentSidebarTab = '';\n  currentSidebarTitle = '';\n  hasNotifications = true; // Example notification state\n  window = window; // For template access\n\n  // TikTok-style interaction states\n  isLiked = false;\n\n  // Instagram Stories Data - Enhanced for responsive design and mobile app\n  instagramStories = [\n    {\n      id: 1,\n      username: 'zara',\n      avatar: '/assets/images/default-avatar.svg',\n      hasStory: true,\n      viewed: false,\n      touching: false\n    },\n    {\n      id: 2,\n      username: 'nike',\n      avatar: '/assets/images/default-avatar.svg',\n      hasStory: true,\n      viewed: false,\n      touching: false\n    },\n    {\n      id: 3,\n      username: 'adidas',\n      avatar: '/assets/images/default-avatar.svg',\n      hasStory: true,\n      viewed: true,\n      touching: false\n    },\n    {\n      id: 4,\n      username: 'h&m',\n      avatar: '/assets/images/default-avatar.svg',\n      hasStory: true,\n      viewed: false,\n      touching: false\n    },\n    {\n      id: 5,\n      username: 'uniqlo',\n      avatar: '/assets/images/default-avatar.svg',\n      hasStory: true,\n      viewed: false,\n      touching: false\n    },\n    {\n      id: 6,\n      username: 'gucci',\n      avatar: '/assets/images/default-avatar.svg',\n      hasStory: true,\n      viewed: true,\n      touching: false\n    },\n    {\n      id: 7,\n      username: 'prada',\n      avatar: '/assets/images/default-avatar.svg',\n      hasStory: true,\n      viewed: false,\n      touching: false\n    },\n    {\n      id: 8,\n      username: 'versace',\n      avatar: '/assets/images/default-avatar.svg',\n      hasStory: true,\n      viewed: false,\n      touching: false\n    }\n  ];\n\n  // Categories Data\n  categories = [\n    { name: 'Women', icon: 'woman' },\n    { name: 'Men', icon: 'man' },\n    { name: 'Kids', icon: 'happy' },\n    { name: 'Shoes', icon: 'footsteps' },\n    { name: 'Bags', icon: 'bag' },\n    { name: 'Accessories', icon: 'watch' },\n    { name: 'Beauty', icon: 'flower' },\n    { name: 'Sports', icon: 'fitness' }\n  ];\n\n  constructor() {}\n\n  ngOnInit() {\n    this.checkScreenSize();\n    console.log('Home component initialized:', {\n      isMobile: this.isMobile,\n      instagramStories: this.instagramStories.length\n    });\n    // Prevent body scroll when sidebar is open\n    document.addEventListener('touchmove', this.preventScroll, { passive: false });\n  }\n\n  ngOnDestroy() {\n    document.removeEventListener('touchmove', this.preventScroll);\n  }\n\n  @HostListener('window:resize', ['$event'])\n  onResize(event: any) {\n    this.checkScreenSize();\n    if (!this.isMobile && this.isSidebarOpen) {\n      this.closeSidebar();\n    }\n  }\n\n  private checkScreenSize() {\n    // More comprehensive mobile detection\n    const width = window.innerWidth;\n    const userAgent = navigator.userAgent;\n    const isMobileUserAgent = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(userAgent);\n\n    // Consider it mobile if width <= 768px OR if it's a mobile user agent\n    this.isMobile = width <= 768 || isMobileUserAgent;\n\n    console.log('Screen size check:', {\n      width: width,\n      height: window.innerHeight,\n      isMobile: this.isMobile,\n      isMobileUserAgent: isMobileUserAgent,\n      userAgent: userAgent\n    });\n  }\n\n  toggleSidebar() {\n    this.isSidebarOpen = !this.isSidebarOpen;\n    this.toggleBodyScroll();\n  }\n\n  closeSidebar() {\n    this.isSidebarOpen = false;\n    this.toggleBodyScroll();\n  }\n\n  private toggleBodyScroll() {\n    if (this.isSidebarOpen) {\n      document.body.style.overflow = 'hidden';\n    } else {\n      document.body.style.overflow = '';\n    }\n  }\n\n  private preventScroll = (e: TouchEvent) => {\n    if (this.isSidebarOpen || this.isTabMenuOpen || this.isSidebarContentOpen) {\n      e.preventDefault();\n    }\n  }\n\n  // Tab Menu Methods\n  toggleTabMenu() {\n    this.isTabMenuOpen = !this.isTabMenuOpen;\n    this.toggleBodyScroll();\n  }\n\n  closeTabMenu() {\n    this.isTabMenuOpen = false;\n    this.toggleBodyScroll();\n  }\n\n  openSidebarTab(tabType: string) {\n    this.currentSidebarTab = tabType;\n    this.isSidebarContentOpen = true;\n    this.isTabMenuOpen = false;\n\n    // Set title based on tab type\n    const titles: { [key: string]: string } = {\n      'trending': 'Trending Products',\n      'brands': 'Featured Brands',\n      'arrivals': 'New Arrivals',\n      'suggested': 'Suggested for You',\n      'influencers': 'Fashion Influencers',\n      'categories': 'Categories'\n    };\n\n    this.currentSidebarTitle = titles[tabType] || 'Discover';\n    this.toggleBodyScroll();\n  }\n\n  closeSidebarContent() {\n    this.isSidebarContentOpen = false;\n    this.currentSidebarTab = '';\n    this.toggleBodyScroll();\n  }\n\n  // TikTok-style interaction methods\n  toggleLike() {\n    this.isLiked = !this.isLiked;\n    // TODO: Implement like functionality with backend\n    console.log('Like toggled:', this.isLiked);\n  }\n\n  openComments() {\n    // TODO: Implement comments modal/page\n    console.log('Opening comments...');\n  }\n\n  shareContent() {\n    // TODO: Implement share functionality\n    console.log('Sharing content...');\n    if (navigator.share) {\n      navigator.share({\n        title: 'DFashion',\n        text: 'Check out this amazing fashion content!',\n        url: window.location.href\n      });\n    }\n  }\n\n  openMusic() {\n    // TODO: Implement music/audio functionality\n    console.log('Opening music...');\n  }\n\n  // Stories functionality\n  createStory() {\n    console.log('Create story clicked');\n    // TODO: Implement story creation\n  }\n\n  viewStory(story: any) {\n    console.log('View story:', story);\n    // TODO: Implement story viewer\n  }\n\n  trackByStoryId(index: number, story: any): any {\n    return story.id || index;\n  }\n\n  // Enhanced touch interactions for mobile app\n  onStoryTouchStart(event: TouchEvent, story: any) {\n    story.touching = true;\n    // Add haptic feedback if available\n    if ('vibrate' in navigator) {\n      navigator.vibrate(10);\n    }\n  }\n\n  onStoryTouchEnd(event: TouchEvent, story: any) {\n    story.touching = false;\n  }\n\n  // TikTok-style interaction methods\n  onLikeClick() {\n    this.isLiked = !this.isLiked;\n    console.log('Like clicked:', this.isLiked);\n    // TODO: Implement like functionality with backend\n  }\n\n  onCommentClick() {\n    console.log('Comment clicked');\n    // TODO: Implement comment functionality\n  }\n\n  onShareClick() {\n    console.log('Share clicked');\n    // TODO: Implement share functionality\n  }\n\n  onBookmarkClick() {\n    console.log('Bookmark clicked');\n    // TODO: Implement bookmark functionality\n  }\n\n  // Mobile quick actions navigation methods\n  navigateToTrending() {\n    console.log('Navigate to trending');\n    // TODO: Implement navigation to trending page\n  }\n\n  navigateToNewArrivals() {\n    console.log('Navigate to new arrivals');\n    // TODO: Implement navigation to new arrivals page\n  }\n\n  navigateToOffers() {\n    console.log('Navigate to offers');\n    // TODO: Implement navigation to offers page\n  }\n\n  navigateToCategories() {\n    console.log('Navigate to categories');\n    // TODO: Implement navigation to categories page\n  }\n\n  navigateToWishlist() {\n    console.log('Navigate to wishlist');\n    // TODO: Implement navigation to wishlist page\n  }\n\n  navigateToCart() {\n    console.log('Navigate to cart');\n    // TODO: Implement navigation to cart page\n  }\n}\n", "<div class=\"home-container\" [class.mobile-instagram]=\"isMobile\" [class.sidebar-open]=\"isSidebarOpen\" [class.mobile]=\"isMobile\">\n  <!-- Mobile Header with Instagram-like styling -->\n  <div class=\"mobile-header instagram-style\" *ngIf=\"isMobile\">\n    <div class=\"header-left\">\n      <h1 class=\"app-logo\">DFashion</h1>\n      <ion-icon name=\"chevron-down\" class=\"logo-dropdown\"></ion-icon>\n    </div>\n    <div class=\"header-right\">\n      <ion-icon name=\"heart-outline\" class=\"header-icon\"></ion-icon>\n      <div class=\"menu-icon-container\" (click)=\"toggleTabMenu()\">\n        <ion-icon name=\"grid-outline\" class=\"header-icon menu-icon\"></ion-icon>\n        <div class=\"notification-dot\" *ngIf=\"hasNotifications\">3</div>\n      </div>\n      <div class=\"menu-icon-container\" (click)=\"toggleSidebar()\">\n        <ion-icon name=\"menu-outline\" class=\"header-icon menu-icon\"></ion-icon>\n      </div>\n    </div>\n  </div>\n\n  <!-- Removed separate mobile stories section - using unified responsive stories component -->\n\n\n  <!-- Web Layout: Stories Section + Trending Now + Two Column Layout -->\n  <div class=\"web-layout\" *ngIf=\"!isMobile\">\n    <!-- Stories Section (Full Width) -->\n    <div class=\"stories-section-container\">\n      <app-view-add-stories></app-view-add-stories>\n    </div>\n\n    <!-- Trending Now Section (Full Width) -->\n    <div class=\"trending-now-section-container\">\n      <app-trending-now></app-trending-now>\n    </div>\n\n    <!-- Two Column Layout -->\n    <div class=\"two-column-layout\">\n      <!-- Post Section (Left Column) -->\n      <div class=\"post-section\">\n        <app-feed></app-feed>\n      </div>\n\n      <!-- Sidebar Section (Right Column) -->\n      <div class=\"sidebar-section\">\n        <app-sidebar class=\"desktop-sidebar\"></app-sidebar>\n      </div>\n    </div>\n  </div>\n\n  <!-- Mobile Layout: Original Layout -->\n  <div class=\"mobile-layout\" *ngIf=\"isMobile\">\n    <div class=\"content-grid\">\n      <!-- Main Feed -->\n      <div class=\"main-content\">\n        <!-- Responsive Stories Component (Works on all screen sizes) -->\n        <app-view-add-stories></app-view-add-stories>\n\n        <!-- Trending Now Section (Mobile) -->\n        <div class=\"mobile-trending-now-section\">\n          <app-trending-now></app-trending-now>\n        </div>\n\n        <!-- Instagram-style Feed with Posts and Reels -->\n        <app-feed></app-feed>\n      </div>\n\n      <!-- Desktop Sidebar -->\n      <app-sidebar class=\"desktop-sidebar\"></app-sidebar>\n    </div>\n  </div>\n\n  <!-- Mobile Tab Menu Overlay -->\n  <div class=\"tab-menu-overlay\"\n       [class.active]=\"isTabMenuOpen\"\n       (click)=\"closeTabMenu()\"\n       *ngIf=\"isMobile\">\n  </div>\n\n  <!-- Mobile Sidebar Overlay -->\n  <div class=\"sidebar-overlay\"\n       [class.active]=\"isSidebarOpen\"\n       (click)=\"closeSidebar()\"\n       *ngIf=\"isMobile\">\n  </div>\n\n  <!-- Instagram-Style Tab Menu -->\n  <div class=\"instagram-tab-menu\"\n       [class.active]=\"isTabMenuOpen\"\n       *ngIf=\"isMobile\">\n    <div class=\"tab-menu-header\">\n      <h3>Discover</h3>\n      <ion-icon name=\"close-outline\" class=\"close-icon\" (click)=\"closeTabMenu()\"></ion-icon>\n    </div>\n\n    <div class=\"tab-menu-grid\">\n      <!-- Trending Products Tab -->\n      <div class=\"tab-item\" (click)=\"openSidebarTab('trending')\">\n        <div class=\"tab-icon trending\">\n          <ion-icon name=\"trending-up\"></ion-icon>\n        </div>\n        <span class=\"tab-label\">Trending</span>\n        <div class=\"tab-tooltip\">Hot products right now</div>\n      </div>\n\n      <!-- Featured Brands Tab -->\n      <div class=\"tab-item\" (click)=\"openSidebarTab('brands')\">\n        <div class=\"tab-icon brands\">\n          <ion-icon name=\"diamond\"></ion-icon>\n        </div>\n        <span class=\"tab-label\">Brands</span>\n        <div class=\"tab-tooltip\">Top fashion brands</div>\n      </div>\n\n      <!-- New Arrivals Tab -->\n      <div class=\"tab-item\" (click)=\"openSidebarTab('arrivals')\">\n        <div class=\"tab-icon arrivals\">\n          <ion-icon name=\"sparkles\"></ion-icon>\n        </div>\n        <span class=\"tab-label\">New</span>\n        <div class=\"tab-tooltip\">Latest arrivals</div>\n      </div>\n\n      <!-- Suggested for You Tab -->\n      <div class=\"tab-item\" (click)=\"openSidebarTab('suggested')\">\n        <div class=\"tab-icon suggested\">\n          <ion-icon name=\"heart\"></ion-icon>\n        </div>\n        <span class=\"tab-label\">For You</span>\n        <div class=\"tab-tooltip\">Personalized picks</div>\n      </div>\n\n      <!-- Fashion Influencers Tab -->\n      <div class=\"tab-item\" (click)=\"openSidebarTab('influencers')\">\n        <div class=\"tab-icon influencers\">\n          <ion-icon name=\"people\"></ion-icon>\n        </div>\n        <span class=\"tab-label\">Influencers</span>\n        <div class=\"tab-tooltip\">Top fashion creators</div>\n      </div>\n\n      <!-- Categories Tab -->\n      <div class=\"tab-item\" (click)=\"openSidebarTab('categories')\">\n        <div class=\"tab-icon categories\">\n          <ion-icon name=\"grid\"></ion-icon>\n        </div>\n        <span class=\"tab-label\">Categories</span>\n        <div class=\"tab-tooltip\">Browse by category</div>\n      </div>\n    </div>\n  </div>\n\n  <!-- Mobile Sidebar -->\n  <div class=\"mobile-sidebar\"\n       [class.active]=\"isSidebarOpen\"\n       *ngIf=\"isMobile\">\n    <div class=\"sidebar-header\">\n      <div class=\"user-profile\">\n        <div class=\"profile-avatar\">\n          <img src=\"assets/images/default-avatar.svg\" alt=\"Profile\">\n        </div>\n        <div class=\"profile-info\">\n          <h3>Your Profile</h3>\n          <p>&#64;username</p>\n        </div>\n      </div>\n      <ion-icon name=\"close-outline\" class=\"close-icon\" (click)=\"closeSidebar()\"></ion-icon>\n    </div>\n\n    <div class=\"sidebar-content\">\n      <app-sidebar></app-sidebar>\n    </div>\n  </div>\n\n  <!-- Sidebar Content Modal -->\n  <div class=\"sidebar-content-modal\"\n       [class.active]=\"isSidebarContentOpen\"\n       *ngIf=\"isMobile\">\n    <div class=\"modal-header\">\n      <h3>{{currentSidebarTitle}}</h3>\n      <ion-icon name=\"close-outline\" class=\"close-icon\" (click)=\"closeSidebarContent()\"></ion-icon>\n    </div>\n\n    <div class=\"modal-content\">\n      <!-- Trending Products Section -->\n      <div *ngIf=\"currentSidebarTab === 'trending'\" class=\"sidebar-section\">\n        <app-trending-products></app-trending-products>\n      </div>\n\n      <!-- Featured Brands Section -->\n      <div *ngIf=\"currentSidebarTab === 'brands'\" class=\"sidebar-section\">\n        <app-featured-brands></app-featured-brands>\n      </div>\n\n      <!-- New Arrivals Section -->\n      <div *ngIf=\"currentSidebarTab === 'arrivals'\" class=\"sidebar-section\">\n        <app-new-arrivals></app-new-arrivals>\n      </div>\n\n      <!-- Suggested for you -->\n      <div *ngIf=\"currentSidebarTab === 'suggested'\" class=\"sidebar-section\">\n        <app-suggested-for-you></app-suggested-for-you>\n      </div>\n\n      <!-- Top Fashion Influencers -->\n      <div *ngIf=\"currentSidebarTab === 'influencers'\" class=\"sidebar-section\">\n        <app-top-fashion-influencers></app-top-fashion-influencers>\n      </div>\n\n      <!-- Categories (placeholder) -->\n      <div *ngIf=\"currentSidebarTab === 'categories'\" class=\"sidebar-section\">\n        <div class=\"categories-grid\">\n          <div class=\"category-item\" *ngFor=\"let category of categories\">\n            <div class=\"category-icon\">\n              <ion-icon [name]=\"category.icon\"></ion-icon>\n            </div>\n            <span>{{category.name}}</span>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n\n  <!-- Instagram Bottom Navigation (Mobile Only) -->\n  <div class=\"instagram-bottom-nav\" *ngIf=\"isMobile\">\n    <div class=\"nav-item active\">\n      <ion-icon name=\"home\"></ion-icon>\n    </div>\n    <div class=\"nav-item\">\n      <ion-icon name=\"search\"></ion-icon>\n    </div>\n    <div class=\"nav-item\">\n      <ion-icon name=\"add-circle-outline\"></ion-icon>\n    </div>\n    <div class=\"nav-item\">\n      <ion-icon name=\"play-circle-outline\"></ion-icon>\n    </div>\n    <div class=\"nav-item\">\n      <div class=\"profile-avatar-nav\">\n        <img src=\"assets/images/default-avatar.svg\" alt=\"Profile\">\n      </div>\n    </div>\n  </div>\n</div>\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,QAAQ,gBAAgB;AAE5C,SAASC,uBAAuB,QAAQ,8DAA8D;AACtG,SAASC,aAAa,QAAQ,sCAAsC;AACpE,SAASC,gBAAgB,QAAQ,4CAA4C;AAC7E,SAASC,oBAAoB,QAAQ,sDAAsD;AAC3F,SAASC,yBAAyB,QAAQ,gEAAgE;AAC1G,SAASC,uBAAuB,QAAQ,4DAA4D;AACpG,SAASC,oBAAoB,QAAQ,sDAAsD;AAC3F,SAASC,wBAAwB,QAAQ,gEAAgE;AACzG,SAASC,8BAA8B,QAAQ,4EAA4E;;;;;;ICDnHC,EAAA,CAAAC,cAAA,cAAuD;IAAAD,EAAA,CAAAE,MAAA,QAAC;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;;IAPhEH,EAFJ,CAAAC,cAAA,cAA4D,cACjC,aACF;IAAAD,EAAA,CAAAE,MAAA,eAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAClCH,EAAA,CAAAI,SAAA,mBAA+D;IACjEJ,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,cAA0B;IACxBD,EAAA,CAAAI,SAAA,mBAA8D;IAC9DJ,EAAA,CAAAC,cAAA,cAA2D;IAA1BD,EAAA,CAAAK,UAAA,mBAAAC,kDAAA;MAAAN,EAAA,CAAAO,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAG,aAAA,EAAe;IAAA,EAAC;IACxDZ,EAAA,CAAAI,SAAA,mBAAuE;IACvEJ,EAAA,CAAAa,UAAA,IAAAC,kCAAA,kBAAuD;IACzDd,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,eAA2D;IAA1BD,EAAA,CAAAK,UAAA,mBAAAU,mDAAA;MAAAf,EAAA,CAAAO,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAO,aAAA,EAAe;IAAA,EAAC;IACxDhB,EAAA,CAAAI,SAAA,oBAAuE;IAG7EJ,EAFI,CAAAG,YAAA,EAAM,EACF,EACF;;;;IAN+BH,EAAA,CAAAiB,SAAA,GAAsB;IAAtBjB,EAAA,CAAAkB,UAAA,SAAAT,MAAA,CAAAU,gBAAA,CAAsB;;;;;IAczDnB,EAFF,CAAAC,cAAA,cAA0C,cAED;IACrCD,EAAA,CAAAI,SAAA,2BAA6C;IAC/CJ,EAAA,CAAAG,YAAA,EAAM;IAGNH,EAAA,CAAAC,cAAA,cAA4C;IAC1CD,EAAA,CAAAI,SAAA,uBAAqC;IACvCJ,EAAA,CAAAG,YAAA,EAAM;IAKJH,EAFF,CAAAC,cAAA,cAA+B,cAEH;IACxBD,EAAA,CAAAI,SAAA,eAAqB;IACvBJ,EAAA,CAAAG,YAAA,EAAM;IAGNH,EAAA,CAAAC,cAAA,cAA6B;IAC3BD,EAAA,CAAAI,SAAA,sBAAmD;IAGzDJ,EAFI,CAAAG,YAAA,EAAM,EACF,EACF;;;;;IAMFH,EAHJ,CAAAC,cAAA,cAA4C,cAChB,cAEE;IAExBD,EAAA,CAAAI,SAAA,2BAA6C;IAG7CJ,EAAA,CAAAC,cAAA,cAAyC;IACvCD,EAAA,CAAAI,SAAA,uBAAqC;IACvCJ,EAAA,CAAAG,YAAA,EAAM;IAGNH,EAAA,CAAAI,SAAA,eAAqB;IACvBJ,EAAA,CAAAG,YAAA,EAAM;IAGNH,EAAA,CAAAI,SAAA,sBAAmD;IAEvDJ,EADE,CAAAG,YAAA,EAAM,EACF;;;;;;IAGNH,EAAA,CAAAC,cAAA,cAGsB;IADjBD,EAAA,CAAAK,UAAA,mBAAAe,kDAAA;MAAApB,EAAA,CAAAO,aAAA,CAAAc,GAAA;MAAA,MAAAZ,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAa,YAAA,EAAc;IAAA,EAAC;IAE7BtB,EAAA,CAAAG,YAAA,EAAM;;;;IAHDH,EAAA,CAAAuB,WAAA,WAAAd,MAAA,CAAAe,aAAA,CAA8B;;;;;;IAMnCxB,EAAA,CAAAC,cAAA,cAGsB;IADjBD,EAAA,CAAAK,UAAA,mBAAAoB,kDAAA;MAAAzB,EAAA,CAAAO,aAAA,CAAAmB,GAAA;MAAA,MAAAjB,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAkB,YAAA,EAAc;IAAA,EAAC;IAE7B3B,EAAA,CAAAG,YAAA,EAAM;;;;IAHDH,EAAA,CAAAuB,WAAA,WAAAd,MAAA,CAAAmB,aAAA,CAA8B;;;;;;IAU/B5B,EAJJ,CAAAC,cAAA,cAEsB,cACS,SACvB;IAAAD,EAAA,CAAAE,MAAA,eAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACjBH,EAAA,CAAAC,cAAA,mBAA2E;IAAzBD,EAAA,CAAAK,UAAA,mBAAAwB,uDAAA;MAAA7B,EAAA,CAAAO,aAAA,CAAAuB,GAAA;MAAA,MAAArB,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAa,YAAA,EAAc;IAAA,EAAC;IAC5EtB,EAD6E,CAAAG,YAAA,EAAW,EAClF;IAIJH,EAFF,CAAAC,cAAA,cAA2B,cAEkC;IAArCD,EAAA,CAAAK,UAAA,mBAAA0B,kDAAA;MAAA/B,EAAA,CAAAO,aAAA,CAAAuB,GAAA;MAAA,MAAArB,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAuB,cAAA,CAAe,UAAU,CAAC;IAAA,EAAC;IACxDhC,EAAA,CAAAC,cAAA,cAA+B;IAC7BD,EAAA,CAAAI,SAAA,mBAAwC;IAC1CJ,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,eAAwB;IAAAD,EAAA,CAAAE,MAAA,gBAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACvCH,EAAA,CAAAC,cAAA,eAAyB;IAAAD,EAAA,CAAAE,MAAA,8BAAsB;IACjDF,EADiD,CAAAG,YAAA,EAAM,EACjD;IAGNH,EAAA,CAAAC,cAAA,eAAyD;IAAnCD,EAAA,CAAAK,UAAA,mBAAA4B,mDAAA;MAAAjC,EAAA,CAAAO,aAAA,CAAAuB,GAAA;MAAA,MAAArB,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAuB,cAAA,CAAe,QAAQ,CAAC;IAAA,EAAC;IACtDhC,EAAA,CAAAC,cAAA,eAA6B;IAC3BD,EAAA,CAAAI,SAAA,oBAAoC;IACtCJ,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,gBAAwB;IAAAD,EAAA,CAAAE,MAAA,cAAM;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACrCH,EAAA,CAAAC,cAAA,eAAyB;IAAAD,EAAA,CAAAE,MAAA,0BAAkB;IAC7CF,EAD6C,CAAAG,YAAA,EAAM,EAC7C;IAGNH,EAAA,CAAAC,cAAA,eAA2D;IAArCD,EAAA,CAAAK,UAAA,mBAAA6B,mDAAA;MAAAlC,EAAA,CAAAO,aAAA,CAAAuB,GAAA;MAAA,MAAArB,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAuB,cAAA,CAAe,UAAU,CAAC;IAAA,EAAC;IACxDhC,EAAA,CAAAC,cAAA,eAA+B;IAC7BD,EAAA,CAAAI,SAAA,oBAAqC;IACvCJ,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,gBAAwB;IAAAD,EAAA,CAAAE,MAAA,WAAG;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAClCH,EAAA,CAAAC,cAAA,eAAyB;IAAAD,EAAA,CAAAE,MAAA,uBAAe;IAC1CF,EAD0C,CAAAG,YAAA,EAAM,EAC1C;IAGNH,EAAA,CAAAC,cAAA,eAA4D;IAAtCD,EAAA,CAAAK,UAAA,mBAAA8B,mDAAA;MAAAnC,EAAA,CAAAO,aAAA,CAAAuB,GAAA;MAAA,MAAArB,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAuB,cAAA,CAAe,WAAW,CAAC;IAAA,EAAC;IACzDhC,EAAA,CAAAC,cAAA,eAAgC;IAC9BD,EAAA,CAAAI,SAAA,oBAAkC;IACpCJ,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,gBAAwB;IAAAD,EAAA,CAAAE,MAAA,eAAO;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACtCH,EAAA,CAAAC,cAAA,eAAyB;IAAAD,EAAA,CAAAE,MAAA,0BAAkB;IAC7CF,EAD6C,CAAAG,YAAA,EAAM,EAC7C;IAGNH,EAAA,CAAAC,cAAA,eAA8D;IAAxCD,EAAA,CAAAK,UAAA,mBAAA+B,mDAAA;MAAApC,EAAA,CAAAO,aAAA,CAAAuB,GAAA;MAAA,MAAArB,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAuB,cAAA,CAAe,aAAa,CAAC;IAAA,EAAC;IAC3DhC,EAAA,CAAAC,cAAA,eAAkC;IAChCD,EAAA,CAAAI,SAAA,oBAAmC;IACrCJ,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,gBAAwB;IAAAD,EAAA,CAAAE,MAAA,mBAAW;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAC1CH,EAAA,CAAAC,cAAA,eAAyB;IAAAD,EAAA,CAAAE,MAAA,4BAAoB;IAC/CF,EAD+C,CAAAG,YAAA,EAAM,EAC/C;IAGNH,EAAA,CAAAC,cAAA,eAA6D;IAAvCD,EAAA,CAAAK,UAAA,mBAAAgC,mDAAA;MAAArC,EAAA,CAAAO,aAAA,CAAAuB,GAAA;MAAA,MAAArB,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAuB,cAAA,CAAe,YAAY,CAAC;IAAA,EAAC;IAC1DhC,EAAA,CAAAC,cAAA,eAAiC;IAC/BD,EAAA,CAAAI,SAAA,oBAAiC;IACnCJ,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,gBAAwB;IAAAD,EAAA,CAAAE,MAAA,kBAAU;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACzCH,EAAA,CAAAC,cAAA,eAAyB;IAAAD,EAAA,CAAAE,MAAA,0BAAkB;IAGjDF,EAHiD,CAAAG,YAAA,EAAM,EAC7C,EACF,EACF;;;;IA9DDH,EAAA,CAAAuB,WAAA,WAAAd,MAAA,CAAAe,aAAA,CAA8B;;;;;;IAsE7BxB,EALN,CAAAC,cAAA,cAEsB,cACQ,cACA,cACI;IAC1BD,EAAA,CAAAI,SAAA,cAA0D;IAC5DJ,EAAA,CAAAG,YAAA,EAAM;IAEJH,EADF,CAAAC,cAAA,cAA0B,SACpB;IAAAD,EAAA,CAAAE,MAAA,mBAAY;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACrBH,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,gBAAa;IAEpBF,EAFoB,CAAAG,YAAA,EAAI,EAChB,EACF;IACNH,EAAA,CAAAC,cAAA,oBAA2E;IAAzBD,EAAA,CAAAK,UAAA,mBAAAiC,wDAAA;MAAAtC,EAAA,CAAAO,aAAA,CAAAgC,GAAA;MAAA,MAAA9B,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAkB,YAAA,EAAc;IAAA,EAAC;IAC5E3B,EAD6E,CAAAG,YAAA,EAAW,EAClF;IAENH,EAAA,CAAAC,cAAA,eAA6B;IAC3BD,EAAA,CAAAI,SAAA,mBAA2B;IAE/BJ,EADE,CAAAG,YAAA,EAAM,EACF;;;;IAlBDH,EAAA,CAAAuB,WAAA,WAAAd,MAAA,CAAAmB,aAAA,CAA8B;;;;;IA+B/B5B,EAAA,CAAAC,cAAA,cAAsE;IACpED,EAAA,CAAAI,SAAA,4BAA+C;IACjDJ,EAAA,CAAAG,YAAA,EAAM;;;;;IAGNH,EAAA,CAAAC,cAAA,cAAoE;IAClED,EAAA,CAAAI,SAAA,0BAA2C;IAC7CJ,EAAA,CAAAG,YAAA,EAAM;;;;;IAGNH,EAAA,CAAAC,cAAA,cAAsE;IACpED,EAAA,CAAAI,SAAA,uBAAqC;IACvCJ,EAAA,CAAAG,YAAA,EAAM;;;;;IAGNH,EAAA,CAAAC,cAAA,cAAuE;IACrED,EAAA,CAAAI,SAAA,4BAA+C;IACjDJ,EAAA,CAAAG,YAAA,EAAM;;;;;IAGNH,EAAA,CAAAC,cAAA,cAAyE;IACvED,EAAA,CAAAI,SAAA,kCAA2D;IAC7DJ,EAAA,CAAAG,YAAA,EAAM;;;;;IAMAH,EADF,CAAAC,cAAA,cAA+D,cAClC;IACzBD,EAAA,CAAAI,SAAA,mBAA4C;IAC9CJ,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAE,MAAA,GAAiB;IACzBF,EADyB,CAAAG,YAAA,EAAO,EAC1B;;;;IAHQH,EAAA,CAAAiB,SAAA,GAAsB;IAAtBjB,EAAA,CAAAkB,UAAA,SAAAsB,WAAA,CAAAC,IAAA,CAAsB;IAE5BzC,EAAA,CAAAiB,SAAA,GAAiB;IAAjBjB,EAAA,CAAA0C,iBAAA,CAAAF,WAAA,CAAAG,IAAA,CAAiB;;;;;IAL3B3C,EADF,CAAAC,cAAA,cAAwE,cACzC;IAC3BD,EAAA,CAAAa,UAAA,IAAA+B,yCAAA,kBAA+D;IAOnE5C,EADE,CAAAG,YAAA,EAAM,EACF;;;;IAP8CH,EAAA,CAAAiB,SAAA,GAAa;IAAbjB,EAAA,CAAAkB,UAAA,YAAAT,MAAA,CAAAoC,UAAA,CAAa;;;;;;IAjCjE7C,EAJJ,CAAAC,cAAA,cAEsB,cACM,SACpB;IAAAD,EAAA,CAAAE,MAAA,GAAuB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAChCH,EAAA,CAAAC,cAAA,mBAAkF;IAAhCD,EAAA,CAAAK,UAAA,mBAAAyC,uDAAA;MAAA9C,EAAA,CAAAO,aAAA,CAAAwC,GAAA;MAAA,MAAAtC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAuC,mBAAA,EAAqB;IAAA,EAAC;IACnFhD,EADoF,CAAAG,YAAA,EAAW,EACzF;IAENH,EAAA,CAAAC,cAAA,cAA2B;IA2BzBD,EAzBA,CAAAa,UAAA,IAAAoC,kCAAA,kBAAsE,IAAAC,kCAAA,kBAKF,IAAAC,kCAAA,kBAKE,IAAAC,kCAAA,kBAKC,KAAAC,mCAAA,kBAKE,KAAAC,mCAAA,kBAKD;IAW5EtD,EADE,CAAAG,YAAA,EAAM,EACF;;;;IA7CDH,EAAA,CAAAuB,WAAA,WAAAd,MAAA,CAAA8C,oBAAA,CAAqC;IAGlCvD,EAAA,CAAAiB,SAAA,GAAuB;IAAvBjB,EAAA,CAAA0C,iBAAA,CAAAjC,MAAA,CAAA+C,mBAAA,CAAuB;IAMrBxD,EAAA,CAAAiB,SAAA,GAAsC;IAAtCjB,EAAA,CAAAkB,UAAA,SAAAT,MAAA,CAAAgD,iBAAA,gBAAsC;IAKtCzD,EAAA,CAAAiB,SAAA,EAAoC;IAApCjB,EAAA,CAAAkB,UAAA,SAAAT,MAAA,CAAAgD,iBAAA,cAAoC;IAKpCzD,EAAA,CAAAiB,SAAA,EAAsC;IAAtCjB,EAAA,CAAAkB,UAAA,SAAAT,MAAA,CAAAgD,iBAAA,gBAAsC;IAKtCzD,EAAA,CAAAiB,SAAA,EAAuC;IAAvCjB,EAAA,CAAAkB,UAAA,SAAAT,MAAA,CAAAgD,iBAAA,iBAAuC;IAKvCzD,EAAA,CAAAiB,SAAA,EAAyC;IAAzCjB,EAAA,CAAAkB,UAAA,SAAAT,MAAA,CAAAgD,iBAAA,mBAAyC;IAKzCzD,EAAA,CAAAiB,SAAA,EAAwC;IAAxCjB,EAAA,CAAAkB,UAAA,SAAAT,MAAA,CAAAgD,iBAAA,kBAAwC;;;;;IAehDzD,EADF,CAAAC,cAAA,cAAmD,cACpB;IAC3BD,EAAA,CAAAI,SAAA,mBAAiC;IACnCJ,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,cAAsB;IACpBD,EAAA,CAAAI,SAAA,mBAAmC;IACrCJ,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,cAAsB;IACpBD,EAAA,CAAAI,SAAA,mBAA+C;IACjDJ,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,cAAsB;IACpBD,EAAA,CAAAI,SAAA,mBAAgD;IAClDJ,EAAA,CAAAG,YAAA,EAAM;IAEJH,EADF,CAAAC,cAAA,cAAsB,eACY;IAC9BD,EAAA,CAAAI,SAAA,eAA0D;IAGhEJ,EAFI,CAAAG,YAAA,EAAM,EACF,EACF;;;AD7MR,OAAM,MAAOuD,aAAa;EA6FxBC,YAAA;IA5FA,KAAAC,QAAQ,GAAG,KAAK;IAChB,KAAAhC,aAAa,GAAG,KAAK;IACrB,KAAAJ,aAAa,GAAG,KAAK;IACrB,KAAA+B,oBAAoB,GAAG,KAAK;IAC5B,KAAAE,iBAAiB,GAAG,EAAE;IACtB,KAAAD,mBAAmB,GAAG,EAAE;IACxB,KAAArC,gBAAgB,GAAG,IAAI,CAAC,CAAC;IACzB,KAAA0C,MAAM,GAAGA,MAAM,CAAC,CAAC;IAEjB;IACA,KAAAC,OAAO,GAAG,KAAK;IAEf;IACA,KAAAC,gBAAgB,GAAG,CACjB;MACEC,EAAE,EAAE,CAAC;MACLC,QAAQ,EAAE,MAAM;MAChBC,MAAM,EAAE,mCAAmC;MAC3CC,QAAQ,EAAE,IAAI;MACdC,MAAM,EAAE,KAAK;MACbC,QAAQ,EAAE;KACX,EACD;MACEL,EAAE,EAAE,CAAC;MACLC,QAAQ,EAAE,MAAM;MAChBC,MAAM,EAAE,mCAAmC;MAC3CC,QAAQ,EAAE,IAAI;MACdC,MAAM,EAAE,KAAK;MACbC,QAAQ,EAAE;KACX,EACD;MACEL,EAAE,EAAE,CAAC;MACLC,QAAQ,EAAE,QAAQ;MAClBC,MAAM,EAAE,mCAAmC;MAC3CC,QAAQ,EAAE,IAAI;MACdC,MAAM,EAAE,IAAI;MACZC,QAAQ,EAAE;KACX,EACD;MACEL,EAAE,EAAE,CAAC;MACLC,QAAQ,EAAE,KAAK;MACfC,MAAM,EAAE,mCAAmC;MAC3CC,QAAQ,EAAE,IAAI;MACdC,MAAM,EAAE,KAAK;MACbC,QAAQ,EAAE;KACX,EACD;MACEL,EAAE,EAAE,CAAC;MACLC,QAAQ,EAAE,QAAQ;MAClBC,MAAM,EAAE,mCAAmC;MAC3CC,QAAQ,EAAE,IAAI;MACdC,MAAM,EAAE,KAAK;MACbC,QAAQ,EAAE;KACX,EACD;MACEL,EAAE,EAAE,CAAC;MACLC,QAAQ,EAAE,OAAO;MACjBC,MAAM,EAAE,mCAAmC;MAC3CC,QAAQ,EAAE,IAAI;MACdC,MAAM,EAAE,IAAI;MACZC,QAAQ,EAAE;KACX,EACD;MACEL,EAAE,EAAE,CAAC;MACLC,QAAQ,EAAE,OAAO;MACjBC,MAAM,EAAE,mCAAmC;MAC3CC,QAAQ,EAAE,IAAI;MACdC,MAAM,EAAE,KAAK;MACbC,QAAQ,EAAE;KACX,EACD;MACEL,EAAE,EAAE,CAAC;MACLC,QAAQ,EAAE,SAAS;MACnBC,MAAM,EAAE,mCAAmC;MAC3CC,QAAQ,EAAE,IAAI;MACdC,MAAM,EAAE,KAAK;MACbC,QAAQ,EAAE;KACX,CACF;IAED;IACA,KAAAxB,UAAU,GAAG,CACX;MAAEF,IAAI,EAAE,OAAO;MAAEF,IAAI,EAAE;IAAO,CAAE,EAChC;MAAEE,IAAI,EAAE,KAAK;MAAEF,IAAI,EAAE;IAAK,CAAE,EAC5B;MAAEE,IAAI,EAAE,MAAM;MAAEF,IAAI,EAAE;IAAO,CAAE,EAC/B;MAAEE,IAAI,EAAE,OAAO;MAAEF,IAAI,EAAE;IAAW,CAAE,EACpC;MAAEE,IAAI,EAAE,MAAM;MAAEF,IAAI,EAAE;IAAK,CAAE,EAC7B;MAAEE,IAAI,EAAE,aAAa;MAAEF,IAAI,EAAE;IAAO,CAAE,EACtC;MAAEE,IAAI,EAAE,QAAQ;MAAEF,IAAI,EAAE;IAAQ,CAAE,EAClC;MAAEE,IAAI,EAAE,QAAQ;MAAEF,IAAI,EAAE;IAAS,CAAE,CACpC;IA8DO,KAAA6B,aAAa,GAAIC,CAAa,IAAI;MACxC,IAAI,IAAI,CAAC3C,aAAa,IAAI,IAAI,CAACJ,aAAa,IAAI,IAAI,CAAC+B,oBAAoB,EAAE;QACzEgB,CAAC,CAACC,cAAc,EAAE;;IAEtB,CAAC;EAhEc;EAEfC,QAAQA,CAAA;IACN,IAAI,CAACC,eAAe,EAAE;IACtBC,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAE;MACzChB,QAAQ,EAAE,IAAI,CAACA,QAAQ;MACvBG,gBAAgB,EAAE,IAAI,CAACA,gBAAgB,CAACc;KACzC,CAAC;IACF;IACAC,QAAQ,CAACC,gBAAgB,CAAC,WAAW,EAAE,IAAI,CAACT,aAAa,EAAE;MAAEU,OAAO,EAAE;IAAK,CAAE,CAAC;EAChF;EAEAC,WAAWA,CAAA;IACTH,QAAQ,CAACI,mBAAmB,CAAC,WAAW,EAAE,IAAI,CAACZ,aAAa,CAAC;EAC/D;EAGAa,QAAQA,CAACC,KAAU;IACjB,IAAI,CAACV,eAAe,EAAE;IACtB,IAAI,CAAC,IAAI,CAACd,QAAQ,IAAI,IAAI,CAAChC,aAAa,EAAE;MACxC,IAAI,CAACD,YAAY,EAAE;;EAEvB;EAEQ+C,eAAeA,CAAA;IACrB;IACA,MAAMW,KAAK,GAAGxB,MAAM,CAACyB,UAAU;IAC/B,MAAMC,SAAS,GAAGC,SAAS,CAACD,SAAS;IACrC,MAAME,iBAAiB,GAAG,gEAAgE,CAACC,IAAI,CAACH,SAAS,CAAC;IAE1G;IACA,IAAI,CAAC3B,QAAQ,GAAGyB,KAAK,IAAI,GAAG,IAAII,iBAAiB;IAEjDd,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAE;MAChCS,KAAK,EAAEA,KAAK;MACZM,MAAM,EAAE9B,MAAM,CAAC+B,WAAW;MAC1BhC,QAAQ,EAAE,IAAI,CAACA,QAAQ;MACvB6B,iBAAiB,EAAEA,iBAAiB;MACpCF,SAAS,EAAEA;KACZ,CAAC;EACJ;EAEAvE,aAAaA,CAAA;IACX,IAAI,CAACY,aAAa,GAAG,CAAC,IAAI,CAACA,aAAa;IACxC,IAAI,CAACiE,gBAAgB,EAAE;EACzB;EAEAlE,YAAYA,CAAA;IACV,IAAI,CAACC,aAAa,GAAG,KAAK;IAC1B,IAAI,CAACiE,gBAAgB,EAAE;EACzB;EAEQA,gBAAgBA,CAAA;IACtB,IAAI,IAAI,CAACjE,aAAa,EAAE;MACtBkD,QAAQ,CAACgB,IAAI,CAACC,KAAK,CAACC,QAAQ,GAAG,QAAQ;KACxC,MAAM;MACLlB,QAAQ,CAACgB,IAAI,CAACC,KAAK,CAACC,QAAQ,GAAG,EAAE;;EAErC;EAQA;EACApF,aAAaA,CAAA;IACX,IAAI,CAACY,aAAa,GAAG,CAAC,IAAI,CAACA,aAAa;IACxC,IAAI,CAACqE,gBAAgB,EAAE;EACzB;EAEAvE,YAAYA,CAAA;IACV,IAAI,CAACE,aAAa,GAAG,KAAK;IAC1B,IAAI,CAACqE,gBAAgB,EAAE;EACzB;EAEA7D,cAAcA,CAACiE,OAAe;IAC5B,IAAI,CAACxC,iBAAiB,GAAGwC,OAAO;IAChC,IAAI,CAAC1C,oBAAoB,GAAG,IAAI;IAChC,IAAI,CAAC/B,aAAa,GAAG,KAAK;IAE1B;IACA,MAAM0E,MAAM,GAA8B;MACxC,UAAU,EAAE,mBAAmB;MAC/B,QAAQ,EAAE,iBAAiB;MAC3B,UAAU,EAAE,cAAc;MAC1B,WAAW,EAAE,mBAAmB;MAChC,aAAa,EAAE,qBAAqB;MACpC,YAAY,EAAE;KACf;IAED,IAAI,CAAC1C,mBAAmB,GAAG0C,MAAM,CAACD,OAAO,CAAC,IAAI,UAAU;IACxD,IAAI,CAACJ,gBAAgB,EAAE;EACzB;EAEA7C,mBAAmBA,CAAA;IACjB,IAAI,CAACO,oBAAoB,GAAG,KAAK;IACjC,IAAI,CAACE,iBAAiB,GAAG,EAAE;IAC3B,IAAI,CAACoC,gBAAgB,EAAE;EACzB;EAEA;EACAM,UAAUA,CAAA;IACR,IAAI,CAACrC,OAAO,GAAG,CAAC,IAAI,CAACA,OAAO;IAC5B;IACAa,OAAO,CAACC,GAAG,CAAC,eAAe,EAAE,IAAI,CAACd,OAAO,CAAC;EAC5C;EAEAsC,YAAYA,CAAA;IACV;IACAzB,OAAO,CAACC,GAAG,CAAC,qBAAqB,CAAC;EACpC;EAEAyB,YAAYA,CAAA;IACV;IACA1B,OAAO,CAACC,GAAG,CAAC,oBAAoB,CAAC;IACjC,IAAIY,SAAS,CAACc,KAAK,EAAE;MACnBd,SAAS,CAACc,KAAK,CAAC;QACdC,KAAK,EAAE,UAAU;QACjBC,IAAI,EAAE,yCAAyC;QAC/CC,GAAG,EAAE5C,MAAM,CAAC6C,QAAQ,CAACC;OACtB,CAAC;;EAEN;EAEAC,SAASA,CAAA;IACP;IACAjC,OAAO,CAACC,GAAG,CAAC,kBAAkB,CAAC;EACjC;EAEA;EACAiC,WAAWA,CAAA;IACTlC,OAAO,CAACC,GAAG,CAAC,sBAAsB,CAAC;IACnC;EACF;EAEAkC,SAASA,CAACC,KAAU;IAClBpC,OAAO,CAACC,GAAG,CAAC,aAAa,EAAEmC,KAAK,CAAC;IACjC;EACF;EAEAC,cAAcA,CAACC,KAAa,EAAEF,KAAU;IACtC,OAAOA,KAAK,CAAC/C,EAAE,IAAIiD,KAAK;EAC1B;EAEA;EACAC,iBAAiBA,CAAC9B,KAAiB,EAAE2B,KAAU;IAC7CA,KAAK,CAAC1C,QAAQ,GAAG,IAAI;IACrB;IACA,IAAI,SAAS,IAAImB,SAAS,EAAE;MAC1BA,SAAS,CAAC2B,OAAO,CAAC,EAAE,CAAC;;EAEzB;EAEAC,eAAeA,CAAChC,KAAiB,EAAE2B,KAAU;IAC3CA,KAAK,CAAC1C,QAAQ,GAAG,KAAK;EACxB;EAEA;EACAgD,WAAWA,CAAA;IACT,IAAI,CAACvD,OAAO,GAAG,CAAC,IAAI,CAACA,OAAO;IAC5Ba,OAAO,CAACC,GAAG,CAAC,eAAe,EAAE,IAAI,CAACd,OAAO,CAAC;IAC1C;EACF;EAEAwD,cAAcA,CAAA;IACZ3C,OAAO,CAACC,GAAG,CAAC,iBAAiB,CAAC;IAC9B;EACF;EAEA2C,YAAYA,CAAA;IACV5C,OAAO,CAACC,GAAG,CAAC,eAAe,CAAC;IAC5B;EACF;EAEA4C,eAAeA,CAAA;IACb7C,OAAO,CAACC,GAAG,CAAC,kBAAkB,CAAC;IAC/B;EACF;EAEA;EACA6C,kBAAkBA,CAAA;IAChB9C,OAAO,CAACC,GAAG,CAAC,sBAAsB,CAAC;IACnC;EACF;EAEA8C,qBAAqBA,CAAA;IACnB/C,OAAO,CAACC,GAAG,CAAC,0BAA0B,CAAC;IACvC;EACF;EAEA+C,gBAAgBA,CAAA;IACdhD,OAAO,CAACC,GAAG,CAAC,oBAAoB,CAAC;IACjC;EACF;EAEAgD,oBAAoBA,CAAA;IAClBjD,OAAO,CAACC,GAAG,CAAC,wBAAwB,CAAC;IACrC;EACF;EAEAiD,kBAAkBA,CAAA;IAChBlD,OAAO,CAACC,GAAG,CAAC,sBAAsB,CAAC;IACnC;EACF;EAEAkD,cAAcA,CAAA;IACZnD,OAAO,CAACC,GAAG,CAAC,kBAAkB,CAAC;IAC/B;EACF;;;uBA/SWlB,aAAa;IAAA;EAAA;;;YAAbA,aAAa;MAAAqE,SAAA;MAAAC,YAAA,WAAAC,2BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAblI,EAAA,CAAAK,UAAA,oBAAA+H,wCAAAC,MAAA;YAAA,OAAAF,GAAA,CAAAhD,QAAA,CAAAkD,MAAA,CAAgB;UAAA,UAAArI,EAAA,CAAAsI,eAAA,CAAH;;;;;;;;;;UCnC1BtI,EAAA,CAAAC,cAAA,aAA+H;UA8N7HD,EA5NA,CAAAa,UAAA,IAAA0H,4BAAA,kBAA4D,IAAAC,4BAAA,kBAqBlB,IAAAC,4BAAA,iBA0BE,IAAAC,4BAAA,iBAyBtB,IAAAC,4BAAA,iBAOA,IAAAC,4BAAA,kBAMA,IAAAC,4BAAA,kBAkEA,IAAAC,4BAAA,kBAsBA,IAAAC,4BAAA,kBA+C6B;UAmBrD/I,EAAA,CAAAG,YAAA,EAAM;;;UAjP+FH,EAAzE,CAAAuB,WAAA,qBAAA4G,GAAA,CAAAvE,QAAA,CAAmC,iBAAAuE,GAAA,CAAAvG,aAAA,CAAqC,WAAAuG,GAAA,CAAAvE,QAAA,CAA0B;UAEhF5D,EAAA,CAAAiB,SAAA,EAAc;UAAdjB,EAAA,CAAAkB,UAAA,SAAAiH,GAAA,CAAAvE,QAAA,CAAc;UAqBjC5D,EAAA,CAAAiB,SAAA,EAAe;UAAfjB,EAAA,CAAAkB,UAAA,UAAAiH,GAAA,CAAAvE,QAAA,CAAe;UA0BZ5D,EAAA,CAAAiB,SAAA,EAAc;UAAdjB,EAAA,CAAAkB,UAAA,SAAAiH,GAAA,CAAAvE,QAAA,CAAc;UAyBpC5D,EAAA,CAAAiB,SAAA,EAAc;UAAdjB,EAAA,CAAAkB,UAAA,SAAAiH,GAAA,CAAAvE,QAAA,CAAc;UAOd5D,EAAA,CAAAiB,SAAA,EAAc;UAAdjB,EAAA,CAAAkB,UAAA,SAAAiH,GAAA,CAAAvE,QAAA,CAAc;UAMd5D,EAAA,CAAAiB,SAAA,EAAc;UAAdjB,EAAA,CAAAkB,UAAA,SAAAiH,GAAA,CAAAvE,QAAA,CAAc;UAkEd5D,EAAA,CAAAiB,SAAA,EAAc;UAAdjB,EAAA,CAAAkB,UAAA,SAAAiH,GAAA,CAAAvE,QAAA,CAAc;UAsBd5D,EAAA,CAAAiB,SAAA,EAAc;UAAdjB,EAAA,CAAAkB,UAAA,SAAAiH,GAAA,CAAAvE,QAAA,CAAc;UA+Ce5D,EAAA,CAAAiB,SAAA,EAAc;UAAdjB,EAAA,CAAAkB,UAAA,SAAAiH,GAAA,CAAAvE,QAAA,CAAc;;;qBD3M/CvE,YAAY,EAAA2J,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EACZ5J,WAAW,EAAA6J,EAAA,CAAAC,OAAA,EACX7J,uBAAuB,EACvBC,aAAa,EACbC,gBAAgB,EAChBC,oBAAoB,EACpBC,yBAAyB,EACzBC,uBAAuB,EACvBC,oBAAoB,EACpBC,wBAAwB,EACxBC,8BAA8B;MAAAsJ,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}