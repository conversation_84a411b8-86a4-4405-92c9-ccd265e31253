{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { Component } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\nimport { IonicModule } from '@ionic/angular';\n// Import individual trending components\nimport { TrendingBannersComponent } from './trending-banners/trending-banners.component';\nimport { TrendingProductSlidersComponent } from './trending-product-sliders/trending-product-sliders.component';\nimport { TrendingInfluencerHighlightsComponent } from './trending-influencer-highlights/trending-influencer-highlights.component';\nimport { TrendingCollectionsComponent } from './trending-collections/trending-collections.component';\nimport { TrendingDealsComponent } from './trending-deals/trending-deals.component';\nimport { TrendingHashtagsComponent } from './trending-hashtags/trending-hashtags.component';\nimport { TrendingReelsComponent } from './trending-reels/trending-reels.component';\nlet TrendingNowComponent = class TrendingNowComponent {\n  constructor(router) {\n    this.router = router;\n  }\n  ngOnInit() {\n    // Initialize trending now container\n  }\n  onViewAll() {\n    this.router.navigate(['/trending'], {\n      queryParams: {\n        section: 'all'\n      }\n    });\n  }\n};\nTrendingNowComponent = __decorate([Component({\n  selector: 'app-trending-now',\n  standalone: true,\n  imports: [CommonModule, RouterModule, IonicModule, TrendingBannersComponent, TrendingProductSlidersComponent, TrendingInfluencerHighlightsComponent, TrendingCollectionsComponent, TrendingDealsComponent, TrendingHashtagsComponent, TrendingReelsComponent],\n  templateUrl: './trending-now.component.html',\n  styleUrls: ['./trending-now.component.scss']\n})], TrendingNowComponent);\nexport { TrendingNowComponent };", "map": {"version": 3, "names": ["Component", "CommonModule", "RouterModule", "IonicModule", "TrendingBannersComponent", "TrendingProductSlidersComponent", "TrendingInfluencerHighlightsComponent", "TrendingCollectionsComponent", "TrendingDealsComponent", "TrendingHashtagsComponent", "TrendingReelsComponent", "TrendingNowComponent", "constructor", "router", "ngOnInit", "onViewAll", "navigate", "queryParams", "section", "__decorate", "selector", "standalone", "imports", "templateUrl", "styleUrls"], "sources": ["E:\\Fashion\\DFashionFrontend\\frontend\\src\\app\\features\\home\\components\\trending-now\\trending-now.component.ts"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\nimport { IonicModule } from '@ionic/angular';\nimport { Router } from '@angular/router';\n\n// Import individual trending components\nimport { TrendingBannersComponent } from './trending-banners/trending-banners.component';\nimport { TrendingProductSlidersComponent } from './trending-product-sliders/trending-product-sliders.component';\nimport { TrendingInfluencerHighlightsComponent } from './trending-influencer-highlights/trending-influencer-highlights.component';\nimport { TrendingCollectionsComponent } from './trending-collections/trending-collections.component';\nimport { TrendingDealsComponent } from './trending-deals/trending-deals.component';\nimport { TrendingHashtagsComponent } from './trending-hashtags/trending-hashtags.component';\nimport { TrendingReelsComponent } from './trending-reels/trending-reels.component';\n\n@Component({\n  selector: 'app-trending-now',\n  standalone: true,\n  imports: [\n    CommonModule,\n    RouterModule,\n    IonicModule,\n    TrendingBannersComponent,\n    TrendingProductSlidersComponent,\n    TrendingInfluencerHighlightsComponent,\n    TrendingCollectionsComponent,\n    TrendingDealsComponent,\n    TrendingHashtagsComponent,\n    TrendingReelsComponent\n  ],\n  templateUrl: './trending-now.component.html',\n  styleUrls: ['./trending-now.component.scss']\n})\nexport class TrendingNowComponent implements OnInit {\n\n  constructor(private router: Router) {}\n\n  ngOnInit() {\n    // Initialize trending now container\n  }\n\n  onViewAll() {\n    this.router.navigate(['/trending'], {\n      queryParams: { section: 'all' }\n    });\n  }\n}\n"], "mappings": ";AAAA,SAASA,SAAS,QAAgB,eAAe;AACjD,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,QAAQ,gBAAgB;AAG5C;AACA,SAASC,wBAAwB,QAAQ,+CAA+C;AACxF,SAASC,+BAA+B,QAAQ,+DAA+D;AAC/G,SAASC,qCAAqC,QAAQ,2EAA2E;AACjI,SAASC,4BAA4B,QAAQ,uDAAuD;AACpG,SAASC,sBAAsB,QAAQ,2CAA2C;AAClF,SAASC,yBAAyB,QAAQ,iDAAiD;AAC3F,SAASC,sBAAsB,QAAQ,2CAA2C;AAoB3E,IAAMC,oBAAoB,GAA1B,MAAMA,oBAAoB;EAE/BC,YAAoBC,MAAc;IAAd,KAAAA,MAAM,GAANA,MAAM;EAAW;EAErCC,QAAQA,CAAA;IACN;EAAA;EAGFC,SAASA,CAAA;IACP,IAAI,CAACF,MAAM,CAACG,QAAQ,CAAC,CAAC,WAAW,CAAC,EAAE;MAClCC,WAAW,EAAE;QAAEC,OAAO,EAAE;MAAK;KAC9B,CAAC;EACJ;CACD;AAbYP,oBAAoB,GAAAQ,UAAA,EAlBhCnB,SAAS,CAAC;EACToB,QAAQ,EAAE,kBAAkB;EAC5BC,UAAU,EAAE,IAAI;EAChBC,OAAO,EAAE,CACPrB,YAAY,EACZC,YAAY,EACZC,WAAW,EACXC,wBAAwB,EACxBC,+BAA+B,EAC/BC,qCAAqC,EACrCC,4BAA4B,EAC5BC,sBAAsB,EACtBC,yBAAyB,EACzBC,sBAAsB,CACvB;EACDa,WAAW,EAAE,+BAA+B;EAC5CC,SAAS,EAAE,CAAC,+BAA+B;CAC5C,CAAC,C,EACWb,oBAAoB,CAahC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}