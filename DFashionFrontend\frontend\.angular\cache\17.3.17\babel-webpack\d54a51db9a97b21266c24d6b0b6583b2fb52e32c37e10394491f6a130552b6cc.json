{"ast": null, "code": "import _asyncToGenerator from \"E:/Fashion/DFashionFrontend/frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\nimport { IonicModule } from '@ionic/angular';\nimport { Subscription, interval } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"@ionic/angular\";\nfunction TrendingDealsComponent_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 9);\n    i0.ɵɵelement(1, \"ion-spinner\", 10);\n    i0.ɵɵelementStart(2, \"p\");\n    i0.ɵɵtext(3, \"Loading trending deals...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction TrendingDealsComponent_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 11);\n    i0.ɵɵelement(1, \"ion-icon\", 12);\n    i0.ɵɵelementStart(2, \"p\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"button\", 13);\n    i0.ɵɵlistener(\"click\", function TrendingDealsComponent_div_9_Template_button_click_4_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onRetry());\n    });\n    i0.ɵɵtext(5, \"Try Again\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r1.error);\n  }\n}\nfunction TrendingDealsComponent_div_10_div_1_div_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 35)(1, \"div\", 36);\n    i0.ɵɵtext(2, \"Ends in:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 37)(4, \"div\", 38)(5, \"span\", 39);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"span\", 40);\n    i0.ɵɵtext(8, \"H\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"div\", 38)(10, \"span\", 39);\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"span\", 40);\n    i0.ɵɵtext(13, \"M\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(14, \"div\", 38)(15, \"span\", 39);\n    i0.ɵɵtext(16);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"span\", 40);\n    i0.ɵɵtext(18, \"S\");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r1.getTimeRemaining(ctx_r1.flashSale.endTime).hours);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r1.getTimeRemaining(ctx_r1.flashSale.endTime).minutes);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r1.getTimeRemaining(ctx_r1.flashSale.endTime).seconds);\n  }\n}\nfunction TrendingDealsComponent_div_10_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 21);\n    i0.ɵɵlistener(\"click\", function TrendingDealsComponent_div_10_div_1_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onDealClick(ctx_r1.flashSale));\n    });\n    i0.ɵɵelementStart(1, \"div\", 22)(2, \"div\", 23)(3, \"div\", 24);\n    i0.ɵɵelement(4, \"ion-icon\", 25);\n    i0.ɵɵtext(5, \" Flash Sale \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"h4\", 26);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"p\", 27);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"div\", 28)(11, \"span\", 29);\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"span\", 30);\n    i0.ɵɵtext(14);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"span\", 31);\n    i0.ɵɵtext(16);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(17, TrendingDealsComponent_div_10_div_1_div_17_Template, 19, 3, \"div\", 32);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"div\", 33);\n    i0.ɵɵelement(19, \"img\", 34);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate(ctx_r1.flashSale.title);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.flashSale.description);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r1.flashSale.discountPercentage, \"% OFF\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.formatPrice(ctx_r1.flashSale.originalPrice));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.formatPrice(ctx_r1.flashSale.salePrice));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.flashSale.endTime);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"src\", ctx_r1.flashSale.image, i0.ɵɵsanitizeUrl)(\"alt\", ctx_r1.flashSale.title);\n  }\n}\nfunction TrendingDealsComponent_div_10_button_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 41);\n    i0.ɵɵlistener(\"click\", function TrendingDealsComponent_div_10_button_3_Template_button_click_0_listener() {\n      const category_r5 = i0.ɵɵrestoreView(_r4).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.selectCategory(category_r5.name));\n    });\n    i0.ɵɵelement(1, \"ion-icon\", 42);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementStart(3, \"span\", 43);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const category_r5 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassProp(\"active\", ctx_r1.selectedCategory === category_r5.name);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"name\", category_r5.icon);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", category_r5.name, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(category_r5.dealCount);\n  }\n}\nfunction TrendingDealsComponent_div_10_div_5_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 62);\n    i0.ɵɵelement(1, \"ion-icon\", 63);\n    i0.ɵɵelementStart(2, \"span\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const deal_r7 = i0.ɵɵnextContext().$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate2(\"\", ctx_r1.getTimeRemaining(deal_r7.endTime).hours, \"h \", ctx_r1.getTimeRemaining(deal_r7.endTime).minutes, \"m\");\n  }\n}\nfunction TrendingDealsComponent_div_10_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 44);\n    i0.ɵɵlistener(\"click\", function TrendingDealsComponent_div_10_div_5_Template_div_click_0_listener() {\n      const deal_r7 = i0.ɵɵrestoreView(_r6).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onDealClick(deal_r7));\n    });\n    i0.ɵɵelementStart(1, \"div\", 45);\n    i0.ɵɵelement(2, \"img\", 34);\n    i0.ɵɵelementStart(3, \"div\", 46)(4, \"div\", 47);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 48);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(8, TrendingDealsComponent_div_10_div_5_div_8_Template, 4, 2, \"div\", 49);\n    i0.ɵɵelementStart(9, \"button\", 50);\n    i0.ɵɵlistener(\"click\", function TrendingDealsComponent_div_10_div_5_Template_button_click_9_listener($event) {\n      const deal_r7 = i0.ɵɵrestoreView(_r6).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.toggleFavorite(deal_r7, $event));\n    });\n    i0.ɵɵelement(10, \"ion-icon\", 42);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"div\", 51)(12, \"h5\", 52);\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"p\", 53);\n    i0.ɵɵtext(15);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"div\", 54)(17, \"span\", 30);\n    i0.ɵɵtext(18);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"span\", 31);\n    i0.ɵɵtext(20);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"span\", 55);\n    i0.ɵɵtext(22);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(23, \"div\", 56)(24, \"div\", 57)(25, \"span\", 58);\n    i0.ɵɵtext(26);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(27, \"span\", 59);\n    i0.ɵɵelement(28, \"ion-icon\", 60);\n    i0.ɵɵtext(29);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(30, \"button\", 61);\n    i0.ɵɵlistener(\"click\", function TrendingDealsComponent_div_10_div_5_Template_button_click_30_listener($event) {\n      const deal_r7 = i0.ɵɵrestoreView(_r6).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.claimDeal(deal_r7, $event));\n    });\n    i0.ɵɵtext(31, \" Claim Deal \");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const deal_r7 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"src\", deal_r7.image, i0.ɵɵsanitizeUrl)(\"alt\", deal_r7.title);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\", deal_r7.discountPercentage, \"% OFF\");\n    i0.ɵɵadvance();\n    i0.ɵɵclassMap(deal_r7.type);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(deal_r7.type.toUpperCase());\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", deal_r7.endTime);\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"active\", ctx_r1.isDealFavorited(deal_r7._id));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"name\", ctx_r1.isDealFavorited(deal_r7._id) ? \"heart\" : \"heart-outline\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(deal_r7.title);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(deal_r7.description);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r1.formatPrice(deal_r7.originalPrice));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.formatPrice(deal_r7.salePrice));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"Save \", ctx_r1.formatPrice(deal_r7.originalPrice - deal_r7.salePrice), \"\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\"\", deal_r7.soldCount, \" sold\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", deal_r7.rating, \" \");\n  }\n}\nfunction TrendingDealsComponent_div_10_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 64)(1, \"div\", 65)(2, \"div\", 66);\n    i0.ɵɵelement(3, \"ion-icon\", 2);\n    i0.ɵɵelementStart(4, \"div\", 67)(5, \"span\", 68);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"span\", 69);\n    i0.ɵɵtext(8, \"Active Deals\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(9, \"div\", 66);\n    i0.ɵɵelement(10, \"ion-icon\", 70);\n    i0.ɵɵelementStart(11, \"div\", 67)(12, \"span\", 68);\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"span\", 69);\n    i0.ɵɵtext(15, \"Total Claimed\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(16, \"div\", 66);\n    i0.ɵɵelement(17, \"ion-icon\", 71);\n    i0.ɵɵelementStart(18, \"div\", 67)(19, \"span\", 68);\n    i0.ɵɵtext(20);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"span\", 69);\n    i0.ɵɵtext(22, \"Total Savings\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(23, \"div\", 66);\n    i0.ɵɵelement(24, \"ion-icon\", 63);\n    i0.ɵɵelementStart(25, \"div\", 67)(26, \"span\", 68);\n    i0.ɵɵtext(27);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(28, \"span\", 69);\n    i0.ɵɵtext(29, \"Avg Discount\");\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r1.dealStats.activeDeals);\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate(ctx_r1.formatCount(ctx_r1.dealStats.totalClaimed));\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate(ctx_r1.formatPrice(ctx_r1.dealStats.totalSavings));\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r1.dealStats.avgDiscountPercentage, \"%\");\n  }\n}\nfunction TrendingDealsComponent_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 14);\n    i0.ɵɵtemplate(1, TrendingDealsComponent_div_10_div_1_Template, 20, 8, \"div\", 15);\n    i0.ɵɵelementStart(2, \"div\", 16);\n    i0.ɵɵtemplate(3, TrendingDealsComponent_div_10_button_3_Template, 5, 5, \"button\", 17);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 18);\n    i0.ɵɵtemplate(5, TrendingDealsComponent_div_10_div_5_Template, 32, 17, \"div\", 19);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(6, TrendingDealsComponent_div_10_div_6_Template, 30, 4, \"div\", 20);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.flashSale);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.dealCategories)(\"ngForTrackBy\", ctx_r1.trackByCategoryName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.filteredDeals)(\"ngForTrackBy\", ctx_r1.trackByDealId);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.dealStats);\n  }\n}\nfunction TrendingDealsComponent_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 72);\n    i0.ɵɵelement(1, \"ion-icon\", 2);\n    i0.ɵɵelementStart(2, \"p\");\n    i0.ɵɵtext(3, \"No trending deals available\");\n    i0.ɵɵelementEnd()();\n  }\n}\nexport class TrendingDealsComponent {\n  constructor(router) {\n    this.router = router;\n    this.trendingDeals = [];\n    this.flashSale = null;\n    this.filteredDeals = [];\n    this.dealCategories = [];\n    this.dealStats = null;\n    this.selectedCategory = 'All';\n    this.favoritedDeals = new Set();\n    this.isLoading = true;\n    this.error = null;\n    this.subscription = new Subscription();\n    this.timerSubscription = new Subscription();\n  }\n  ngOnInit() {\n    this.loadTrendingDeals();\n    this.startTimer();\n  }\n  ngOnDestroy() {\n    this.subscription.unsubscribe();\n    this.timerSubscription.unsubscribe();\n  }\n  loadTrendingDeals() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      try {\n        _this.isLoading = true;\n        _this.error = null;\n        // Mock data for now - replace with actual service call\n        yield _this.loadMockDeals();\n      } catch (error) {\n        console.error('Error loading trending deals:', error);\n        _this.error = 'Failed to load trending deals';\n        _this.isLoading = false;\n      }\n    })();\n  }\n  loadMockDeals() {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      // Simulate API call delay\n      yield new Promise(resolve => setTimeout(resolve, 800));\n      const now = new Date();\n      const endTime1 = new Date(now.getTime() + 2 * 60 * 60 * 1000); // 2 hours from now\n      const endTime2 = new Date(now.getTime() + 6 * 60 * 60 * 1000); // 6 hours from now\n      _this2.trendingDeals = [{\n        _id: 'deal1',\n        title: 'Summer Collection Flash Sale',\n        description: 'Limited time offer on summer essentials',\n        image: 'https://images.unsplash.com/photo-1515372039744-b8f02a3ae446?w=300&h=200&fit=crop',\n        originalPrice: 4999,\n        salePrice: 2499,\n        discountPercentage: 50,\n        type: 'flash',\n        category: 'Women',\n        endTime: endTime1,\n        soldCount: 234,\n        rating: 4.5,\n        isActive: true,\n        createdAt: new Date()\n      }, {\n        _id: 'deal2',\n        title: 'Designer Sneakers Deal',\n        description: 'Premium sneakers at unbeatable prices',\n        image: 'https://images.unsplash.com/photo-1542291026-7eec264c27ff?w=300&h=200&fit=crop',\n        originalPrice: 8999,\n        salePrice: 5999,\n        discountPercentage: 33,\n        type: 'daily',\n        category: 'Footwear',\n        endTime: endTime2,\n        soldCount: 156,\n        rating: 4.7,\n        isActive: true,\n        createdAt: new Date()\n      }, {\n        _id: 'deal3',\n        title: 'Handbag Clearance',\n        description: 'Stylish handbags at clearance prices',\n        image: 'https://images.unsplash.com/photo-1553062407-98eeb64c6a62?w=300&h=200&fit=crop',\n        originalPrice: 6999,\n        salePrice: 3499,\n        discountPercentage: 50,\n        type: 'clearance',\n        category: 'Accessories',\n        soldCount: 89,\n        rating: 4.3,\n        isActive: true,\n        createdAt: new Date()\n      }, {\n        _id: 'deal4',\n        title: 'Men\\'s Formal Wear',\n        description: 'Professional attire for the modern man',\n        image: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=300&h=200&fit=crop',\n        originalPrice: 12999,\n        salePrice: 8999,\n        discountPercentage: 31,\n        type: 'weekly',\n        category: 'Men',\n        soldCount: 67,\n        rating: 4.6,\n        isActive: true,\n        createdAt: new Date()\n      }];\n      // Set flash sale (highest discount percentage)\n      _this2.flashSale = _this2.trendingDeals.reduce((prev, current) => prev.discountPercentage > current.discountPercentage ? prev : current);\n      // Set up categories\n      _this2.dealCategories = [{\n        name: 'All',\n        icon: 'apps-outline',\n        dealCount: _this2.trendingDeals.length\n      }, {\n        name: 'Women',\n        icon: 'woman-outline',\n        dealCount: _this2.trendingDeals.filter(d => d.category === 'Women').length\n      }, {\n        name: 'Men',\n        icon: 'man-outline',\n        dealCount: _this2.trendingDeals.filter(d => d.category === 'Men').length\n      }, {\n        name: 'Footwear',\n        icon: 'footsteps-outline',\n        dealCount: _this2.trendingDeals.filter(d => d.category === 'Footwear').length\n      }, {\n        name: 'Accessories',\n        icon: 'bag-outline',\n        dealCount: _this2.trendingDeals.filter(d => d.category === 'Accessories').length\n      }];\n      // Set up deal stats\n      _this2.dealStats = {\n        activeDeals: _this2.trendingDeals.filter(d => d.isActive).length,\n        totalClaimed: _this2.trendingDeals.reduce((sum, deal) => sum + deal.soldCount, 0),\n        totalSavings: _this2.trendingDeals.reduce((sum, deal) => sum + (deal.originalPrice - deal.salePrice) * deal.soldCount, 0),\n        avgDiscountPercentage: Math.round(_this2.trendingDeals.reduce((sum, deal) => sum + deal.discountPercentage, 0) / _this2.trendingDeals.length)\n      };\n      _this2.filterDeals();\n      _this2.isLoading = false;\n    })();\n  }\n  startTimer() {\n    this.timerSubscription = interval(1000).subscribe(() => {\n      // Timer updates every second for countdown\n    });\n  }\n  selectCategory(category) {\n    this.selectedCategory = category;\n    this.filterDeals();\n  }\n  filterDeals() {\n    if (this.selectedCategory === 'All') {\n      this.filteredDeals = this.trendingDeals.filter(deal => deal._id !== this.flashSale?._id);\n    } else {\n      this.filteredDeals = this.trendingDeals.filter(deal => deal.category === this.selectedCategory && deal._id !== this.flashSale?._id);\n    }\n  }\n  getTimeRemaining(endTime) {\n    const now = new Date();\n    const timeDiff = endTime.getTime() - now.getTime();\n    if (timeDiff <= 0) {\n      return {\n        hours: 0,\n        minutes: 0,\n        seconds: 0\n      };\n    }\n    const hours = Math.floor(timeDiff / (1000 * 60 * 60));\n    const minutes = Math.floor(timeDiff % (1000 * 60 * 60) / (1000 * 60));\n    const seconds = Math.floor(timeDiff % (1000 * 60) / 1000);\n    return {\n      hours,\n      minutes,\n      seconds\n    };\n  }\n  onDealClick(deal) {\n    this.router.navigate(['/deal', deal._id]);\n  }\n  claimDeal(deal, event) {\n    event.stopPropagation();\n    // Handle deal claiming logic\n    deal.soldCount++;\n    console.log('Deal claimed:', deal.title);\n  }\n  toggleFavorite(deal, event) {\n    event.stopPropagation();\n    if (this.favoritedDeals.has(deal._id)) {\n      this.favoritedDeals.delete(deal._id);\n    } else {\n      this.favoritedDeals.add(deal._id);\n    }\n  }\n  isDealFavorited(dealId) {\n    return this.favoritedDeals.has(dealId);\n  }\n  formatPrice(price) {\n    return new Intl.NumberFormat('en-IN', {\n      style: 'currency',\n      currency: 'INR',\n      minimumFractionDigits: 0\n    }).format(price);\n  }\n  formatCount(count) {\n    if (count >= 1000000) {\n      return (count / 1000000).toFixed(1) + 'M';\n    } else if (count >= 1000) {\n      return (count / 1000).toFixed(1) + 'K';\n    }\n    return count.toString();\n  }\n  trackByDealId(index, deal) {\n    return deal._id;\n  }\n  trackByCategoryName(index, category) {\n    return category.name;\n  }\n  onSeeAll(event) {\n    event.preventDefault();\n    this.router.navigate(['/trending/deals']);\n  }\n  onRetry() {\n    this.loadTrendingDeals();\n  }\n  static {\n    this.ɵfac = function TrendingDealsComponent_Factory(t) {\n      return new (t || TrendingDealsComponent)(i0.ɵɵdirectiveInject(i1.Router));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: TrendingDealsComponent,\n      selectors: [[\"app-trending-deals\"]],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 12,\n      vars: 4,\n      consts: [[1, \"component-container\"], [1, \"component-header\"], [\"name\", \"flash-outline\"], [\"href\", \"#\", 1, \"see-all\", 3, \"click\"], [1, \"component-content\"], [\"class\", \"loading-container\", 4, \"ngIf\"], [\"class\", \"error-container\", 4, \"ngIf\"], [\"class\", \"deals-content\", 4, \"ngIf\"], [\"class\", \"empty-container\", 4, \"ngIf\"], [1, \"loading-container\"], [\"name\", \"crescent\"], [1, \"error-container\"], [\"name\", \"alert-circle-outline\"], [1, \"retry-btn\", 3, \"click\"], [1, \"deals-content\"], [\"class\", \"flash-sale-banner\", 3, \"click\", 4, \"ngIf\"], [1, \"deal-categories\"], [\"class\", \"category-btn\", 3, \"active\", \"click\", 4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [1, \"deals-grid\"], [\"class\", \"deal-item\", 3, \"click\", 4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [\"class\", \"deal-stats-summary\", 4, \"ngIf\"], [1, \"flash-sale-banner\", 3, \"click\"], [1, \"banner-content\"], [1, \"sale-info\"], [1, \"sale-badge\"], [\"name\", \"flash\"], [1, \"sale-title\"], [1, \"sale-description\"], [1, \"discount-info\"], [1, \"discount-percentage\"], [1, \"original-price\"], [1, \"sale-price\"], [\"class\", \"countdown-timer\", 4, \"ngIf\"], [1, \"banner-image\"], [1, \"deal-image\", 3, \"src\", \"alt\"], [1, \"countdown-timer\"], [1, \"timer-label\"], [1, \"timer-display\"], [1, \"time-unit\"], [1, \"time-value\"], [1, \"time-label\"], [1, \"category-btn\", 3, \"click\"], [3, \"name\"], [1, \"deal-count\"], [1, \"deal-item\", 3, \"click\"], [1, \"deal-image-container\"], [1, \"deal-badges\"], [1, \"discount-badge\"], [1, \"deal-type-badge\"], [\"class\", \"deal-timer\", 4, \"ngIf\"], [1, \"favorite-btn\", 3, \"click\"], [1, \"deal-info\"], [1, \"deal-title\"], [1, \"deal-description\"], [1, \"price-info\"], [1, \"savings\"], [1, \"deal-meta\"], [1, \"deal-stats\"], [1, \"sold-count\"], [1, \"rating\"], [\"name\", \"star\"], [1, \"claim-btn\", 3, \"click\"], [1, \"deal-timer\"], [\"name\", \"time-outline\"], [1, \"deal-stats-summary\"], [1, \"stats-grid\"], [1, \"stat-item\"], [1, \"stat-info\"], [1, \"stat-value\"], [1, \"stat-label\"], [\"name\", \"people-outline\"], [\"name\", \"cash-outline\"], [1, \"empty-container\"]],\n      template: function TrendingDealsComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"h3\");\n          i0.ɵɵelement(3, \"ion-icon\", 2);\n          i0.ɵɵtext(4, \" Trending Deals \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(5, \"a\", 3);\n          i0.ɵɵlistener(\"click\", function TrendingDealsComponent_Template_a_click_5_listener($event) {\n            return ctx.onSeeAll($event);\n          });\n          i0.ɵɵtext(6, \"See All\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(7, \"div\", 4);\n          i0.ɵɵtemplate(8, TrendingDealsComponent_div_8_Template, 4, 0, \"div\", 5)(9, TrendingDealsComponent_div_9_Template, 6, 1, \"div\", 6)(10, TrendingDealsComponent_div_10_Template, 7, 6, \"div\", 7)(11, TrendingDealsComponent_div_11_Template, 4, 0, \"div\", 8);\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.error && !ctx.isLoading);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading && !ctx.error && ctx.trendingDeals.length > 0);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading && !ctx.error && ctx.trendingDeals.length === 0);\n        }\n      },\n      dependencies: [CommonModule, i2.NgForOf, i2.NgIf, RouterModule, IonicModule, i3.IonIcon, i3.IonSpinner],\n      styles: [\".loading-container[_ngcontent-%COMP%], .error-container[_ngcontent-%COMP%], .empty-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n  padding: 40px 20px;\\n  text-align: center;\\n}\\n.loading-container[_ngcontent-%COMP%]   ion-spinner[_ngcontent-%COMP%], .loading-container[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%], .error-container[_ngcontent-%COMP%]   ion-spinner[_ngcontent-%COMP%], .error-container[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%], .empty-container[_ngcontent-%COMP%]   ion-spinner[_ngcontent-%COMP%], .empty-container[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n  margin-bottom: 16px;\\n  font-size: 48px;\\n}\\n.loading-container[_ngcontent-%COMP%]   p[_ngcontent-%COMP%], .error-container[_ngcontent-%COMP%]   p[_ngcontent-%COMP%], .empty-container[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0 0 16px 0;\\n  font-size: 14px;\\n}\\n.loading-container[_ngcontent-%COMP%]   .retry-btn[_ngcontent-%COMP%], .error-container[_ngcontent-%COMP%]   .retry-btn[_ngcontent-%COMP%], .empty-container[_ngcontent-%COMP%]   .retry-btn[_ngcontent-%COMP%] {\\n  background: #e74c3c;\\n  color: white;\\n  border: none;\\n  padding: 8px 16px;\\n  border-radius: 6px;\\n  font-size: 12px;\\n  cursor: pointer;\\n  transition: background 0.3s ease;\\n}\\n.loading-container[_ngcontent-%COMP%]   .retry-btn[_ngcontent-%COMP%]:hover, .error-container[_ngcontent-%COMP%]   .retry-btn[_ngcontent-%COMP%]:hover, .empty-container[_ngcontent-%COMP%]   .retry-btn[_ngcontent-%COMP%]:hover {\\n  background: #c0392b;\\n}\\n\\n.deals-content[_ngcontent-%COMP%]   .flash-sale-banner[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #ff6b6b, #ee5a24);\\n  border-radius: 16px;\\n  padding: 24px;\\n  margin-bottom: 24px;\\n  cursor: pointer;\\n  transition: transform 0.3s ease;\\n  color: white;\\n}\\n.deals-content[_ngcontent-%COMP%]   .flash-sale-banner[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-2px);\\n}\\n.deals-content[_ngcontent-%COMP%]   .flash-sale-banner[_ngcontent-%COMP%]   .banner-content[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n}\\n.deals-content[_ngcontent-%COMP%]   .flash-sale-banner[_ngcontent-%COMP%]   .banner-content[_ngcontent-%COMP%]   .sale-info[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n.deals-content[_ngcontent-%COMP%]   .flash-sale-banner[_ngcontent-%COMP%]   .banner-content[_ngcontent-%COMP%]   .sale-info[_ngcontent-%COMP%]   .sale-badge[_ngcontent-%COMP%] {\\n  display: inline-flex;\\n  align-items: center;\\n  gap: 6px;\\n  background: rgba(255, 255, 255, 0.2);\\n  padding: 6px 12px;\\n  border-radius: 20px;\\n  font-size: 12px;\\n  font-weight: 600;\\n  margin-bottom: 12px;\\n}\\n.deals-content[_ngcontent-%COMP%]   .flash-sale-banner[_ngcontent-%COMP%]   .banner-content[_ngcontent-%COMP%]   .sale-info[_ngcontent-%COMP%]   .sale-badge[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  animation: _ngcontent-%COMP%_flash 1s infinite;\\n}\\n.deals-content[_ngcontent-%COMP%]   .flash-sale-banner[_ngcontent-%COMP%]   .banner-content[_ngcontent-%COMP%]   .sale-info[_ngcontent-%COMP%]   .sale-title[_ngcontent-%COMP%] {\\n  font-size: 24px;\\n  font-weight: 700;\\n  margin: 0 0 8px 0;\\n}\\n.deals-content[_ngcontent-%COMP%]   .flash-sale-banner[_ngcontent-%COMP%]   .banner-content[_ngcontent-%COMP%]   .sale-info[_ngcontent-%COMP%]   .sale-description[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  margin: 0 0 16px 0;\\n  opacity: 0.9;\\n}\\n.deals-content[_ngcontent-%COMP%]   .flash-sale-banner[_ngcontent-%COMP%]   .banner-content[_ngcontent-%COMP%]   .sale-info[_ngcontent-%COMP%]   .discount-info[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 12px;\\n}\\n.deals-content[_ngcontent-%COMP%]   .flash-sale-banner[_ngcontent-%COMP%]   .banner-content[_ngcontent-%COMP%]   .sale-info[_ngcontent-%COMP%]   .discount-info[_ngcontent-%COMP%]   .discount-percentage[_ngcontent-%COMP%] {\\n  background: #ffd700;\\n  color: #333;\\n  padding: 6px 12px;\\n  border-radius: 20px;\\n  font-weight: 700;\\n  font-size: 14px;\\n}\\n.deals-content[_ngcontent-%COMP%]   .flash-sale-banner[_ngcontent-%COMP%]   .banner-content[_ngcontent-%COMP%]   .sale-info[_ngcontent-%COMP%]   .discount-info[_ngcontent-%COMP%]   .original-price[_ngcontent-%COMP%] {\\n  text-decoration: line-through;\\n  opacity: 0.7;\\n  font-size: 14px;\\n}\\n.deals-content[_ngcontent-%COMP%]   .flash-sale-banner[_ngcontent-%COMP%]   .banner-content[_ngcontent-%COMP%]   .sale-info[_ngcontent-%COMP%]   .discount-info[_ngcontent-%COMP%]   .sale-price[_ngcontent-%COMP%] {\\n  font-size: 18px;\\n  font-weight: 700;\\n}\\n.deals-content[_ngcontent-%COMP%]   .flash-sale-banner[_ngcontent-%COMP%]   .banner-content[_ngcontent-%COMP%]   .countdown-timer[_ngcontent-%COMP%] {\\n  text-align: center;\\n}\\n.deals-content[_ngcontent-%COMP%]   .flash-sale-banner[_ngcontent-%COMP%]   .banner-content[_ngcontent-%COMP%]   .countdown-timer[_ngcontent-%COMP%]   .timer-label[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  margin-bottom: 8px;\\n  opacity: 0.8;\\n}\\n.deals-content[_ngcontent-%COMP%]   .flash-sale-banner[_ngcontent-%COMP%]   .banner-content[_ngcontent-%COMP%]   .countdown-timer[_ngcontent-%COMP%]   .timer-display[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 8px;\\n}\\n.deals-content[_ngcontent-%COMP%]   .flash-sale-banner[_ngcontent-%COMP%]   .banner-content[_ngcontent-%COMP%]   .countdown-timer[_ngcontent-%COMP%]   .timer-display[_ngcontent-%COMP%]   .time-unit[_ngcontent-%COMP%] {\\n  background: rgba(255, 255, 255, 0.2);\\n  padding: 8px;\\n  border-radius: 8px;\\n  text-align: center;\\n  min-width: 40px;\\n}\\n.deals-content[_ngcontent-%COMP%]   .flash-sale-banner[_ngcontent-%COMP%]   .banner-content[_ngcontent-%COMP%]   .countdown-timer[_ngcontent-%COMP%]   .timer-display[_ngcontent-%COMP%]   .time-unit[_ngcontent-%COMP%]   .time-value[_ngcontent-%COMP%] {\\n  display: block;\\n  font-size: 18px;\\n  font-weight: 700;\\n}\\n.deals-content[_ngcontent-%COMP%]   .flash-sale-banner[_ngcontent-%COMP%]   .banner-content[_ngcontent-%COMP%]   .countdown-timer[_ngcontent-%COMP%]   .timer-display[_ngcontent-%COMP%]   .time-unit[_ngcontent-%COMP%]   .time-label[_ngcontent-%COMP%] {\\n  font-size: 10px;\\n  opacity: 0.8;\\n}\\n.deals-content[_ngcontent-%COMP%]   .flash-sale-banner[_ngcontent-%COMP%]   .banner-image[_ngcontent-%COMP%] {\\n  width: 120px;\\n  height: 80px;\\n  border-radius: 8px;\\n  overflow: hidden;\\n  margin-left: 20px;\\n}\\n.deals-content[_ngcontent-%COMP%]   .flash-sale-banner[_ngcontent-%COMP%]   .banner-image[_ngcontent-%COMP%]   .deal-image[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 100%;\\n  object-fit: cover;\\n}\\n.deals-content[_ngcontent-%COMP%]   .deal-categories[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 8px;\\n  margin-bottom: 20px;\\n  overflow-x: auto;\\n  padding-bottom: 8px;\\n}\\n.deals-content[_ngcontent-%COMP%]   .deal-categories[_ngcontent-%COMP%]   .category-btn[_ngcontent-%COMP%] {\\n  background: #f8f9fa;\\n  border: 1px solid #dee2e6;\\n  border-radius: 20px;\\n  padding: 8px 16px;\\n  font-size: 12px;\\n  font-weight: 600;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n  display: flex;\\n  align-items: center;\\n  gap: 6px;\\n  white-space: nowrap;\\n}\\n.deals-content[_ngcontent-%COMP%]   .deal-categories[_ngcontent-%COMP%]   .category-btn[_ngcontent-%COMP%]:hover {\\n  background: #e9ecef;\\n}\\n.deals-content[_ngcontent-%COMP%]   .deal-categories[_ngcontent-%COMP%]   .category-btn.active[_ngcontent-%COMP%] {\\n  background: #6c5ce7;\\n  color: white;\\n  border-color: #6c5ce7;\\n}\\n.deals-content[_ngcontent-%COMP%]   .deal-categories[_ngcontent-%COMP%]   .category-btn[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n}\\n.deals-content[_ngcontent-%COMP%]   .deal-categories[_ngcontent-%COMP%]   .category-btn[_ngcontent-%COMP%]   .deal-count[_ngcontent-%COMP%] {\\n  background: rgba(0, 0, 0, 0.1);\\n  padding: 2px 6px;\\n  border-radius: 10px;\\n  font-size: 10px;\\n}\\n.deals-content[_ngcontent-%COMP%]   .deals-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));\\n  gap: 16px;\\n  margin-bottom: 24px;\\n}\\n.deals-content[_ngcontent-%COMP%]   .deals-grid[_ngcontent-%COMP%]   .deal-item[_ngcontent-%COMP%] {\\n  background: white;\\n  border-radius: 12px;\\n  overflow: hidden;\\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n}\\n.deals-content[_ngcontent-%COMP%]   .deals-grid[_ngcontent-%COMP%]   .deal-item[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-2px);\\n  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);\\n}\\n.deals-content[_ngcontent-%COMP%]   .deals-grid[_ngcontent-%COMP%]   .deal-item[_ngcontent-%COMP%]   .deal-image-container[_ngcontent-%COMP%] {\\n  position: relative;\\n  height: 160px;\\n  overflow: hidden;\\n}\\n.deals-content[_ngcontent-%COMP%]   .deals-grid[_ngcontent-%COMP%]   .deal-item[_ngcontent-%COMP%]   .deal-image-container[_ngcontent-%COMP%]   .deal-image[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 100%;\\n  object-fit: cover;\\n  transition: transform 0.3s ease;\\n}\\n.deals-content[_ngcontent-%COMP%]   .deals-grid[_ngcontent-%COMP%]   .deal-item[_ngcontent-%COMP%]   .deal-image-container[_ngcontent-%COMP%]:hover   .deal-image[_ngcontent-%COMP%] {\\n  transform: scale(1.05);\\n}\\n.deals-content[_ngcontent-%COMP%]   .deals-grid[_ngcontent-%COMP%]   .deal-item[_ngcontent-%COMP%]   .deal-image-container[_ngcontent-%COMP%]   .deal-badges[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 8px;\\n  left: 8px;\\n  display: flex;\\n  flex-direction: column;\\n  gap: 4px;\\n}\\n.deals-content[_ngcontent-%COMP%]   .deals-grid[_ngcontent-%COMP%]   .deal-item[_ngcontent-%COMP%]   .deal-image-container[_ngcontent-%COMP%]   .deal-badges[_ngcontent-%COMP%]   .discount-badge[_ngcontent-%COMP%] {\\n  background: #ff6b6b;\\n  color: white;\\n  padding: 4px 8px;\\n  border-radius: 12px;\\n  font-size: 10px;\\n  font-weight: 600;\\n}\\n.deals-content[_ngcontent-%COMP%]   .deals-grid[_ngcontent-%COMP%]   .deal-item[_ngcontent-%COMP%]   .deal-image-container[_ngcontent-%COMP%]   .deal-badges[_ngcontent-%COMP%]   .deal-type-badge[_ngcontent-%COMP%] {\\n  padding: 4px 8px;\\n  border-radius: 12px;\\n  font-size: 9px;\\n  font-weight: 600;\\n  text-transform: uppercase;\\n}\\n.deals-content[_ngcontent-%COMP%]   .deals-grid[_ngcontent-%COMP%]   .deal-item[_ngcontent-%COMP%]   .deal-image-container[_ngcontent-%COMP%]   .deal-badges[_ngcontent-%COMP%]   .deal-type-badge.flash[_ngcontent-%COMP%] {\\n  background: #ffd700;\\n  color: #333;\\n}\\n.deals-content[_ngcontent-%COMP%]   .deals-grid[_ngcontent-%COMP%]   .deal-item[_ngcontent-%COMP%]   .deal-image-container[_ngcontent-%COMP%]   .deal-badges[_ngcontent-%COMP%]   .deal-type-badge.daily[_ngcontent-%COMP%] {\\n  background: #4ecdc4;\\n  color: white;\\n}\\n.deals-content[_ngcontent-%COMP%]   .deals-grid[_ngcontent-%COMP%]   .deal-item[_ngcontent-%COMP%]   .deal-image-container[_ngcontent-%COMP%]   .deal-badges[_ngcontent-%COMP%]   .deal-type-badge.weekly[_ngcontent-%COMP%] {\\n  background: #6c5ce7;\\n  color: white;\\n}\\n.deals-content[_ngcontent-%COMP%]   .deals-grid[_ngcontent-%COMP%]   .deal-item[_ngcontent-%COMP%]   .deal-image-container[_ngcontent-%COMP%]   .deal-badges[_ngcontent-%COMP%]   .deal-type-badge.clearance[_ngcontent-%COMP%] {\\n  background: #f39c12;\\n  color: white;\\n}\\n.deals-content[_ngcontent-%COMP%]   .deals-grid[_ngcontent-%COMP%]   .deal-item[_ngcontent-%COMP%]   .deal-image-container[_ngcontent-%COMP%]   .deal-timer[_ngcontent-%COMP%] {\\n  position: absolute;\\n  bottom: 8px;\\n  left: 8px;\\n  background: rgba(0, 0, 0, 0.7);\\n  color: white;\\n  padding: 4px 8px;\\n  border-radius: 12px;\\n  font-size: 10px;\\n  display: flex;\\n  align-items: center;\\n  gap: 4px;\\n}\\n.deals-content[_ngcontent-%COMP%]   .deals-grid[_ngcontent-%COMP%]   .deal-item[_ngcontent-%COMP%]   .deal-image-container[_ngcontent-%COMP%]   .deal-timer[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n}\\n.deals-content[_ngcontent-%COMP%]   .deals-grid[_ngcontent-%COMP%]   .deal-item[_ngcontent-%COMP%]   .deal-image-container[_ngcontent-%COMP%]   .favorite-btn[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 8px;\\n  right: 8px;\\n  background: rgba(255, 255, 255, 0.9);\\n  border: none;\\n  border-radius: 50%;\\n  width: 32px;\\n  height: 32px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n}\\n.deals-content[_ngcontent-%COMP%]   .deals-grid[_ngcontent-%COMP%]   .deal-item[_ngcontent-%COMP%]   .deal-image-container[_ngcontent-%COMP%]   .favorite-btn[_ngcontent-%COMP%]:hover {\\n  background: white;\\n  transform: scale(1.1);\\n}\\n.deals-content[_ngcontent-%COMP%]   .deals-grid[_ngcontent-%COMP%]   .deal-item[_ngcontent-%COMP%]   .deal-image-container[_ngcontent-%COMP%]   .favorite-btn.active[_ngcontent-%COMP%] {\\n  background: #ff6b6b;\\n  color: white;\\n}\\n.deals-content[_ngcontent-%COMP%]   .deals-grid[_ngcontent-%COMP%]   .deal-item[_ngcontent-%COMP%]   .deal-image-container[_ngcontent-%COMP%]   .favorite-btn[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n}\\n.deals-content[_ngcontent-%COMP%]   .deals-grid[_ngcontent-%COMP%]   .deal-item[_ngcontent-%COMP%]   .deal-info[_ngcontent-%COMP%] {\\n  padding: 16px;\\n}\\n.deals-content[_ngcontent-%COMP%]   .deals-grid[_ngcontent-%COMP%]   .deal-item[_ngcontent-%COMP%]   .deal-info[_ngcontent-%COMP%]   .deal-title[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  font-weight: 600;\\n  margin: 0 0 8px 0;\\n  color: #333;\\n}\\n.deals-content[_ngcontent-%COMP%]   .deals-grid[_ngcontent-%COMP%]   .deal-item[_ngcontent-%COMP%]   .deal-info[_ngcontent-%COMP%]   .deal-description[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  color: #666;\\n  margin: 0 0 12px 0;\\n  line-height: 1.4;\\n}\\n.deals-content[_ngcontent-%COMP%]   .deals-grid[_ngcontent-%COMP%]   .deal-item[_ngcontent-%COMP%]   .deal-info[_ngcontent-%COMP%]   .price-info[_ngcontent-%COMP%] {\\n  margin-bottom: 12px;\\n}\\n.deals-content[_ngcontent-%COMP%]   .deals-grid[_ngcontent-%COMP%]   .deal-item[_ngcontent-%COMP%]   .deal-info[_ngcontent-%COMP%]   .price-info[_ngcontent-%COMP%]   .original-price[_ngcontent-%COMP%] {\\n  text-decoration: line-through;\\n  color: #999;\\n  font-size: 12px;\\n  margin-right: 8px;\\n}\\n.deals-content[_ngcontent-%COMP%]   .deals-grid[_ngcontent-%COMP%]   .deal-item[_ngcontent-%COMP%]   .deal-info[_ngcontent-%COMP%]   .price-info[_ngcontent-%COMP%]   .sale-price[_ngcontent-%COMP%] {\\n  font-size: 18px;\\n  font-weight: 700;\\n  color: #333;\\n  margin-right: 8px;\\n}\\n.deals-content[_ngcontent-%COMP%]   .deals-grid[_ngcontent-%COMP%]   .deal-item[_ngcontent-%COMP%]   .deal-info[_ngcontent-%COMP%]   .price-info[_ngcontent-%COMP%]   .savings[_ngcontent-%COMP%] {\\n  background: #e8f5e8;\\n  color: #28a745;\\n  padding: 2px 6px;\\n  border-radius: 8px;\\n  font-size: 10px;\\n  font-weight: 600;\\n}\\n.deals-content[_ngcontent-%COMP%]   .deals-grid[_ngcontent-%COMP%]   .deal-item[_ngcontent-%COMP%]   .deal-info[_ngcontent-%COMP%]   .deal-meta[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n}\\n.deals-content[_ngcontent-%COMP%]   .deals-grid[_ngcontent-%COMP%]   .deal-item[_ngcontent-%COMP%]   .deal-info[_ngcontent-%COMP%]   .deal-meta[_ngcontent-%COMP%]   .deal-stats[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 12px;\\n  font-size: 11px;\\n  color: #666;\\n}\\n.deals-content[_ngcontent-%COMP%]   .deals-grid[_ngcontent-%COMP%]   .deal-item[_ngcontent-%COMP%]   .deal-info[_ngcontent-%COMP%]   .deal-meta[_ngcontent-%COMP%]   .deal-stats[_ngcontent-%COMP%]   .sold-count[_ngcontent-%COMP%] {\\n  color: #28a745;\\n}\\n.deals-content[_ngcontent-%COMP%]   .deals-grid[_ngcontent-%COMP%]   .deal-item[_ngcontent-%COMP%]   .deal-info[_ngcontent-%COMP%]   .deal-meta[_ngcontent-%COMP%]   .deal-stats[_ngcontent-%COMP%]   .rating[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 2px;\\n}\\n.deals-content[_ngcontent-%COMP%]   .deals-grid[_ngcontent-%COMP%]   .deal-item[_ngcontent-%COMP%]   .deal-info[_ngcontent-%COMP%]   .deal-meta[_ngcontent-%COMP%]   .deal-stats[_ngcontent-%COMP%]   .rating[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  color: #ffd700;\\n}\\n.deals-content[_ngcontent-%COMP%]   .deals-grid[_ngcontent-%COMP%]   .deal-item[_ngcontent-%COMP%]   .deal-info[_ngcontent-%COMP%]   .deal-meta[_ngcontent-%COMP%]   .claim-btn[_ngcontent-%COMP%] {\\n  background: #6c5ce7;\\n  color: white;\\n  border: none;\\n  padding: 6px 12px;\\n  border-radius: 16px;\\n  font-size: 11px;\\n  font-weight: 600;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n}\\n.deals-content[_ngcontent-%COMP%]   .deals-grid[_ngcontent-%COMP%]   .deal-item[_ngcontent-%COMP%]   .deal-info[_ngcontent-%COMP%]   .deal-meta[_ngcontent-%COMP%]   .claim-btn[_ngcontent-%COMP%]:hover {\\n  background: #5a4fcf;\\n  transform: scale(1.05);\\n}\\n.deals-content[_ngcontent-%COMP%]   .deal-stats-summary[_ngcontent-%COMP%]   .stats-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));\\n  gap: 16px;\\n  padding: 20px;\\n  background: linear-gradient(135deg, #f8f9fa, #e9ecef);\\n  border-radius: 12px;\\n}\\n.deals-content[_ngcontent-%COMP%]   .deal-stats-summary[_ngcontent-%COMP%]   .stats-grid[_ngcontent-%COMP%]   .stat-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 12px;\\n  padding: 12px;\\n  background: white;\\n  border-radius: 8px;\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\\n}\\n.deals-content[_ngcontent-%COMP%]   .deal-stats-summary[_ngcontent-%COMP%]   .stats-grid[_ngcontent-%COMP%]   .stat-item[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n  font-size: 24px;\\n  color: #6c5ce7;\\n}\\n.deals-content[_ngcontent-%COMP%]   .deal-stats-summary[_ngcontent-%COMP%]   .stats-grid[_ngcontent-%COMP%]   .stat-item[_ngcontent-%COMP%]   .stat-info[_ngcontent-%COMP%]   .stat-value[_ngcontent-%COMP%] {\\n  display: block;\\n  font-size: 18px;\\n  font-weight: 700;\\n  color: #333;\\n}\\n.deals-content[_ngcontent-%COMP%]   .deal-stats-summary[_ngcontent-%COMP%]   .stats-grid[_ngcontent-%COMP%]   .stat-item[_ngcontent-%COMP%]   .stat-info[_ngcontent-%COMP%]   .stat-label[_ngcontent-%COMP%] {\\n  font-size: 11px;\\n  color: #666;\\n  text-transform: uppercase;\\n  letter-spacing: 0.5px;\\n}\\n\\n@keyframes _ngcontent-%COMP%_flash {\\n  0%, 50% {\\n    opacity: 1;\\n  }\\n  51%, 100% {\\n    opacity: 0.5;\\n  }\\n}\\n@media (max-width: 768px) {\\n  .deals-content[_ngcontent-%COMP%]   .flash-sale-banner[_ngcontent-%COMP%] {\\n    padding: 16px;\\n  }\\n  .deals-content[_ngcontent-%COMP%]   .flash-sale-banner[_ngcontent-%COMP%]   .banner-content[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 16px;\\n  }\\n  .deals-content[_ngcontent-%COMP%]   .flash-sale-banner[_ngcontent-%COMP%]   .banner-content[_ngcontent-%COMP%]   .sale-info[_ngcontent-%COMP%] {\\n    text-align: center;\\n  }\\n  .deals-content[_ngcontent-%COMP%]   .flash-sale-banner[_ngcontent-%COMP%]   .banner-content[_ngcontent-%COMP%]   .sale-info[_ngcontent-%COMP%]   .sale-title[_ngcontent-%COMP%] {\\n    font-size: 20px;\\n  }\\n  .deals-content[_ngcontent-%COMP%]   .flash-sale-banner[_ngcontent-%COMP%]   .banner-content[_ngcontent-%COMP%]   .countdown-timer[_ngcontent-%COMP%]   .timer-display[_ngcontent-%COMP%] {\\n    justify-content: center;\\n  }\\n  .deals-content[_ngcontent-%COMP%]   .flash-sale-banner[_ngcontent-%COMP%]   .banner-image[_ngcontent-%COMP%] {\\n    width: 100px;\\n    height: 60px;\\n    margin-left: 0;\\n  }\\n  .deals-content[_ngcontent-%COMP%]   .deals-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\\n    gap: 12px;\\n  }\\n  .deals-content[_ngcontent-%COMP%]   .deals-grid[_ngcontent-%COMP%]   .deal-item[_ngcontent-%COMP%]   .deal-image-container[_ngcontent-%COMP%] {\\n    height: 140px;\\n  }\\n  .deals-content[_ngcontent-%COMP%]   .deals-grid[_ngcontent-%COMP%]   .deal-item[_ngcontent-%COMP%]   .deal-info[_ngcontent-%COMP%] {\\n    padding: 12px;\\n  }\\n  .deals-content[_ngcontent-%COMP%]   .deals-grid[_ngcontent-%COMP%]   .deal-item[_ngcontent-%COMP%]   .deal-info[_ngcontent-%COMP%]   .deal-title[_ngcontent-%COMP%] {\\n    font-size: 14px;\\n  }\\n  .deals-content[_ngcontent-%COMP%]   .deals-grid[_ngcontent-%COMP%]   .deal-item[_ngcontent-%COMP%]   .deal-info[_ngcontent-%COMP%]   .deal-description[_ngcontent-%COMP%] {\\n    font-size: 11px;\\n  }\\n  .deals-content[_ngcontent-%COMP%]   .deal-stats-summary[_ngcontent-%COMP%]   .stats-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: repeat(2, 1fr);\\n    gap: 12px;\\n    padding: 16px;\\n  }\\n  .deals-content[_ngcontent-%COMP%]   .deal-stats-summary[_ngcontent-%COMP%]   .stats-grid[_ngcontent-%COMP%]   .stat-item[_ngcontent-%COMP%] {\\n    padding: 10px;\\n  }\\n  .deals-content[_ngcontent-%COMP%]   .deal-stats-summary[_ngcontent-%COMP%]   .stats-grid[_ngcontent-%COMP%]   .stat-item[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n    font-size: 20px;\\n  }\\n  .deals-content[_ngcontent-%COMP%]   .deal-stats-summary[_ngcontent-%COMP%]   .stats-grid[_ngcontent-%COMP%]   .stat-item[_ngcontent-%COMP%]   .stat-info[_ngcontent-%COMP%]   .stat-value[_ngcontent-%COMP%] {\\n    font-size: 16px;\\n  }\\n  .deals-content[_ngcontent-%COMP%]   .deal-stats-summary[_ngcontent-%COMP%]   .stats-grid[_ngcontent-%COMP%]   .stat-item[_ngcontent-%COMP%]   .stat-info[_ngcontent-%COMP%]   .stat-label[_ngcontent-%COMP%] {\\n    font-size: 10px;\\n  }\\n}\\n@media (max-width: 425px) {\\n  .deals-content[_ngcontent-%COMP%]   .deals-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n  }\\n  .deals-content[_ngcontent-%COMP%]   .deals-grid[_ngcontent-%COMP%]   .deal-item[_ngcontent-%COMP%]   .deal-image-container[_ngcontent-%COMP%] {\\n    height: 120px;\\n  }\\n  .deals-content[_ngcontent-%COMP%]   .deal-categories[_ngcontent-%COMP%]   .category-btn[_ngcontent-%COMP%] {\\n    padding: 6px 12px;\\n    font-size: 11px;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "RouterModule", "IonicModule", "Subscription", "interval", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵtext", "ɵɵelementEnd", "ɵɵlistener", "TrendingDealsComponent_div_9_Template_button_click_4_listener", "ɵɵrestoreView", "_r1", "ctx_r1", "ɵɵnextContext", "ɵɵresetView", "onRetry", "ɵɵadvance", "ɵɵtextInterpolate", "error", "getTimeRemaining", "flashSale", "endTime", "hours", "minutes", "seconds", "TrendingDealsComponent_div_10_div_1_Template_div_click_0_listener", "_r3", "onDealClick", "ɵɵtemplate", "TrendingDealsComponent_div_10_div_1_div_17_Template", "title", "description", "ɵɵtextInterpolate1", "discountPercentage", "formatPrice", "originalPrice", "salePrice", "ɵɵproperty", "image", "ɵɵsanitizeUrl", "TrendingDealsComponent_div_10_button_3_Template_button_click_0_listener", "category_r5", "_r4", "$implicit", "selectCategory", "name", "ɵɵclassProp", "selectedCate<PERSON><PERSON>", "icon", "dealCount", "ɵɵtextInterpolate2", "deal_r7", "TrendingDealsComponent_div_10_div_5_Template_div_click_0_listener", "_r6", "TrendingDealsComponent_div_10_div_5_div_8_Template", "TrendingDealsComponent_div_10_div_5_Template_button_click_9_listener", "$event", "toggleFavorite", "TrendingDealsComponent_div_10_div_5_Template_button_click_30_listener", "claimDeal", "ɵɵclassMap", "type", "toUpperCase", "isDealFavorited", "_id", "soldCount", "rating", "dealStats", "activeDeals", "formatCount", "totalClaimed", "totalSavings", "avgDiscountPercentage", "TrendingDealsComponent_div_10_div_1_Template", "TrendingDealsComponent_div_10_button_3_Template", "TrendingDealsComponent_div_10_div_5_Template", "TrendingDealsComponent_div_10_div_6_Template", "dealCategories", "trackByCategoryName", "filteredDeals", "trackByDealId", "TrendingDealsComponent", "constructor", "router", "trendingDeals", "favoritedDeals", "Set", "isLoading", "subscription", "timerSubscription", "ngOnInit", "loadTrendingDeals", "startTimer", "ngOnDestroy", "unsubscribe", "_this", "_asyncToGenerator", "loadMockDeals", "console", "_this2", "Promise", "resolve", "setTimeout", "now", "Date", "endTime1", "getTime", "endTime2", "category", "isActive", "createdAt", "reduce", "prev", "current", "length", "filter", "d", "sum", "deal", "Math", "round", "filterDeals", "subscribe", "timeDiff", "floor", "navigate", "event", "stopPropagation", "log", "has", "delete", "add", "dealId", "price", "Intl", "NumberFormat", "style", "currency", "minimumFractionDigits", "format", "count", "toFixed", "toString", "index", "onSeeAll", "preventDefault", "ɵɵdirectiveInject", "i1", "Router", "selectors", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "TrendingDealsComponent_Template", "rf", "ctx", "TrendingDealsComponent_Template_a_click_5_listener", "TrendingDealsComponent_div_8_Template", "TrendingDealsComponent_div_9_Template", "TrendingDealsComponent_div_10_Template", "TrendingDealsComponent_div_11_Template", "i2", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "i3", "IonIcon", "Ion<PERSON><PERSON><PERSON>", "styles"], "sources": ["E:\\Fashion\\DFashionFrontend\\frontend\\src\\app\\features\\home\\components\\trending-now\\trending-deals\\trending-deals.component.ts", "E:\\Fashion\\DFashionFrontend\\frontend\\src\\app\\features\\home\\components\\trending-now\\trending-deals\\trending-deals.component.html"], "sourcesContent": ["import { Component, OnInit, OnDestroy } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\nimport { IonicModule } from '@ionic/angular';\nimport { Router } from '@angular/router';\nimport { Subscription, interval } from 'rxjs';\n\ninterface TrendingDeal {\n  _id: string;\n  title: string;\n  description: string;\n  image: string;\n  originalPrice: number;\n  salePrice: number;\n  discountPercentage: number;\n  type: 'flash' | 'daily' | 'weekly' | 'clearance';\n  category: string;\n  endTime?: Date;\n  soldCount: number;\n  rating: number;\n  isActive: boolean;\n  createdAt: Date;\n}\n\ninterface DealCategory {\n  name: string;\n  icon: string;\n  dealCount: number;\n}\n\ninterface DealStats {\n  activeDeals: number;\n  totalClaimed: number;\n  totalSavings: number;\n  avgDiscountPercentage: number;\n}\n\ninterface TimeRemaining {\n  hours: number;\n  minutes: number;\n  seconds: number;\n}\n\n@Component({\n  selector: 'app-trending-deals',\n  standalone: true,\n  imports: [\n    CommonModule,\n    RouterModule,\n    IonicModule\n  ],\n  templateUrl: './trending-deals.component.html',\n  styleUrls: ['./trending-deals.component.scss']\n})\nexport class TrendingDealsComponent implements OnInit, OnDestroy {\n  trendingDeals: TrendingDeal[] = [];\n  flashSale: TrendingDeal | null = null;\n  filteredDeals: TrendingDeal[] = [];\n  dealCategories: DealCategory[] = [];\n  dealStats: DealStats | null = null;\n  selectedCategory = 'All';\n  favoritedDeals = new Set<string>();\n  isLoading = true;\n  error: string | null = null;\n  private subscription: Subscription = new Subscription();\n  private timerSubscription: Subscription = new Subscription();\n\n  constructor(private router: Router) {}\n\n  ngOnInit() {\n    this.loadTrendingDeals();\n    this.startTimer();\n  }\n\n  ngOnDestroy() {\n    this.subscription.unsubscribe();\n    this.timerSubscription.unsubscribe();\n  }\n\n  private async loadTrendingDeals() {\n    try {\n      this.isLoading = true;\n      this.error = null;\n      \n      // Mock data for now - replace with actual service call\n      await this.loadMockDeals();\n      \n    } catch (error) {\n      console.error('Error loading trending deals:', error);\n      this.error = 'Failed to load trending deals';\n      this.isLoading = false;\n    }\n  }\n\n  private async loadMockDeals() {\n    // Simulate API call delay\n    await new Promise(resolve => setTimeout(resolve, 800));\n\n    const now = new Date();\n    const endTime1 = new Date(now.getTime() + 2 * 60 * 60 * 1000); // 2 hours from now\n    const endTime2 = new Date(now.getTime() + 6 * 60 * 60 * 1000); // 6 hours from now\n\n    this.trendingDeals = [\n      {\n        _id: 'deal1',\n        title: 'Summer Collection Flash Sale',\n        description: 'Limited time offer on summer essentials',\n        image: 'https://images.unsplash.com/photo-1515372039744-b8f02a3ae446?w=300&h=200&fit=crop',\n        originalPrice: 4999,\n        salePrice: 2499,\n        discountPercentage: 50,\n        type: 'flash',\n        category: 'Women',\n        endTime: endTime1,\n        soldCount: 234,\n        rating: 4.5,\n        isActive: true,\n        createdAt: new Date()\n      },\n      {\n        _id: 'deal2',\n        title: 'Designer Sneakers Deal',\n        description: 'Premium sneakers at unbeatable prices',\n        image: 'https://images.unsplash.com/photo-1542291026-7eec264c27ff?w=300&h=200&fit=crop',\n        originalPrice: 8999,\n        salePrice: 5999,\n        discountPercentage: 33,\n        type: 'daily',\n        category: 'Footwear',\n        endTime: endTime2,\n        soldCount: 156,\n        rating: 4.7,\n        isActive: true,\n        createdAt: new Date()\n      },\n      {\n        _id: 'deal3',\n        title: 'Handbag Clearance',\n        description: 'Stylish handbags at clearance prices',\n        image: 'https://images.unsplash.com/photo-1553062407-98eeb64c6a62?w=300&h=200&fit=crop',\n        originalPrice: 6999,\n        salePrice: 3499,\n        discountPercentage: 50,\n        type: 'clearance',\n        category: 'Accessories',\n        soldCount: 89,\n        rating: 4.3,\n        isActive: true,\n        createdAt: new Date()\n      },\n      {\n        _id: 'deal4',\n        title: 'Men\\'s Formal Wear',\n        description: 'Professional attire for the modern man',\n        image: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=300&h=200&fit=crop',\n        originalPrice: 12999,\n        salePrice: 8999,\n        discountPercentage: 31,\n        type: 'weekly',\n        category: 'Men',\n        soldCount: 67,\n        rating: 4.6,\n        isActive: true,\n        createdAt: new Date()\n      }\n    ];\n\n    // Set flash sale (highest discount percentage)\n    this.flashSale = this.trendingDeals.reduce((prev, current) => \n      (prev.discountPercentage > current.discountPercentage) ? prev : current\n    );\n\n    // Set up categories\n    this.dealCategories = [\n      { name: 'All', icon: 'apps-outline', dealCount: this.trendingDeals.length },\n      { name: 'Women', icon: 'woman-outline', dealCount: this.trendingDeals.filter(d => d.category === 'Women').length },\n      { name: 'Men', icon: 'man-outline', dealCount: this.trendingDeals.filter(d => d.category === 'Men').length },\n      { name: 'Footwear', icon: 'footsteps-outline', dealCount: this.trendingDeals.filter(d => d.category === 'Footwear').length },\n      { name: 'Accessories', icon: 'bag-outline', dealCount: this.trendingDeals.filter(d => d.category === 'Accessories').length }\n    ];\n\n    // Set up deal stats\n    this.dealStats = {\n      activeDeals: this.trendingDeals.filter(d => d.isActive).length,\n      totalClaimed: this.trendingDeals.reduce((sum, deal) => sum + deal.soldCount, 0),\n      totalSavings: this.trendingDeals.reduce((sum, deal) => sum + (deal.originalPrice - deal.salePrice) * deal.soldCount, 0),\n      avgDiscountPercentage: Math.round(this.trendingDeals.reduce((sum, deal) => sum + deal.discountPercentage, 0) / this.trendingDeals.length)\n    };\n\n    this.filterDeals();\n    this.isLoading = false;\n  }\n\n  private startTimer() {\n    this.timerSubscription = interval(1000).subscribe(() => {\n      // Timer updates every second for countdown\n    });\n  }\n\n  selectCategory(category: string) {\n    this.selectedCategory = category;\n    this.filterDeals();\n  }\n\n  private filterDeals() {\n    if (this.selectedCategory === 'All') {\n      this.filteredDeals = this.trendingDeals.filter(deal => deal._id !== this.flashSale?._id);\n    } else {\n      this.filteredDeals = this.trendingDeals.filter(deal => \n        deal.category === this.selectedCategory && deal._id !== this.flashSale?._id\n      );\n    }\n  }\n\n  getTimeRemaining(endTime: Date): TimeRemaining {\n    const now = new Date();\n    const timeDiff = endTime.getTime() - now.getTime();\n    \n    if (timeDiff <= 0) {\n      return { hours: 0, minutes: 0, seconds: 0 };\n    }\n\n    const hours = Math.floor(timeDiff / (1000 * 60 * 60));\n    const minutes = Math.floor((timeDiff % (1000 * 60 * 60)) / (1000 * 60));\n    const seconds = Math.floor((timeDiff % (1000 * 60)) / 1000);\n\n    return { hours, minutes, seconds };\n  }\n\n  onDealClick(deal: TrendingDeal) {\n    this.router.navigate(['/deal', deal._id]);\n  }\n\n  claimDeal(deal: TrendingDeal, event: Event) {\n    event.stopPropagation();\n    // Handle deal claiming logic\n    deal.soldCount++;\n    console.log('Deal claimed:', deal.title);\n  }\n\n  toggleFavorite(deal: TrendingDeal, event: Event) {\n    event.stopPropagation();\n    \n    if (this.favoritedDeals.has(deal._id)) {\n      this.favoritedDeals.delete(deal._id);\n    } else {\n      this.favoritedDeals.add(deal._id);\n    }\n  }\n\n  isDealFavorited(dealId: string): boolean {\n    return this.favoritedDeals.has(dealId);\n  }\n\n  formatPrice(price: number): string {\n    return new Intl.NumberFormat('en-IN', {\n      style: 'currency',\n      currency: 'INR',\n      minimumFractionDigits: 0\n    }).format(price);\n  }\n\n  formatCount(count: number): string {\n    if (count >= 1000000) {\n      return (count / 1000000).toFixed(1) + 'M';\n    } else if (count >= 1000) {\n      return (count / 1000).toFixed(1) + 'K';\n    }\n    return count.toString();\n  }\n\n  trackByDealId(index: number, deal: TrendingDeal): string {\n    return deal._id;\n  }\n\n  trackByCategoryName(index: number, category: DealCategory): string {\n    return category.name;\n  }\n\n  onSeeAll(event: Event) {\n    event.preventDefault();\n    this.router.navigate(['/trending/deals']);\n  }\n\n  onRetry() {\n    this.loadTrendingDeals();\n  }\n}\n", "<div class=\"component-container\">\n  <!-- Component Header -->\n  <div class=\"component-header\">\n    <h3>\n      <ion-icon name=\"flash-outline\"></ion-icon>\n      Trending Deals\n    </h3>\n    <a href=\"#\" class=\"see-all\" (click)=\"onSeeAll($event)\">See All</a>\n  </div>\n\n  <!-- Component Content -->\n  <div class=\"component-content\">\n    <!-- Loading State -->\n    <div *ngIf=\"isLoading\" class=\"loading-container\">\n      <ion-spinner name=\"crescent\"></ion-spinner>\n      <p>Loading trending deals...</p>\n    </div>\n\n    <!-- Error State -->\n    <div *ngIf=\"error && !isLoading\" class=\"error-container\">\n      <ion-icon name=\"alert-circle-outline\"></ion-icon>\n      <p>{{ error }}</p>\n      <button class=\"retry-btn\" (click)=\"onRetry()\">Try Again</button>\n    </div>\n\n    <!-- Deals Content -->\n    <div *ngIf=\"!isLoading && !error && trendingDeals.length > 0\" class=\"deals-content\">\n      <!-- Flash Sale Banner -->\n      <div class=\"flash-sale-banner\" *ngIf=\"flashSale\" (click)=\"onDealClick(flashSale)\">\n        <div class=\"banner-content\">\n          <div class=\"sale-info\">\n            <div class=\"sale-badge\">\n              <ion-icon name=\"flash\"></ion-icon>\n              Flash Sale\n            </div>\n            <h4 class=\"sale-title\">{{ flashSale.title }}</h4>\n            <p class=\"sale-description\">{{ flashSale.description }}</p>\n            <div class=\"discount-info\">\n              <span class=\"discount-percentage\">{{ flashSale.discountPercentage }}% OFF</span>\n              <span class=\"original-price\">{{ formatPrice(flashSale.originalPrice) }}</span>\n              <span class=\"sale-price\">{{ formatPrice(flashSale.salePrice) }}</span>\n            </div>\n          </div>\n          <div class=\"countdown-timer\" *ngIf=\"flashSale.endTime\">\n            <div class=\"timer-label\">Ends in:</div>\n            <div class=\"timer-display\">\n              <div class=\"time-unit\">\n                <span class=\"time-value\">{{ getTimeRemaining(flashSale.endTime).hours }}</span>\n                <span class=\"time-label\">H</span>\n              </div>\n              <div class=\"time-unit\">\n                <span class=\"time-value\">{{ getTimeRemaining(flashSale.endTime).minutes }}</span>\n                <span class=\"time-label\">M</span>\n              </div>\n              <div class=\"time-unit\">\n                <span class=\"time-value\">{{ getTimeRemaining(flashSale.endTime).seconds }}</span>\n                <span class=\"time-label\">S</span>\n              </div>\n            </div>\n          </div>\n        </div>\n        <div class=\"banner-image\">\n          <img [src]=\"flashSale.image\" [alt]=\"flashSale.title\" class=\"deal-image\">\n        </div>\n      </div>\n\n      <!-- Deal Categories -->\n      <div class=\"deal-categories\">\n        <button \n          *ngFor=\"let category of dealCategories; trackBy: trackByCategoryName\"\n          class=\"category-btn\"\n          [class.active]=\"selectedCategory === category.name\"\n          (click)=\"selectCategory(category.name)\"\n        >\n          <ion-icon [name]=\"category.icon\"></ion-icon>\n          {{ category.name }}\n          <span class=\"deal-count\">{{ category.dealCount }}</span>\n        </button>\n      </div>\n\n      <!-- Deals Grid -->\n      <div class=\"deals-grid\">\n        <div \n          *ngFor=\"let deal of filteredDeals; trackBy: trackByDealId\" \n          class=\"deal-item\"\n          (click)=\"onDealClick(deal)\"\n        >\n          <div class=\"deal-image-container\">\n            <img [src]=\"deal.image\" [alt]=\"deal.title\" class=\"deal-image\">\n            <div class=\"deal-badges\">\n              <div class=\"discount-badge\">{{ deal.discountPercentage }}% OFF</div>\n              <div class=\"deal-type-badge\" [class]=\"deal.type\">{{ deal.type.toUpperCase() }}</div>\n            </div>\n            <div class=\"deal-timer\" *ngIf=\"deal.endTime\">\n              <ion-icon name=\"time-outline\"></ion-icon>\n              <span>{{ getTimeRemaining(deal.endTime).hours }}h {{ getTimeRemaining(deal.endTime).minutes }}m</span>\n            </div>\n            <button class=\"favorite-btn\" [class.active]=\"isDealFavorited(deal._id)\" (click)=\"toggleFavorite(deal, $event)\">\n              <ion-icon [name]=\"isDealFavorited(deal._id) ? 'heart' : 'heart-outline'\"></ion-icon>\n            </button>\n          </div>\n          <div class=\"deal-info\">\n            <h5 class=\"deal-title\">{{ deal.title }}</h5>\n            <p class=\"deal-description\">{{ deal.description }}</p>\n            <div class=\"price-info\">\n              <span class=\"original-price\">{{ formatPrice(deal.originalPrice) }}</span>\n              <span class=\"sale-price\">{{ formatPrice(deal.salePrice) }}</span>\n              <span class=\"savings\">Save {{ formatPrice(deal.originalPrice - deal.salePrice) }}</span>\n            </div>\n            <div class=\"deal-meta\">\n              <div class=\"deal-stats\">\n                <span class=\"sold-count\">{{ deal.soldCount }} sold</span>\n                <span class=\"rating\">\n                  <ion-icon name=\"star\"></ion-icon>\n                  {{ deal.rating }}\n                </span>\n              </div>\n              <button class=\"claim-btn\" (click)=\"claimDeal(deal, $event)\">\n                Claim Deal\n              </button>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <!-- Deal Stats -->\n      <div class=\"deal-stats-summary\" *ngIf=\"dealStats\">\n        <div class=\"stats-grid\">\n          <div class=\"stat-item\">\n            <ion-icon name=\"flash-outline\"></ion-icon>\n            <div class=\"stat-info\">\n              <span class=\"stat-value\">{{ dealStats.activeDeals }}</span>\n              <span class=\"stat-label\">Active Deals</span>\n            </div>\n          </div>\n          <div class=\"stat-item\">\n            <ion-icon name=\"people-outline\"></ion-icon>\n            <div class=\"stat-info\">\n              <span class=\"stat-value\">{{ formatCount(dealStats.totalClaimed) }}</span>\n              <span class=\"stat-label\">Total Claimed</span>\n            </div>\n          </div>\n          <div class=\"stat-item\">\n            <ion-icon name=\"cash-outline\"></ion-icon>\n            <div class=\"stat-info\">\n              <span class=\"stat-value\">{{ formatPrice(dealStats.totalSavings) }}</span>\n              <span class=\"stat-label\">Total Savings</span>\n            </div>\n          </div>\n          <div class=\"stat-item\">\n            <ion-icon name=\"time-outline\"></ion-icon>\n            <div class=\"stat-info\">\n              <span class=\"stat-value\">{{ dealStats.avgDiscountPercentage }}%</span>\n              <span class=\"stat-label\">Avg Discount</span>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- Empty State -->\n    <div *ngIf=\"!isLoading && !error && trendingDeals.length === 0\" class=\"empty-container\">\n      <ion-icon name=\"flash-outline\"></ion-icon>\n      <p>No trending deals available</p>\n    </div>\n  </div>\n</div>\n"], "mappings": ";AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,QAAQ,gBAAgB;AAE5C,SAASC,YAAY,EAAEC,QAAQ,QAAQ,MAAM;;;;;;;ICQzCC,EAAA,CAAAC,cAAA,aAAiD;IAC/CD,EAAA,CAAAE,SAAA,sBAA2C;IAC3CF,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAG,MAAA,gCAAyB;IAC9BH,EAD8B,CAAAI,YAAA,EAAI,EAC5B;;;;;;IAGNJ,EAAA,CAAAC,cAAA,cAAyD;IACvDD,EAAA,CAAAE,SAAA,mBAAiD;IACjDF,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAG,MAAA,GAAW;IAAAH,EAAA,CAAAI,YAAA,EAAI;IAClBJ,EAAA,CAAAC,cAAA,iBAA8C;IAApBD,EAAA,CAAAK,UAAA,mBAAAC,8DAAA;MAAAN,EAAA,CAAAO,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAG,OAAA,EAAS;IAAA,EAAC;IAACZ,EAAA,CAAAG,MAAA,gBAAS;IACzDH,EADyD,CAAAI,YAAA,EAAS,EAC5D;;;;IAFDJ,EAAA,CAAAa,SAAA,GAAW;IAAXb,EAAA,CAAAc,iBAAA,CAAAL,MAAA,CAAAM,KAAA,CAAW;;;;;IAuBRf,EADF,CAAAC,cAAA,cAAuD,cAC5B;IAAAD,EAAA,CAAAG,MAAA,eAAQ;IAAAH,EAAA,CAAAI,YAAA,EAAM;IAGnCJ,EAFJ,CAAAC,cAAA,cAA2B,cACF,eACI;IAAAD,EAAA,CAAAG,MAAA,GAA+C;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAC/EJ,EAAA,CAAAC,cAAA,eAAyB;IAAAD,EAAA,CAAAG,MAAA,QAAC;IAC5BH,EAD4B,CAAAI,YAAA,EAAO,EAC7B;IAEJJ,EADF,CAAAC,cAAA,cAAuB,gBACI;IAAAD,EAAA,CAAAG,MAAA,IAAiD;IAAAH,EAAA,CAAAI,YAAA,EAAO;IACjFJ,EAAA,CAAAC,cAAA,gBAAyB;IAAAD,EAAA,CAAAG,MAAA,SAAC;IAC5BH,EAD4B,CAAAI,YAAA,EAAO,EAC7B;IAEJJ,EADF,CAAAC,cAAA,eAAuB,gBACI;IAAAD,EAAA,CAAAG,MAAA,IAAiD;IAAAH,EAAA,CAAAI,YAAA,EAAO;IACjFJ,EAAA,CAAAC,cAAA,gBAAyB;IAAAD,EAAA,CAAAG,MAAA,SAAC;IAGhCH,EAHgC,CAAAI,YAAA,EAAO,EAC7B,EACF,EACF;;;;IAZyBJ,EAAA,CAAAa,SAAA,GAA+C;IAA/Cb,EAAA,CAAAc,iBAAA,CAAAL,MAAA,CAAAO,gBAAA,CAAAP,MAAA,CAAAQ,SAAA,CAAAC,OAAA,EAAAC,KAAA,CAA+C;IAI/CnB,EAAA,CAAAa,SAAA,GAAiD;IAAjDb,EAAA,CAAAc,iBAAA,CAAAL,MAAA,CAAAO,gBAAA,CAAAP,MAAA,CAAAQ,SAAA,CAAAC,OAAA,EAAAE,OAAA,CAAiD;IAIjDpB,EAAA,CAAAa,SAAA,GAAiD;IAAjDb,EAAA,CAAAc,iBAAA,CAAAL,MAAA,CAAAO,gBAAA,CAAAP,MAAA,CAAAQ,SAAA,CAAAC,OAAA,EAAAG,OAAA,CAAiD;;;;;;IA3BpFrB,EAAA,CAAAC,cAAA,cAAkF;IAAjCD,EAAA,CAAAK,UAAA,mBAAAiB,kEAAA;MAAAtB,EAAA,CAAAO,aAAA,CAAAgB,GAAA;MAAA,MAAAd,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAe,WAAA,CAAAf,MAAA,CAAAQ,SAAA,CAAsB;IAAA,EAAC;IAG3EjB,EAFJ,CAAAC,cAAA,cAA4B,cACH,cACG;IACtBD,EAAA,CAAAE,SAAA,mBAAkC;IAClCF,EAAA,CAAAG,MAAA,mBACF;IAAAH,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAC,cAAA,aAAuB;IAAAD,EAAA,CAAAG,MAAA,GAAqB;IAAAH,EAAA,CAAAI,YAAA,EAAK;IACjDJ,EAAA,CAAAC,cAAA,YAA4B;IAAAD,EAAA,CAAAG,MAAA,GAA2B;IAAAH,EAAA,CAAAI,YAAA,EAAI;IAEzDJ,EADF,CAAAC,cAAA,eAA2B,gBACS;IAAAD,EAAA,CAAAG,MAAA,IAAuC;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAChFJ,EAAA,CAAAC,cAAA,gBAA6B;IAAAD,EAAA,CAAAG,MAAA,IAA0C;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAC9EJ,EAAA,CAAAC,cAAA,gBAAyB;IAAAD,EAAA,CAAAG,MAAA,IAAsC;IAEnEH,EAFmE,CAAAI,YAAA,EAAO,EAClE,EACF;IACNJ,EAAA,CAAAyB,UAAA,KAAAC,mDAAA,mBAAuD;IAiBzD1B,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAC,cAAA,eAA0B;IACxBD,EAAA,CAAAE,SAAA,eAAwE;IAE5EF,EADE,CAAAI,YAAA,EAAM,EACF;;;;IA7BuBJ,EAAA,CAAAa,SAAA,GAAqB;IAArBb,EAAA,CAAAc,iBAAA,CAAAL,MAAA,CAAAQ,SAAA,CAAAU,KAAA,CAAqB;IAChB3B,EAAA,CAAAa,SAAA,GAA2B;IAA3Bb,EAAA,CAAAc,iBAAA,CAAAL,MAAA,CAAAQ,SAAA,CAAAW,WAAA,CAA2B;IAEnB5B,EAAA,CAAAa,SAAA,GAAuC;IAAvCb,EAAA,CAAA6B,kBAAA,KAAApB,MAAA,CAAAQ,SAAA,CAAAa,kBAAA,UAAuC;IAC5C9B,EAAA,CAAAa,SAAA,GAA0C;IAA1Cb,EAAA,CAAAc,iBAAA,CAAAL,MAAA,CAAAsB,WAAA,CAAAtB,MAAA,CAAAQ,SAAA,CAAAe,aAAA,EAA0C;IAC9ChC,EAAA,CAAAa,SAAA,GAAsC;IAAtCb,EAAA,CAAAc,iBAAA,CAAAL,MAAA,CAAAsB,WAAA,CAAAtB,MAAA,CAAAQ,SAAA,CAAAgB,SAAA,EAAsC;IAGrCjC,EAAA,CAAAa,SAAA,EAAuB;IAAvBb,EAAA,CAAAkC,UAAA,SAAAzB,MAAA,CAAAQ,SAAA,CAAAC,OAAA,CAAuB;IAmBhDlB,EAAA,CAAAa,SAAA,GAAuB;IAACb,EAAxB,CAAAkC,UAAA,QAAAzB,MAAA,CAAAQ,SAAA,CAAAkB,KAAA,EAAAnC,EAAA,CAAAoC,aAAA,CAAuB,QAAA3B,MAAA,CAAAQ,SAAA,CAAAU,KAAA,CAAwB;;;;;;IAMtD3B,EAAA,CAAAC,cAAA,iBAKC;IADCD,EAAA,CAAAK,UAAA,mBAAAgC,wEAAA;MAAA,MAAAC,WAAA,GAAAtC,EAAA,CAAAO,aAAA,CAAAgC,GAAA,EAAAC,SAAA;MAAA,MAAA/B,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAgC,cAAA,CAAAH,WAAA,CAAAI,IAAA,CAA6B;IAAA,EAAC;IAEvC1C,EAAA,CAAAE,SAAA,mBAA4C;IAC5CF,EAAA,CAAAG,MAAA,GACA;IAAAH,EAAA,CAAAC,cAAA,eAAyB;IAAAD,EAAA,CAAAG,MAAA,GAAwB;IACnDH,EADmD,CAAAI,YAAA,EAAO,EACjD;;;;;IANPJ,EAAA,CAAA2C,WAAA,WAAAlC,MAAA,CAAAmC,gBAAA,KAAAN,WAAA,CAAAI,IAAA,CAAmD;IAGzC1C,EAAA,CAAAa,SAAA,EAAsB;IAAtBb,EAAA,CAAAkC,UAAA,SAAAI,WAAA,CAAAO,IAAA,CAAsB;IAChC7C,EAAA,CAAAa,SAAA,EACA;IADAb,EAAA,CAAA6B,kBAAA,MAAAS,WAAA,CAAAI,IAAA,MACA;IAAyB1C,EAAA,CAAAa,SAAA,GAAwB;IAAxBb,EAAA,CAAAc,iBAAA,CAAAwB,WAAA,CAAAQ,SAAA,CAAwB;;;;;IAiB/C9C,EAAA,CAAAC,cAAA,cAA6C;IAC3CD,EAAA,CAAAE,SAAA,mBAAyC;IACzCF,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAG,MAAA,GAAyF;IACjGH,EADiG,CAAAI,YAAA,EAAO,EAClG;;;;;IADEJ,EAAA,CAAAa,SAAA,GAAyF;IAAzFb,EAAA,CAAA+C,kBAAA,KAAAtC,MAAA,CAAAO,gBAAA,CAAAgC,OAAA,CAAA9B,OAAA,EAAAC,KAAA,QAAAV,MAAA,CAAAO,gBAAA,CAAAgC,OAAA,CAAA9B,OAAA,EAAAE,OAAA,MAAyF;;;;;;IAbrGpB,EAAA,CAAAC,cAAA,cAIC;IADCD,EAAA,CAAAK,UAAA,mBAAA4C,kEAAA;MAAA,MAAAD,OAAA,GAAAhD,EAAA,CAAAO,aAAA,CAAA2C,GAAA,EAAAV,SAAA;MAAA,MAAA/B,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAe,WAAA,CAAAwB,OAAA,CAAiB;IAAA,EAAC;IAE3BhD,EAAA,CAAAC,cAAA,cAAkC;IAChCD,EAAA,CAAAE,SAAA,cAA8D;IAE5DF,EADF,CAAAC,cAAA,cAAyB,cACK;IAAAD,EAAA,CAAAG,MAAA,GAAkC;IAAAH,EAAA,CAAAI,YAAA,EAAM;IACpEJ,EAAA,CAAAC,cAAA,cAAiD;IAAAD,EAAA,CAAAG,MAAA,GAA6B;IAChFH,EADgF,CAAAI,YAAA,EAAM,EAChF;IACNJ,EAAA,CAAAyB,UAAA,IAAA0B,kDAAA,kBAA6C;IAI7CnD,EAAA,CAAAC,cAAA,iBAA+G;IAAvCD,EAAA,CAAAK,UAAA,mBAAA+C,qEAAAC,MAAA;MAAA,MAAAL,OAAA,GAAAhD,EAAA,CAAAO,aAAA,CAAA2C,GAAA,EAAAV,SAAA;MAAA,MAAA/B,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAA6C,cAAA,CAAAN,OAAA,EAAAK,MAAA,CAA4B;IAAA,EAAC;IAC5GrD,EAAA,CAAAE,SAAA,oBAAoF;IAExFF,EADE,CAAAI,YAAA,EAAS,EACL;IAEJJ,EADF,CAAAC,cAAA,eAAuB,cACE;IAAAD,EAAA,CAAAG,MAAA,IAAgB;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAC5CJ,EAAA,CAAAC,cAAA,aAA4B;IAAAD,EAAA,CAAAG,MAAA,IAAsB;IAAAH,EAAA,CAAAI,YAAA,EAAI;IAEpDJ,EADF,CAAAC,cAAA,eAAwB,gBACO;IAAAD,EAAA,CAAAG,MAAA,IAAqC;IAAAH,EAAA,CAAAI,YAAA,EAAO;IACzEJ,EAAA,CAAAC,cAAA,gBAAyB;IAAAD,EAAA,CAAAG,MAAA,IAAiC;IAAAH,EAAA,CAAAI,YAAA,EAAO;IACjEJ,EAAA,CAAAC,cAAA,gBAAsB;IAAAD,EAAA,CAAAG,MAAA,IAA2D;IACnFH,EADmF,CAAAI,YAAA,EAAO,EACpF;IAGFJ,EAFJ,CAAAC,cAAA,eAAuB,eACG,gBACG;IAAAD,EAAA,CAAAG,MAAA,IAAyB;IAAAH,EAAA,CAAAI,YAAA,EAAO;IACzDJ,EAAA,CAAAC,cAAA,gBAAqB;IACnBD,EAAA,CAAAE,SAAA,oBAAiC;IACjCF,EAAA,CAAAG,MAAA,IACF;IACFH,EADE,CAAAI,YAAA,EAAO,EACH;IACNJ,EAAA,CAAAC,cAAA,kBAA4D;IAAlCD,EAAA,CAAAK,UAAA,mBAAAkD,sEAAAF,MAAA;MAAA,MAAAL,OAAA,GAAAhD,EAAA,CAAAO,aAAA,CAAA2C,GAAA,EAAAV,SAAA;MAAA,MAAA/B,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAA+C,SAAA,CAAAR,OAAA,EAAAK,MAAA,CAAuB;IAAA,EAAC;IACzDrD,EAAA,CAAAG,MAAA,oBACF;IAGNH,EAHM,CAAAI,YAAA,EAAS,EACL,EACF,EACF;;;;;IAlCGJ,EAAA,CAAAa,SAAA,GAAkB;IAACb,EAAnB,CAAAkC,UAAA,QAAAc,OAAA,CAAAb,KAAA,EAAAnC,EAAA,CAAAoC,aAAA,CAAkB,QAAAY,OAAA,CAAArB,KAAA,CAAmB;IAEZ3B,EAAA,CAAAa,SAAA,GAAkC;IAAlCb,EAAA,CAAA6B,kBAAA,KAAAmB,OAAA,CAAAlB,kBAAA,UAAkC;IACjC9B,EAAA,CAAAa,SAAA,EAAmB;IAAnBb,EAAA,CAAAyD,UAAA,CAAAT,OAAA,CAAAU,IAAA,CAAmB;IAAC1D,EAAA,CAAAa,SAAA,EAA6B;IAA7Bb,EAAA,CAAAc,iBAAA,CAAAkC,OAAA,CAAAU,IAAA,CAAAC,WAAA,GAA6B;IAEvD3D,EAAA,CAAAa,SAAA,EAAkB;IAAlBb,EAAA,CAAAkC,UAAA,SAAAc,OAAA,CAAA9B,OAAA,CAAkB;IAIdlB,EAAA,CAAAa,SAAA,EAA0C;IAA1Cb,EAAA,CAAA2C,WAAA,WAAAlC,MAAA,CAAAmD,eAAA,CAAAZ,OAAA,CAAAa,GAAA,EAA0C;IAC3D7D,EAAA,CAAAa,SAAA,EAA8D;IAA9Db,EAAA,CAAAkC,UAAA,SAAAzB,MAAA,CAAAmD,eAAA,CAAAZ,OAAA,CAAAa,GAAA,8BAA8D;IAInD7D,EAAA,CAAAa,SAAA,GAAgB;IAAhBb,EAAA,CAAAc,iBAAA,CAAAkC,OAAA,CAAArB,KAAA,CAAgB;IACX3B,EAAA,CAAAa,SAAA,GAAsB;IAAtBb,EAAA,CAAAc,iBAAA,CAAAkC,OAAA,CAAApB,WAAA,CAAsB;IAEnB5B,EAAA,CAAAa,SAAA,GAAqC;IAArCb,EAAA,CAAAc,iBAAA,CAAAL,MAAA,CAAAsB,WAAA,CAAAiB,OAAA,CAAAhB,aAAA,EAAqC;IACzChC,EAAA,CAAAa,SAAA,GAAiC;IAAjCb,EAAA,CAAAc,iBAAA,CAAAL,MAAA,CAAAsB,WAAA,CAAAiB,OAAA,CAAAf,SAAA,EAAiC;IACpCjC,EAAA,CAAAa,SAAA,GAA2D;IAA3Db,EAAA,CAAA6B,kBAAA,UAAApB,MAAA,CAAAsB,WAAA,CAAAiB,OAAA,CAAAhB,aAAA,GAAAgB,OAAA,CAAAf,SAAA,MAA2D;IAItDjC,EAAA,CAAAa,SAAA,GAAyB;IAAzBb,EAAA,CAAA6B,kBAAA,KAAAmB,OAAA,CAAAc,SAAA,UAAyB;IAGhD9D,EAAA,CAAAa,SAAA,GACF;IADEb,EAAA,CAAA6B,kBAAA,MAAAmB,OAAA,CAAAe,MAAA,MACF;;;;;IAaN/D,EAFJ,CAAAC,cAAA,cAAkD,cACxB,cACC;IACrBD,EAAA,CAAAE,SAAA,kBAA0C;IAExCF,EADF,CAAAC,cAAA,cAAuB,eACI;IAAAD,EAAA,CAAAG,MAAA,GAA2B;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAC3DJ,EAAA,CAAAC,cAAA,eAAyB;IAAAD,EAAA,CAAAG,MAAA,mBAAY;IAEzCH,EAFyC,CAAAI,YAAA,EAAO,EACxC,EACF;IACNJ,EAAA,CAAAC,cAAA,cAAuB;IACrBD,EAAA,CAAAE,SAAA,oBAA2C;IAEzCF,EADF,CAAAC,cAAA,eAAuB,gBACI;IAAAD,EAAA,CAAAG,MAAA,IAAyC;IAAAH,EAAA,CAAAI,YAAA,EAAO;IACzEJ,EAAA,CAAAC,cAAA,gBAAyB;IAAAD,EAAA,CAAAG,MAAA,qBAAa;IAE1CH,EAF0C,CAAAI,YAAA,EAAO,EACzC,EACF;IACNJ,EAAA,CAAAC,cAAA,eAAuB;IACrBD,EAAA,CAAAE,SAAA,oBAAyC;IAEvCF,EADF,CAAAC,cAAA,eAAuB,gBACI;IAAAD,EAAA,CAAAG,MAAA,IAAyC;IAAAH,EAAA,CAAAI,YAAA,EAAO;IACzEJ,EAAA,CAAAC,cAAA,gBAAyB;IAAAD,EAAA,CAAAG,MAAA,qBAAa;IAE1CH,EAF0C,CAAAI,YAAA,EAAO,EACzC,EACF;IACNJ,EAAA,CAAAC,cAAA,eAAuB;IACrBD,EAAA,CAAAE,SAAA,oBAAyC;IAEvCF,EADF,CAAAC,cAAA,eAAuB,gBACI;IAAAD,EAAA,CAAAG,MAAA,IAAsC;IAAAH,EAAA,CAAAI,YAAA,EAAO;IACtEJ,EAAA,CAAAC,cAAA,gBAAyB;IAAAD,EAAA,CAAAG,MAAA,oBAAY;IAI7CH,EAJ6C,CAAAI,YAAA,EAAO,EACxC,EACF,EACF,EACF;;;;IA1B2BJ,EAAA,CAAAa,SAAA,GAA2B;IAA3Bb,EAAA,CAAAc,iBAAA,CAAAL,MAAA,CAAAuD,SAAA,CAAAC,WAAA,CAA2B;IAO3BjE,EAAA,CAAAa,SAAA,GAAyC;IAAzCb,EAAA,CAAAc,iBAAA,CAAAL,MAAA,CAAAyD,WAAA,CAAAzD,MAAA,CAAAuD,SAAA,CAAAG,YAAA,EAAyC;IAOzCnE,EAAA,CAAAa,SAAA,GAAyC;IAAzCb,EAAA,CAAAc,iBAAA,CAAAL,MAAA,CAAAsB,WAAA,CAAAtB,MAAA,CAAAuD,SAAA,CAAAI,YAAA,EAAyC;IAOzCpE,EAAA,CAAAa,SAAA,GAAsC;IAAtCb,EAAA,CAAA6B,kBAAA,KAAApB,MAAA,CAAAuD,SAAA,CAAAK,qBAAA,MAAsC;;;;;IA9HzErE,EAAA,CAAAC,cAAA,cAAoF;IAElFD,EAAA,CAAAyB,UAAA,IAAA6C,4CAAA,mBAAkF;IAuClFtE,EAAA,CAAAC,cAAA,cAA6B;IAC3BD,EAAA,CAAAyB,UAAA,IAAA8C,+CAAA,qBAKC;IAKHvE,EAAA,CAAAI,YAAA,EAAM;IAGNJ,EAAA,CAAAC,cAAA,cAAwB;IACtBD,EAAA,CAAAyB,UAAA,IAAA+C,4CAAA,oBAIC;IAqCHxE,EAAA,CAAAI,YAAA,EAAM;IAGNJ,EAAA,CAAAyB,UAAA,IAAAgD,4CAAA,mBAAkD;IAgCpDzE,EAAA,CAAAI,YAAA,EAAM;;;;IAlI4BJ,EAAA,CAAAa,SAAA,EAAe;IAAfb,EAAA,CAAAkC,UAAA,SAAAzB,MAAA,CAAAQ,SAAA,CAAe;IAyCtBjB,EAAA,CAAAa,SAAA,GAAmB;IAAAb,EAAnB,CAAAkC,UAAA,YAAAzB,MAAA,CAAAiE,cAAA,CAAmB,iBAAAjE,MAAA,CAAAkE,mBAAA,CAA4B;IAcnD3E,EAAA,CAAAa,SAAA,GAAkB;IAAAb,EAAlB,CAAAkC,UAAA,YAAAzB,MAAA,CAAAmE,aAAA,CAAkB,iBAAAnE,MAAA,CAAAoE,aAAA,CAAsB;IA2C5B7E,EAAA,CAAAa,SAAA,EAAe;IAAfb,EAAA,CAAAkC,UAAA,SAAAzB,MAAA,CAAAuD,SAAA,CAAe;;;;;IAmClDhE,EAAA,CAAAC,cAAA,cAAwF;IACtFD,EAAA,CAAAE,SAAA,kBAA0C;IAC1CF,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAG,MAAA,kCAA2B;IAChCH,EADgC,CAAAI,YAAA,EAAI,EAC9B;;;AD9GV,OAAM,MAAO0E,sBAAsB;EAajCC,YAAoBC,MAAc;IAAd,KAAAA,MAAM,GAANA,MAAM;IAZ1B,KAAAC,aAAa,GAAmB,EAAE;IAClC,KAAAhE,SAAS,GAAwB,IAAI;IACrC,KAAA2D,aAAa,GAAmB,EAAE;IAClC,KAAAF,cAAc,GAAmB,EAAE;IACnC,KAAAV,SAAS,GAAqB,IAAI;IAClC,KAAApB,gBAAgB,GAAG,KAAK;IACxB,KAAAsC,cAAc,GAAG,IAAIC,GAAG,EAAU;IAClC,KAAAC,SAAS,GAAG,IAAI;IAChB,KAAArE,KAAK,GAAkB,IAAI;IACnB,KAAAsE,YAAY,GAAiB,IAAIvF,YAAY,EAAE;IAC/C,KAAAwF,iBAAiB,GAAiB,IAAIxF,YAAY,EAAE;EAEvB;EAErCyF,QAAQA,CAAA;IACN,IAAI,CAACC,iBAAiB,EAAE;IACxB,IAAI,CAACC,UAAU,EAAE;EACnB;EAEAC,WAAWA,CAAA;IACT,IAAI,CAACL,YAAY,CAACM,WAAW,EAAE;IAC/B,IAAI,CAACL,iBAAiB,CAACK,WAAW,EAAE;EACtC;EAEcH,iBAAiBA,CAAA;IAAA,IAAAI,KAAA;IAAA,OAAAC,iBAAA;MAC7B,IAAI;QACFD,KAAI,CAACR,SAAS,GAAG,IAAI;QACrBQ,KAAI,CAAC7E,KAAK,GAAG,IAAI;QAEjB;QACA,MAAM6E,KAAI,CAACE,aAAa,EAAE;OAE3B,CAAC,OAAO/E,KAAK,EAAE;QACdgF,OAAO,CAAChF,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;QACrD6E,KAAI,CAAC7E,KAAK,GAAG,+BAA+B;QAC5C6E,KAAI,CAACR,SAAS,GAAG,KAAK;;IACvB;EACH;EAEcU,aAAaA,CAAA;IAAA,IAAAE,MAAA;IAAA,OAAAH,iBAAA;MACzB;MACA,MAAM,IAAII,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,GAAG,CAAC,CAAC;MAEtD,MAAME,GAAG,GAAG,IAAIC,IAAI,EAAE;MACtB,MAAMC,QAAQ,GAAG,IAAID,IAAI,CAACD,GAAG,CAACG,OAAO,EAAE,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC;MAC/D,MAAMC,QAAQ,GAAG,IAAIH,IAAI,CAACD,GAAG,CAACG,OAAO,EAAE,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC;MAE/DP,MAAI,CAACf,aAAa,GAAG,CACnB;QACEpB,GAAG,EAAE,OAAO;QACZlC,KAAK,EAAE,8BAA8B;QACrCC,WAAW,EAAE,yCAAyC;QACtDO,KAAK,EAAE,mFAAmF;QAC1FH,aAAa,EAAE,IAAI;QACnBC,SAAS,EAAE,IAAI;QACfH,kBAAkB,EAAE,EAAE;QACtB4B,IAAI,EAAE,OAAO;QACb+C,QAAQ,EAAE,OAAO;QACjBvF,OAAO,EAAEoF,QAAQ;QACjBxC,SAAS,EAAE,GAAG;QACdC,MAAM,EAAE,GAAG;QACX2C,QAAQ,EAAE,IAAI;QACdC,SAAS,EAAE,IAAIN,IAAI;OACpB,EACD;QACExC,GAAG,EAAE,OAAO;QACZlC,KAAK,EAAE,wBAAwB;QAC/BC,WAAW,EAAE,uCAAuC;QACpDO,KAAK,EAAE,gFAAgF;QACvFH,aAAa,EAAE,IAAI;QACnBC,SAAS,EAAE,IAAI;QACfH,kBAAkB,EAAE,EAAE;QACtB4B,IAAI,EAAE,OAAO;QACb+C,QAAQ,EAAE,UAAU;QACpBvF,OAAO,EAAEsF,QAAQ;QACjB1C,SAAS,EAAE,GAAG;QACdC,MAAM,EAAE,GAAG;QACX2C,QAAQ,EAAE,IAAI;QACdC,SAAS,EAAE,IAAIN,IAAI;OACpB,EACD;QACExC,GAAG,EAAE,OAAO;QACZlC,KAAK,EAAE,mBAAmB;QAC1BC,WAAW,EAAE,sCAAsC;QACnDO,KAAK,EAAE,gFAAgF;QACvFH,aAAa,EAAE,IAAI;QACnBC,SAAS,EAAE,IAAI;QACfH,kBAAkB,EAAE,EAAE;QACtB4B,IAAI,EAAE,WAAW;QACjB+C,QAAQ,EAAE,aAAa;QACvB3C,SAAS,EAAE,EAAE;QACbC,MAAM,EAAE,GAAG;QACX2C,QAAQ,EAAE,IAAI;QACdC,SAAS,EAAE,IAAIN,IAAI;OACpB,EACD;QACExC,GAAG,EAAE,OAAO;QACZlC,KAAK,EAAE,oBAAoB;QAC3BC,WAAW,EAAE,wCAAwC;QACrDO,KAAK,EAAE,mFAAmF;QAC1FH,aAAa,EAAE,KAAK;QACpBC,SAAS,EAAE,IAAI;QACfH,kBAAkB,EAAE,EAAE;QACtB4B,IAAI,EAAE,QAAQ;QACd+C,QAAQ,EAAE,KAAK;QACf3C,SAAS,EAAE,EAAE;QACbC,MAAM,EAAE,GAAG;QACX2C,QAAQ,EAAE,IAAI;QACdC,SAAS,EAAE,IAAIN,IAAI;OACpB,CACF;MAED;MACAL,MAAI,CAAC/E,SAAS,GAAG+E,MAAI,CAACf,aAAa,CAAC2B,MAAM,CAAC,CAACC,IAAI,EAAEC,OAAO,KACtDD,IAAI,CAAC/E,kBAAkB,GAAGgF,OAAO,CAAChF,kBAAkB,GAAI+E,IAAI,GAAGC,OAAO,CACxE;MAED;MACAd,MAAI,CAACtB,cAAc,GAAG,CACpB;QAAEhC,IAAI,EAAE,KAAK;QAAEG,IAAI,EAAE,cAAc;QAAEC,SAAS,EAAEkD,MAAI,CAACf,aAAa,CAAC8B;MAAM,CAAE,EAC3E;QAAErE,IAAI,EAAE,OAAO;QAAEG,IAAI,EAAE,eAAe;QAAEC,SAAS,EAAEkD,MAAI,CAACf,aAAa,CAAC+B,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACR,QAAQ,KAAK,OAAO,CAAC,CAACM;MAAM,CAAE,EAClH;QAAErE,IAAI,EAAE,KAAK;QAAEG,IAAI,EAAE,aAAa;QAAEC,SAAS,EAAEkD,MAAI,CAACf,aAAa,CAAC+B,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACR,QAAQ,KAAK,KAAK,CAAC,CAACM;MAAM,CAAE,EAC5G;QAAErE,IAAI,EAAE,UAAU;QAAEG,IAAI,EAAE,mBAAmB;QAAEC,SAAS,EAAEkD,MAAI,CAACf,aAAa,CAAC+B,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACR,QAAQ,KAAK,UAAU,CAAC,CAACM;MAAM,CAAE,EAC5H;QAAErE,IAAI,EAAE,aAAa;QAAEG,IAAI,EAAE,aAAa;QAAEC,SAAS,EAAEkD,MAAI,CAACf,aAAa,CAAC+B,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACR,QAAQ,KAAK,aAAa,CAAC,CAACM;MAAM,CAAE,CAC7H;MAED;MACAf,MAAI,CAAChC,SAAS,GAAG;QACfC,WAAW,EAAE+B,MAAI,CAACf,aAAa,CAAC+B,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACP,QAAQ,CAAC,CAACK,MAAM;QAC9D5C,YAAY,EAAE6B,MAAI,CAACf,aAAa,CAAC2B,MAAM,CAAC,CAACM,GAAG,EAAEC,IAAI,KAAKD,GAAG,GAAGC,IAAI,CAACrD,SAAS,EAAE,CAAC,CAAC;QAC/EM,YAAY,EAAE4B,MAAI,CAACf,aAAa,CAAC2B,MAAM,CAAC,CAACM,GAAG,EAAEC,IAAI,KAAKD,GAAG,GAAG,CAACC,IAAI,CAACnF,aAAa,GAAGmF,IAAI,CAAClF,SAAS,IAAIkF,IAAI,CAACrD,SAAS,EAAE,CAAC,CAAC;QACvHO,qBAAqB,EAAE+C,IAAI,CAACC,KAAK,CAACrB,MAAI,CAACf,aAAa,CAAC2B,MAAM,CAAC,CAACM,GAAG,EAAEC,IAAI,KAAKD,GAAG,GAAGC,IAAI,CAACrF,kBAAkB,EAAE,CAAC,CAAC,GAAGkE,MAAI,CAACf,aAAa,CAAC8B,MAAM;OACzI;MAEDf,MAAI,CAACsB,WAAW,EAAE;MAClBtB,MAAI,CAACZ,SAAS,GAAG,KAAK;IAAC;EACzB;EAEQK,UAAUA,CAAA;IAChB,IAAI,CAACH,iBAAiB,GAAGvF,QAAQ,CAAC,IAAI,CAAC,CAACwH,SAAS,CAAC,MAAK;MACrD;IAAA,CACD,CAAC;EACJ;EAEA9E,cAAcA,CAACgE,QAAgB;IAC7B,IAAI,CAAC7D,gBAAgB,GAAG6D,QAAQ;IAChC,IAAI,CAACa,WAAW,EAAE;EACpB;EAEQA,WAAWA,CAAA;IACjB,IAAI,IAAI,CAAC1E,gBAAgB,KAAK,KAAK,EAAE;MACnC,IAAI,CAACgC,aAAa,GAAG,IAAI,CAACK,aAAa,CAAC+B,MAAM,CAACG,IAAI,IAAIA,IAAI,CAACtD,GAAG,KAAK,IAAI,CAAC5C,SAAS,EAAE4C,GAAG,CAAC;KACzF,MAAM;MACL,IAAI,CAACe,aAAa,GAAG,IAAI,CAACK,aAAa,CAAC+B,MAAM,CAACG,IAAI,IACjDA,IAAI,CAACV,QAAQ,KAAK,IAAI,CAAC7D,gBAAgB,IAAIuE,IAAI,CAACtD,GAAG,KAAK,IAAI,CAAC5C,SAAS,EAAE4C,GAAG,CAC5E;;EAEL;EAEA7C,gBAAgBA,CAACE,OAAa;IAC5B,MAAMkF,GAAG,GAAG,IAAIC,IAAI,EAAE;IACtB,MAAMmB,QAAQ,GAAGtG,OAAO,CAACqF,OAAO,EAAE,GAAGH,GAAG,CAACG,OAAO,EAAE;IAElD,IAAIiB,QAAQ,IAAI,CAAC,EAAE;MACjB,OAAO;QAAErG,KAAK,EAAE,CAAC;QAAEC,OAAO,EAAE,CAAC;QAAEC,OAAO,EAAE;MAAC,CAAE;;IAG7C,MAAMF,KAAK,GAAGiG,IAAI,CAACK,KAAK,CAACD,QAAQ,IAAI,IAAI,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;IACrD,MAAMpG,OAAO,GAAGgG,IAAI,CAACK,KAAK,CAAED,QAAQ,IAAI,IAAI,GAAG,EAAE,GAAG,EAAE,CAAC,IAAK,IAAI,GAAG,EAAE,CAAC,CAAC;IACvE,MAAMnG,OAAO,GAAG+F,IAAI,CAACK,KAAK,CAAED,QAAQ,IAAI,IAAI,GAAG,EAAE,CAAC,GAAI,IAAI,CAAC;IAE3D,OAAO;MAAErG,KAAK;MAAEC,OAAO;MAAEC;IAAO,CAAE;EACpC;EAEAG,WAAWA,CAAC2F,IAAkB;IAC5B,IAAI,CAACnC,MAAM,CAAC0C,QAAQ,CAAC,CAAC,OAAO,EAAEP,IAAI,CAACtD,GAAG,CAAC,CAAC;EAC3C;EAEAL,SAASA,CAAC2D,IAAkB,EAAEQ,KAAY;IACxCA,KAAK,CAACC,eAAe,EAAE;IACvB;IACAT,IAAI,CAACrD,SAAS,EAAE;IAChBiC,OAAO,CAAC8B,GAAG,CAAC,eAAe,EAAEV,IAAI,CAACxF,KAAK,CAAC;EAC1C;EAEA2B,cAAcA,CAAC6D,IAAkB,EAAEQ,KAAY;IAC7CA,KAAK,CAACC,eAAe,EAAE;IAEvB,IAAI,IAAI,CAAC1C,cAAc,CAAC4C,GAAG,CAACX,IAAI,CAACtD,GAAG,CAAC,EAAE;MACrC,IAAI,CAACqB,cAAc,CAAC6C,MAAM,CAACZ,IAAI,CAACtD,GAAG,CAAC;KACrC,MAAM;MACL,IAAI,CAACqB,cAAc,CAAC8C,GAAG,CAACb,IAAI,CAACtD,GAAG,CAAC;;EAErC;EAEAD,eAAeA,CAACqE,MAAc;IAC5B,OAAO,IAAI,CAAC/C,cAAc,CAAC4C,GAAG,CAACG,MAAM,CAAC;EACxC;EAEAlG,WAAWA,CAACmG,KAAa;IACvB,OAAO,IAAIC,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;MACpCC,KAAK,EAAE,UAAU;MACjBC,QAAQ,EAAE,KAAK;MACfC,qBAAqB,EAAE;KACxB,CAAC,CAACC,MAAM,CAACN,KAAK,CAAC;EAClB;EAEAhE,WAAWA,CAACuE,KAAa;IACvB,IAAIA,KAAK,IAAI,OAAO,EAAE;MACpB,OAAO,CAACA,KAAK,GAAG,OAAO,EAAEC,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG;KAC1C,MAAM,IAAID,KAAK,IAAI,IAAI,EAAE;MACxB,OAAO,CAACA,KAAK,GAAG,IAAI,EAAEC,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG;;IAExC,OAAOD,KAAK,CAACE,QAAQ,EAAE;EACzB;EAEA9D,aAAaA,CAAC+D,KAAa,EAAEzB,IAAkB;IAC7C,OAAOA,IAAI,CAACtD,GAAG;EACjB;EAEAc,mBAAmBA,CAACiE,KAAa,EAAEnC,QAAsB;IACvD,OAAOA,QAAQ,CAAC/D,IAAI;EACtB;EAEAmG,QAAQA,CAAClB,KAAY;IACnBA,KAAK,CAACmB,cAAc,EAAE;IACtB,IAAI,CAAC9D,MAAM,CAAC0C,QAAQ,CAAC,CAAC,iBAAiB,CAAC,CAAC;EAC3C;EAEA9G,OAAOA,CAAA;IACL,IAAI,CAAC4E,iBAAiB,EAAE;EAC1B;;;uBAxOWV,sBAAsB,EAAA9E,EAAA,CAAA+I,iBAAA,CAAAC,EAAA,CAAAC,MAAA;IAAA;EAAA;;;YAAtBnE,sBAAsB;MAAAoE,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAApJ,EAAA,CAAAqJ,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,gCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCnD/B3J,EAHJ,CAAAC,cAAA,aAAiC,aAED,SACxB;UACFD,EAAA,CAAAE,SAAA,kBAA0C;UAC1CF,EAAA,CAAAG,MAAA,uBACF;UAAAH,EAAA,CAAAI,YAAA,EAAK;UACLJ,EAAA,CAAAC,cAAA,WAAuD;UAA3BD,EAAA,CAAAK,UAAA,mBAAAwJ,mDAAAxG,MAAA;YAAA,OAASuG,GAAA,CAAAf,QAAA,CAAAxF,MAAA,CAAgB;UAAA,EAAC;UAACrD,EAAA,CAAAG,MAAA,cAAO;UAChEH,EADgE,CAAAI,YAAA,EAAI,EAC9D;UAGNJ,EAAA,CAAAC,cAAA,aAA+B;UAsJ7BD,EApJA,CAAAyB,UAAA,IAAAqI,qCAAA,iBAAiD,IAAAC,qCAAA,iBAMQ,KAAAC,sCAAA,iBAO2B,KAAAC,sCAAA,iBAuII;UAK5FjK,EADE,CAAAI,YAAA,EAAM,EACF;;;UAzJIJ,EAAA,CAAAa,SAAA,GAAe;UAAfb,EAAA,CAAAkC,UAAA,SAAA0H,GAAA,CAAAxE,SAAA,CAAe;UAMfpF,EAAA,CAAAa,SAAA,EAAyB;UAAzBb,EAAA,CAAAkC,UAAA,SAAA0H,GAAA,CAAA7I,KAAA,KAAA6I,GAAA,CAAAxE,SAAA,CAAyB;UAOzBpF,EAAA,CAAAa,SAAA,EAAsD;UAAtDb,EAAA,CAAAkC,UAAA,UAAA0H,GAAA,CAAAxE,SAAA,KAAAwE,GAAA,CAAA7I,KAAA,IAAA6I,GAAA,CAAA3E,aAAA,CAAA8B,MAAA,KAAsD;UAuItD/G,EAAA,CAAAa,SAAA,EAAwD;UAAxDb,EAAA,CAAAkC,UAAA,UAAA0H,GAAA,CAAAxE,SAAA,KAAAwE,GAAA,CAAA7I,KAAA,IAAA6I,GAAA,CAAA3E,aAAA,CAAA8B,MAAA,OAAwD;;;qBDlH9DpH,YAAY,EAAAuK,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EACZxK,YAAY,EACZC,WAAW,EAAAwK,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,UAAA;MAAAC,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}