{"ast": null, "code": "import _asyncToGenerator from \"E:/Fashion/DFashionFrontend/frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { BehaviorSubject } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport class TrendingService {\n  constructor(http) {\n    this.http = http;\n    this.API_URL = 'http://localhost:3001/api'; // Updated to correct port\n    // BehaviorSubjects for caching\n    this.trendingProductsSubject = new BehaviorSubject([]);\n    this.suggestedProductsSubject = new BehaviorSubject([]);\n    this.newArrivalsSubject = new BehaviorSubject([]);\n    this.featuredBrandsSubject = new BehaviorSubject([]);\n    this.influencersSubject = new BehaviorSubject([]);\n    // Public observables\n    this.trendingProducts$ = this.trendingProductsSubject.asObservable();\n    this.suggestedProducts$ = this.suggestedProductsSubject.asObservable();\n    this.newArrivals$ = this.newArrivalsSubject.asObservable();\n    this.featuredBrands$ = this.featuredBrandsSubject.asObservable();\n    this.influencers$ = this.influencersSubject.asObservable();\n  }\n  // Get trending products\n  getTrendingProducts(page = 1, limit = 12) {\n    return this.http.get(`${this.API_URL}/v1/products/trending`, {\n      params: {\n        page: page.toString(),\n        limit: limit.toString()\n      }\n    });\n  }\n  // Get suggested products\n  getSuggestedProducts(page = 1, limit = 12) {\n    return this.http.get(`${this.API_URL}/v1/products/suggested`, {\n      params: {\n        page: page.toString(),\n        limit: limit.toString()\n      }\n    });\n  }\n  // Get new arrivals\n  getNewArrivals(page = 1, limit = 12) {\n    return this.http.get(`${this.API_URL}/v1/products/new-arrivals`, {\n      params: {\n        page: page.toString(),\n        limit: limit.toString()\n      }\n    });\n  }\n  // Get featured brands\n  getFeaturedBrands() {\n    return this.http.get(`${this.API_URL}/v1/products/featured-brands`);\n  }\n  // Get influencers\n  getInfluencers(page = 1, limit = 10) {\n    return this.http.get(`${this.API_URL}/v1/users/influencers`, {\n      params: {\n        page: page.toString(),\n        limit: limit.toString()\n      }\n    });\n  }\n  // Load and cache trending products\n  loadTrendingProducts() {\n    var _this = this;\n    return _asyncToGenerator(function* (page = 1, limit = 12) {\n      try {\n        const response = yield _this.getTrendingProducts(page, limit).toPromise();\n        if (response?.success && response?.products) {\n          _this.trendingProductsSubject.next(response.products);\n        }\n      } catch (error) {\n        console.error('Error loading trending products:', error);\n        // Provide mock data as fallback\n        const mockProducts = [{\n          _id: 'tp1',\n          name: 'Trending Sneakers',\n          description: 'Most popular sneakers this season',\n          price: 5999,\n          originalPrice: 7999,\n          discount: 25,\n          category: 'men',\n          subcategory: 'shoes',\n          brand: 'Nike',\n          images: [{\n            url: 'https://images.unsplash.com/photo-1549298916-b41d501d3772?w=400&h=400&fit=crop',\n            alt: 'Trending Sneakers',\n            isPrimary: true\n          }],\n          sizes: [{\n            size: '7',\n            stock: 10\n          }, {\n            size: '8',\n            stock: 15\n          }, {\n            size: '9',\n            stock: 20\n          }, {\n            size: '10',\n            stock: 12\n          }, {\n            size: '11',\n            stock: 8\n          }],\n          colors: [{\n            name: 'White',\n            code: '#FFFFFF'\n          }, {\n            name: 'Black',\n            code: '#000000'\n          }, {\n            name: 'Red',\n            code: '#FF0000'\n          }],\n          vendor: {\n            _id: 'vendor1',\n            username: 'nike_official',\n            fullName: 'Nike Official Store',\n            avatar: 'https://images.unsplash.com/photo-1542291026-7eec264c27ff?w=100&h=100&fit=crop'\n          },\n          tags: ['trending', 'sneakers', 'sports'],\n          features: ['Comfortable', 'Durable', 'Stylish'],\n          isActive: true,\n          isFeatured: true,\n          rating: {\n            average: 4.7,\n            count: 256\n          },\n          reviews: [],\n          seo: {\n            slug: 'trending-sneakers',\n            metaTitle: 'Trending Sneakers - Nike',\n            metaDescription: 'Most popular sneakers this season'\n          },\n          analytics: {\n            views: 1250,\n            likes: 89,\n            shares: 23,\n            purchases: 156\n          },\n          createdAt: new Date(),\n          updatedAt: new Date()\n        }, {\n          _id: 'tp2',\n          name: 'Stylish Handbag',\n          description: 'Elegant handbag for every occasion',\n          price: 4999,\n          originalPrice: 6999,\n          discount: 29,\n          category: 'women',\n          subcategory: 'bags',\n          brand: 'Zara',\n          images: [{\n            url: 'https://images.unsplash.com/photo-1553062407-98eeb64c6a62?w=400&h=400&fit=crop',\n            alt: 'Stylish Handbag',\n            isPrimary: true\n          }],\n          sizes: [{\n            size: 'One Size',\n            stock: 25\n          }],\n          colors: [{\n            name: 'Brown',\n            code: '#8B4513'\n          }, {\n            name: 'Black',\n            code: '#000000'\n          }, {\n            name: 'Tan',\n            code: '#D2B48C'\n          }],\n          vendor: {\n            _id: 'vendor2',\n            username: 'zara_official',\n            fullName: 'Zara Official Store',\n            avatar: 'https://images.unsplash.com/photo-1441986300917-64674bd600d8?w=100&h=100&fit=crop'\n          },\n          tags: ['handbag', 'fashion', 'women'],\n          features: ['Spacious', 'Elegant', 'Versatile'],\n          isActive: true,\n          isFeatured: true,\n          rating: {\n            average: 4.4,\n            count: 189\n          },\n          reviews: [],\n          seo: {\n            slug: 'stylish-handbag',\n            metaTitle: 'Stylish Handbag - Zara',\n            metaDescription: 'Elegant handbag for every occasion'\n          },\n          analytics: {\n            views: 890,\n            likes: 67,\n            shares: 15,\n            purchases: 98\n          },\n          createdAt: new Date(),\n          updatedAt: new Date()\n        }];\n        _this.trendingProductsSubject.next(mockProducts);\n      }\n    }).apply(this, arguments);\n  }\n  // Load and cache suggested products\n  loadSuggestedProducts() {\n    var _this2 = this;\n    return _asyncToGenerator(function* (page = 1, limit = 12) {\n      try {\n        const response = yield _this2.getSuggestedProducts(page, limit).toPromise();\n        if (response?.success && response?.products) {\n          _this2.suggestedProductsSubject.next(response.products);\n        }\n      } catch (error) {\n        console.error('Error loading suggested products:', error);\n      }\n    }).apply(this, arguments);\n  }\n  // Load and cache new arrivals\n  loadNewArrivals() {\n    var _this3 = this;\n    return _asyncToGenerator(function* (page = 1, limit = 12) {\n      try {\n        const response = yield _this3.getNewArrivals(page, limit).toPromise();\n        if (response?.success && response?.products) {\n          _this3.newArrivalsSubject.next(response.products);\n        }\n      } catch (error) {\n        console.error('Error loading new arrivals:', error);\n        // Provide mock data as fallback\n        const mockProducts = [{\n          _id: 'na1',\n          name: 'Summer Floral Dress',\n          description: 'Beautiful summer floral dress perfect for any occasion',\n          price: 2999,\n          originalPrice: 3999,\n          discount: 25,\n          category: 'women',\n          subcategory: 'dresses',\n          brand: 'Zara',\n          images: [{\n            url: 'https://images.unsplash.com/photo-1515372039744-b8f02a3ae446?w=400&h=400&fit=crop',\n            alt: 'Summer Floral Dress',\n            isPrimary: true\n          }],\n          sizes: [{\n            size: 'S',\n            stock: 15\n          }, {\n            size: 'M',\n            stock: 20\n          }, {\n            size: 'L',\n            stock: 18\n          }, {\n            size: 'XL',\n            stock: 12\n          }],\n          colors: [{\n            name: 'Red',\n            code: '#FF0000'\n          }, {\n            name: 'Blue',\n            code: '#0000FF'\n          }, {\n            name: 'Green',\n            code: '#008000'\n          }],\n          vendor: {\n            _id: 'vendor2',\n            username: 'zara_official',\n            fullName: 'Zara Official Store',\n            avatar: 'https://images.unsplash.com/photo-1441986300917-64674bd600d8?w=100&h=100&fit=crop'\n          },\n          tags: ['new', 'dress', 'summer', 'floral'],\n          features: ['Lightweight', 'Breathable', 'Elegant'],\n          isActive: true,\n          isFeatured: false,\n          rating: {\n            average: 4.5,\n            count: 128\n          },\n          reviews: [],\n          seo: {\n            slug: 'summer-floral-dress',\n            metaTitle: 'Summer Floral Dress - Zara',\n            metaDescription: 'Beautiful summer floral dress perfect for any occasion'\n          },\n          analytics: {\n            views: 756,\n            likes: 45,\n            shares: 12,\n            purchases: 67\n          },\n          createdAt: new Date(),\n          updatedAt: new Date()\n        }, {\n          _id: 'na2',\n          name: 'Casual Denim Jacket',\n          description: 'Classic denim jacket for casual wear',\n          price: 3499,\n          originalPrice: 4499,\n          discount: 22,\n          category: 'women',\n          subcategory: 'jackets',\n          brand: 'H&M',\n          images: [{\n            url: 'https://images.unsplash.com/photo-1551698618-1dfe5d97d256?w=400&h=400&fit=crop',\n            alt: 'Casual Denim Jacket',\n            isPrimary: true\n          }],\n          sizes: [{\n            size: 'S',\n            stock: 10\n          }, {\n            size: 'M',\n            stock: 15\n          }, {\n            size: 'L',\n            stock: 12\n          }, {\n            size: 'XL',\n            stock: 8\n          }],\n          colors: [{\n            name: 'Blue',\n            code: '#0000FF'\n          }, {\n            name: 'Black',\n            code: '#000000'\n          }],\n          vendor: {\n            _id: 'vendor3',\n            username: 'hm_official',\n            fullName: 'H&M Official Store',\n            avatar: 'https://images.unsplash.com/photo-1445205170230-053b83016050?w=100&h=100&fit=crop'\n          },\n          tags: ['new', 'jacket', 'denim', 'casual'],\n          features: ['Durable', 'Comfortable', 'Versatile'],\n          isActive: true,\n          isFeatured: false,\n          rating: {\n            average: 4.3,\n            count: 89\n          },\n          reviews: [],\n          seo: {\n            slug: 'casual-denim-jacket',\n            metaTitle: 'Casual Denim Jacket - H&M',\n            metaDescription: 'Classic denim jacket for casual wear'\n          },\n          analytics: {\n            views: 543,\n            likes: 32,\n            shares: 8,\n            purchases: 45\n          },\n          createdAt: new Date(),\n          updatedAt: new Date()\n        }];\n        _this3.newArrivalsSubject.next(mockProducts);\n      }\n    }).apply(this, arguments);\n  }\n  // Load and cache featured brands\n  loadFeaturedBrands() {\n    var _this4 = this;\n    return _asyncToGenerator(function* () {\n      try {\n        const response = yield _this4.getFeaturedBrands().toPromise();\n        if (response?.success && response?.brands) {\n          _this4.featuredBrandsSubject.next(response.brands);\n        }\n      } catch (error) {\n        console.error('Error loading featured brands:', error);\n        // Provide mock data as fallback\n        const mockBrands = [{\n          brand: 'Zara',\n          productCount: 1250,\n          avgRating: 4.3,\n          totalViews: 125000,\n          topProducts: [{\n            _id: 'zara1',\n            name: 'Zara Elegant Dress',\n            description: 'Beautiful elegant dress for special occasions',\n            price: 3999,\n            originalPrice: 4999,\n            discount: 20,\n            category: 'women',\n            subcategory: 'dresses',\n            brand: 'Zara',\n            images: [{\n              url: 'https://images.unsplash.com/photo-1515372039744-b8f02a3ae446?w=400&h=400&fit=crop',\n              alt: 'Zara Elegant Dress',\n              isPrimary: true\n            }],\n            sizes: [{\n              size: 'S',\n              stock: 10\n            }, {\n              size: 'M',\n              stock: 15\n            }, {\n              size: 'L',\n              stock: 8\n            }],\n            colors: [{\n              name: 'Black',\n              code: '#000000'\n            }, {\n              name: 'Navy',\n              code: '#000080'\n            }],\n            vendor: {\n              _id: 'zara_vendor',\n              username: 'zara_official',\n              fullName: 'Zara Official Store',\n              avatar: 'https://images.unsplash.com/photo-1441986300917-64674bd600d8?w=100&h=100&fit=crop'\n            },\n            tags: ['elegant', 'dress', 'formal'],\n            features: ['Premium fabric', 'Elegant design', 'Comfortable fit'],\n            isActive: true,\n            isFeatured: true,\n            rating: {\n              average: 4.3,\n              count: 89\n            },\n            reviews: [],\n            seo: {\n              slug: 'zara-elegant-dress',\n              metaTitle: 'Zara Elegant Dress',\n              metaDescription: 'Beautiful elegant dress for special occasions'\n            },\n            analytics: {\n              views: 890,\n              likes: 67,\n              shares: 12,\n              purchases: 45\n            },\n            createdAt: new Date(),\n            updatedAt: new Date()\n          }]\n        }, {\n          brand: 'H&M',\n          productCount: 980,\n          avgRating: 4.1,\n          totalViews: 98000,\n          topProducts: [{\n            _id: 'hm1',\n            name: 'H&M Casual T-Shirt',\n            description: 'Comfortable casual t-shirt for everyday wear',\n            price: 1299,\n            originalPrice: 1599,\n            discount: 19,\n            category: 'men',\n            subcategory: 'tops',\n            brand: 'H&M',\n            images: [{\n              url: 'https://images.unsplash.com/photo-1521572163474-6864f9cf17ab?w=400&h=400&fit=crop',\n              alt: 'H&M Casual T-Shirt',\n              isPrimary: true\n            }],\n            sizes: [{\n              size: 'S',\n              stock: 20\n            }, {\n              size: 'M',\n              stock: 25\n            }, {\n              size: 'L',\n              stock: 18\n            }, {\n              size: 'XL',\n              stock: 12\n            }],\n            colors: [{\n              name: 'White',\n              code: '#FFFFFF'\n            }, {\n              name: 'Gray',\n              code: '#808080'\n            }, {\n              name: 'Blue',\n              code: '#0000FF'\n            }],\n            vendor: {\n              _id: 'hm_vendor',\n              username: 'hm_official',\n              fullName: 'H&M Official Store',\n              avatar: 'https://images.unsplash.com/photo-1441986300917-64674bd600d8?w=100&h=100&fit=crop'\n            },\n            tags: ['casual', 't-shirt', 'comfortable'],\n            features: ['Soft cotton', 'Casual fit', 'Versatile'],\n            isActive: true,\n            isFeatured: true,\n            rating: {\n              average: 4.1,\n              count: 156\n            },\n            reviews: [],\n            seo: {\n              slug: 'hm-casual-tshirt',\n              metaTitle: 'H&M Casual T-Shirt',\n              metaDescription: 'Comfortable casual t-shirt for everyday wear'\n            },\n            analytics: {\n              views: 567,\n              likes: 43,\n              shares: 8,\n              purchases: 78\n            },\n            createdAt: new Date(),\n            updatedAt: new Date()\n          }]\n        }, {\n          brand: 'Nike',\n          productCount: 750,\n          avgRating: 4.6,\n          totalViews: 156000,\n          topProducts: [{\n            _id: 'nike1',\n            name: 'Nike Air Max',\n            description: 'Premium athletic shoes with air cushioning',\n            price: 8999,\n            originalPrice: 10999,\n            discount: 18,\n            category: 'men',\n            subcategory: 'shoes',\n            brand: 'Nike',\n            images: [{\n              url: 'https://images.unsplash.com/photo-1542291026-7eec264c27ff?w=400&h=400&fit=crop',\n              alt: 'Nike Air Max',\n              isPrimary: true\n            }],\n            sizes: [{\n              size: '7',\n              stock: 8\n            }, {\n              size: '8',\n              stock: 12\n            }, {\n              size: '9',\n              stock: 15\n            }, {\n              size: '10',\n              stock: 10\n            }, {\n              size: '11',\n              stock: 6\n            }],\n            colors: [{\n              name: 'White',\n              code: '#FFFFFF'\n            }, {\n              name: 'Black',\n              code: '#000000'\n            }, {\n              name: 'Red',\n              code: '#FF0000'\n            }],\n            vendor: {\n              _id: 'nike_vendor',\n              username: 'nike_official',\n              fullName: 'Nike Official Store',\n              avatar: 'https://images.unsplash.com/photo-1542291026-7eec264c27ff?w=100&h=100&fit=crop'\n            },\n            tags: ['athletic', 'shoes', 'air-max'],\n            features: ['Air cushioning', 'Durable', 'Comfortable'],\n            isActive: true,\n            isFeatured: true,\n            rating: {\n              average: 4.6,\n              count: 234\n            },\n            reviews: [],\n            seo: {\n              slug: 'nike-air-max',\n              metaTitle: 'Nike Air Max',\n              metaDescription: 'Premium athletic shoes with air cushioning'\n            },\n            analytics: {\n              views: 1234,\n              likes: 98,\n              shares: 25,\n              purchases: 123\n            },\n            createdAt: new Date(),\n            updatedAt: new Date()\n          }]\n        }, {\n          brand: 'Adidas',\n          productCount: 680,\n          avgRating: 4.4,\n          totalViews: 134000,\n          topProducts: [{\n            _id: 'adidas1',\n            name: 'Adidas Ultraboost',\n            description: 'High-performance running shoes with boost technology',\n            price: 9999,\n            originalPrice: 12999,\n            discount: 23,\n            category: 'men',\n            subcategory: 'shoes',\n            brand: 'Adidas',\n            images: [{\n              url: 'https://images.unsplash.com/photo-1551107696-a4b0c5a0d9a2?w=400&h=400&fit=crop',\n              alt: 'Adidas Ultraboost',\n              isPrimary: true\n            }],\n            sizes: [{\n              size: '7',\n              stock: 5\n            }, {\n              size: '8',\n              stock: 10\n            }, {\n              size: '9',\n              stock: 12\n            }, {\n              size: '10',\n              stock: 8\n            }, {\n              size: '11',\n              stock: 4\n            }],\n            colors: [{\n              name: 'Black',\n              code: '#000000'\n            }, {\n              name: 'White',\n              code: '#FFFFFF'\n            }, {\n              name: 'Blue',\n              code: '#0000FF'\n            }],\n            vendor: {\n              _id: 'adidas_vendor',\n              username: 'adidas_official',\n              fullName: 'Adidas Official Store',\n              avatar: 'https://images.unsplash.com/photo-1551107696-a4b0c5a0d9a2?w=100&h=100&fit=crop'\n            },\n            tags: ['running', 'shoes', 'boost'],\n            features: ['Boost technology', 'Lightweight', 'Responsive'],\n            isActive: true,\n            isFeatured: true,\n            rating: {\n              average: 4.4,\n              count: 187\n            },\n            reviews: [],\n            seo: {\n              slug: 'adidas-ultraboost',\n              metaTitle: 'Adidas Ultraboost',\n              metaDescription: 'High-performance running shoes with boost technology'\n            },\n            analytics: {\n              views: 987,\n              likes: 76,\n              shares: 19,\n              purchases: 89\n            },\n            createdAt: new Date(),\n            updatedAt: new Date()\n          }]\n        }];\n        _this4.featuredBrandsSubject.next(mockBrands);\n      }\n    })();\n  }\n  // Load and cache influencers\n  loadInfluencers() {\n    var _this5 = this;\n    return _asyncToGenerator(function* (page = 1, limit = 10) {\n      try {\n        const response = yield _this5.getInfluencers(page, limit).toPromise();\n        if (response?.success && response?.influencers) {\n          _this5.influencersSubject.next(response.influencers);\n        }\n      } catch (error) {\n        console.error('Error loading influencers:', error);\n        // Provide mock data as fallback\n        const mockInfluencers = [{\n          _id: 'inf1',\n          username: 'fashionista_maya',\n          fullName: 'Maya Chen',\n          avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b47c?w=150&h=150&fit=crop&crop=face',\n          bio: 'Fashion enthusiast & style blogger',\n          socialStats: {\n            followersCount: 125000,\n            followingCount: 890,\n            postsCount: 456\n          },\n          isInfluencer: true\n        }, {\n          _id: 'inf2',\n          username: 'style_guru_alex',\n          fullName: 'Alex Rodriguez',\n          avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face',\n          bio: 'Street style & urban fashion',\n          socialStats: {\n            followersCount: 89000,\n            followingCount: 567,\n            postsCount: 234\n          },\n          isInfluencer: true\n        }];\n        _this5.influencersSubject.next(mockInfluencers);\n      }\n    }).apply(this, arguments);\n  }\n  // Clear all cached data\n  clearCache() {\n    this.trendingProductsSubject.next([]);\n    this.suggestedProductsSubject.next([]);\n    this.newArrivalsSubject.next([]);\n    this.featuredBrandsSubject.next([]);\n    this.influencersSubject.next([]);\n  }\n  // Get current cached data\n  getCurrentTrendingProducts() {\n    return this.trendingProductsSubject.value;\n  }\n  getCurrentSuggestedProducts() {\n    return this.suggestedProductsSubject.value;\n  }\n  getCurrentNewArrivals() {\n    return this.newArrivalsSubject.value;\n  }\n  getCurrentFeaturedBrands() {\n    return this.featuredBrandsSubject.value;\n  }\n  getCurrentInfluencers() {\n    return this.influencersSubject.value;\n  }\n  static {\n    this.ɵfac = function TrendingService_Factory(t) {\n      return new (t || TrendingService)(i0.ɵɵinject(i1.HttpClient));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: TrendingService,\n      factory: TrendingService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["BehaviorSubject", "TrendingService", "constructor", "http", "API_URL", "trendingProductsSubject", "suggestedProductsSubject", "newArrivalsSubject", "featuredBrandsSubject", "influencersSubject", "trendingProducts$", "asObservable", "suggestedProducts$", "newArrivals$", "featuredBrands$", "influencers$", "getTrendingProducts", "page", "limit", "get", "params", "toString", "getSuggestedProducts", "getNewArrivals", "getFeaturedBrands", "getInfluencers", "loadTrendingProducts", "_this", "_asyncToGenerator", "response", "to<PERSON>romise", "success", "products", "next", "error", "console", "mockProducts", "_id", "name", "description", "price", "originalPrice", "discount", "category", "subcategory", "brand", "images", "url", "alt", "isPrimary", "sizes", "size", "stock", "colors", "code", "vendor", "username", "fullName", "avatar", "tags", "features", "isActive", "isFeatured", "rating", "average", "count", "reviews", "seo", "slug", "metaTitle", "metaDescription", "analytics", "views", "likes", "shares", "purchases", "createdAt", "Date", "updatedAt", "apply", "arguments", "loadSuggestedProducts", "_this2", "loadNewArrivals", "_this3", "loadFeaturedBrands", "_this4", "brands", "mockBrands", "productCount", "avgRating", "totalViews", "topProducts", "loadInfluencers", "_this5", "influencers", "mockInfluencers", "bio", "socialStats", "followersCount", "followingCount", "postsCount", "isInfluencer", "clearCache", "getCurrentTrendingProducts", "value", "getCurrentSuggestedProducts", "getCurrentNewArrivals", "getCurrentFeaturedBrands", "getCurrentInfluencers", "i0", "ɵɵinject", "i1", "HttpClient", "factory", "ɵfac", "providedIn"], "sources": ["E:\\Fashion\\DFashionFrontend\\frontend\\src\\app\\core\\services\\trending.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { HttpClient } from '@angular/common/http';\nimport { Observable, BehaviorSubject } from 'rxjs';\nimport { environment } from '../../../environments/environment';\nimport { Product } from '../models/product.model';\n\nexport interface TrendingResponse {\n  success: boolean;\n  products: Product[];\n  pagination: {\n    currentPage: number;\n    totalPages: number;\n    totalItems: number;\n    hasNextPage: boolean;\n    hasPrevPage: boolean;\n  };\n}\n\nexport interface FeaturedBrand {\n  brand: string;\n  productCount: number;\n  avgRating: number;\n  totalViews: number;\n  topProducts: Product[];\n}\n\nexport interface FeaturedBrandsResponse {\n  success: boolean;\n  brands: FeaturedBrand[];\n}\n\nexport interface Influencer {\n  _id: string;\n  username: string;\n  fullName: string;\n  avatar: string;\n  bio: string;\n  socialStats: {\n    followersCount: number;\n    postsCount: number;\n    followingCount: number;\n  };\n  isInfluencer: boolean;\n}\n\nexport interface InfluencersResponse {\n  success: boolean;\n  influencers: Influencer[];\n  pagination: {\n    currentPage: number;\n    totalPages: number;\n    totalItems: number;\n    hasNextPage: boolean;\n    hasPrevPage: boolean;\n  };\n}\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class TrendingService {\n  private readonly API_URL = 'http://localhost:3001/api'; // Updated to correct port\n\n  // BehaviorSubjects for caching\n  private trendingProductsSubject = new BehaviorSubject<Product[]>([]);\n  private suggestedProductsSubject = new BehaviorSubject<Product[]>([]);\n  private newArrivalsSubject = new BehaviorSubject<Product[]>([]);\n  private featuredBrandsSubject = new BehaviorSubject<FeaturedBrand[]>([]);\n  private influencersSubject = new BehaviorSubject<Influencer[]>([]);\n\n  // Public observables\n  public trendingProducts$ = this.trendingProductsSubject.asObservable();\n  public suggestedProducts$ = this.suggestedProductsSubject.asObservable();\n  public newArrivals$ = this.newArrivalsSubject.asObservable();\n  public featuredBrands$ = this.featuredBrandsSubject.asObservable();\n  public influencers$ = this.influencersSubject.asObservable();\n\n  constructor(private http: HttpClient) {}\n\n  // Get trending products\n  getTrendingProducts(page: number = 1, limit: number = 12): Observable<TrendingResponse> {\n    return this.http.get<TrendingResponse>(`${this.API_URL}/v1/products/trending`, {\n      params: { page: page.toString(), limit: limit.toString() }\n    });\n  }\n\n  // Get suggested products\n  getSuggestedProducts(page: number = 1, limit: number = 12): Observable<TrendingResponse> {\n    return this.http.get<TrendingResponse>(`${this.API_URL}/v1/products/suggested`, {\n      params: { page: page.toString(), limit: limit.toString() }\n    });\n  }\n\n  // Get new arrivals\n  getNewArrivals(page: number = 1, limit: number = 12): Observable<TrendingResponse> {\n    return this.http.get<TrendingResponse>(`${this.API_URL}/v1/products/new-arrivals`, {\n      params: { page: page.toString(), limit: limit.toString() }\n    });\n  }\n\n  // Get featured brands\n  getFeaturedBrands(): Observable<FeaturedBrandsResponse> {\n    return this.http.get<FeaturedBrandsResponse>(`${this.API_URL}/v1/products/featured-brands`);\n  }\n\n  // Get influencers\n  getInfluencers(page: number = 1, limit: number = 10): Observable<InfluencersResponse> {\n    return this.http.get<InfluencersResponse>(`${this.API_URL}/v1/users/influencers`, {\n      params: { page: page.toString(), limit: limit.toString() }\n    });\n  }\n\n  // Load and cache trending products\n  async loadTrendingProducts(page: number = 1, limit: number = 12): Promise<void> {\n    try {\n      const response = await this.getTrendingProducts(page, limit).toPromise();\n      if (response?.success && response?.products) {\n        this.trendingProductsSubject.next(response.products);\n      }\n    } catch (error) {\n      console.error('Error loading trending products:', error);\n      // Provide mock data as fallback\n      const mockProducts: Product[] = [\n        {\n          _id: 'tp1',\n          name: 'Trending Sneakers',\n          description: 'Most popular sneakers this season',\n          price: 5999,\n          originalPrice: 7999,\n          discount: 25,\n          category: 'men',\n          subcategory: 'shoes',\n          brand: 'Nike',\n          images: [{\n            url: 'https://images.unsplash.com/photo-1549298916-b41d501d3772?w=400&h=400&fit=crop',\n            alt: 'Trending Sneakers',\n            isPrimary: true\n          }],\n          sizes: [\n            { size: '7', stock: 10 },\n            { size: '8', stock: 15 },\n            { size: '9', stock: 20 },\n            { size: '10', stock: 12 },\n            { size: '11', stock: 8 }\n          ],\n          colors: [\n            { name: 'White', code: '#FFFFFF' },\n            { name: 'Black', code: '#000000' },\n            { name: 'Red', code: '#FF0000' }\n          ],\n          vendor: {\n            _id: 'vendor1',\n            username: 'nike_official',\n            fullName: 'Nike Official Store',\n            avatar: 'https://images.unsplash.com/photo-1542291026-7eec264c27ff?w=100&h=100&fit=crop'\n          },\n          tags: ['trending', 'sneakers', 'sports'],\n          features: ['Comfortable', 'Durable', 'Stylish'],\n          isActive: true,\n          isFeatured: true,\n          rating: {\n            average: 4.7,\n            count: 256\n          },\n          reviews: [],\n          seo: {\n            slug: 'trending-sneakers',\n            metaTitle: 'Trending Sneakers - Nike',\n            metaDescription: 'Most popular sneakers this season'\n          },\n          analytics: {\n            views: 1250,\n            likes: 89,\n            shares: 23,\n            purchases: 156\n          },\n          createdAt: new Date(),\n          updatedAt: new Date()\n        },\n        {\n          _id: 'tp2',\n          name: 'Stylish Handbag',\n          description: 'Elegant handbag for every occasion',\n          price: 4999,\n          originalPrice: 6999,\n          discount: 29,\n          category: 'women',\n          subcategory: 'bags',\n          brand: 'Zara',\n          images: [{\n            url: 'https://images.unsplash.com/photo-1553062407-98eeb64c6a62?w=400&h=400&fit=crop',\n            alt: 'Stylish Handbag',\n            isPrimary: true\n          }],\n          sizes: [\n            { size: 'One Size', stock: 25 }\n          ],\n          colors: [\n            { name: 'Brown', code: '#8B4513' },\n            { name: 'Black', code: '#000000' },\n            { name: 'Tan', code: '#D2B48C' }\n          ],\n          vendor: {\n            _id: 'vendor2',\n            username: 'zara_official',\n            fullName: 'Zara Official Store',\n            avatar: 'https://images.unsplash.com/photo-1441986300917-64674bd600d8?w=100&h=100&fit=crop'\n          },\n          tags: ['handbag', 'fashion', 'women'],\n          features: ['Spacious', 'Elegant', 'Versatile'],\n          isActive: true,\n          isFeatured: true,\n          rating: {\n            average: 4.4,\n            count: 189\n          },\n          reviews: [],\n          seo: {\n            slug: 'stylish-handbag',\n            metaTitle: 'Stylish Handbag - Zara',\n            metaDescription: 'Elegant handbag for every occasion'\n          },\n          analytics: {\n            views: 890,\n            likes: 67,\n            shares: 15,\n            purchases: 98\n          },\n          createdAt: new Date(),\n          updatedAt: new Date()\n        }\n      ];\n      this.trendingProductsSubject.next(mockProducts);\n    }\n  }\n\n  // Load and cache suggested products\n  async loadSuggestedProducts(page: number = 1, limit: number = 12): Promise<void> {\n    try {\n      const response = await this.getSuggestedProducts(page, limit).toPromise();\n      if (response?.success && response?.products) {\n        this.suggestedProductsSubject.next(response.products);\n      }\n    } catch (error) {\n      console.error('Error loading suggested products:', error);\n    }\n  }\n\n  // Load and cache new arrivals\n  async loadNewArrivals(page: number = 1, limit: number = 12): Promise<void> {\n    try {\n      const response = await this.getNewArrivals(page, limit).toPromise();\n      if (response?.success && response?.products) {\n        this.newArrivalsSubject.next(response.products);\n      }\n    } catch (error) {\n      console.error('Error loading new arrivals:', error);\n      // Provide mock data as fallback\n      const mockProducts: Product[] = [\n        {\n          _id: 'na1',\n          name: 'Summer Floral Dress',\n          description: 'Beautiful summer floral dress perfect for any occasion',\n          price: 2999,\n          originalPrice: 3999,\n          discount: 25,\n          category: 'women',\n          subcategory: 'dresses',\n          brand: 'Zara',\n          images: [{\n            url: 'https://images.unsplash.com/photo-1515372039744-b8f02a3ae446?w=400&h=400&fit=crop',\n            alt: 'Summer Floral Dress',\n            isPrimary: true\n          }],\n          sizes: [\n            { size: 'S', stock: 15 },\n            { size: 'M', stock: 20 },\n            { size: 'L', stock: 18 },\n            { size: 'XL', stock: 12 }\n          ],\n          colors: [\n            { name: 'Red', code: '#FF0000' },\n            { name: 'Blue', code: '#0000FF' },\n            { name: 'Green', code: '#008000' }\n          ],\n          vendor: {\n            _id: 'vendor2',\n            username: 'zara_official',\n            fullName: 'Zara Official Store',\n            avatar: 'https://images.unsplash.com/photo-1441986300917-64674bd600d8?w=100&h=100&fit=crop'\n          },\n          tags: ['new', 'dress', 'summer', 'floral'],\n          features: ['Lightweight', 'Breathable', 'Elegant'],\n          isActive: true,\n          isFeatured: false,\n          rating: {\n            average: 4.5,\n            count: 128\n          },\n          reviews: [],\n          seo: {\n            slug: 'summer-floral-dress',\n            metaTitle: 'Summer Floral Dress - Zara',\n            metaDescription: 'Beautiful summer floral dress perfect for any occasion'\n          },\n          analytics: {\n            views: 756,\n            likes: 45,\n            shares: 12,\n            purchases: 67\n          },\n          createdAt: new Date(),\n          updatedAt: new Date()\n        },\n        {\n          _id: 'na2',\n          name: 'Casual Denim Jacket',\n          description: 'Classic denim jacket for casual wear',\n          price: 3499,\n          originalPrice: 4499,\n          discount: 22,\n          category: 'women',\n          subcategory: 'jackets',\n          brand: 'H&M',\n          images: [{\n            url: 'https://images.unsplash.com/photo-1551698618-1dfe5d97d256?w=400&h=400&fit=crop',\n            alt: 'Casual Denim Jacket',\n            isPrimary: true\n          }],\n          sizes: [\n            { size: 'S', stock: 10 },\n            { size: 'M', stock: 15 },\n            { size: 'L', stock: 12 },\n            { size: 'XL', stock: 8 }\n          ],\n          colors: [\n            { name: 'Blue', code: '#0000FF' },\n            { name: 'Black', code: '#000000' }\n          ],\n          vendor: {\n            _id: 'vendor3',\n            username: 'hm_official',\n            fullName: 'H&M Official Store',\n            avatar: 'https://images.unsplash.com/photo-1445205170230-053b83016050?w=100&h=100&fit=crop'\n          },\n          tags: ['new', 'jacket', 'denim', 'casual'],\n          features: ['Durable', 'Comfortable', 'Versatile'],\n          isActive: true,\n          isFeatured: false,\n          rating: {\n            average: 4.3,\n            count: 89\n          },\n          reviews: [],\n          seo: {\n            slug: 'casual-denim-jacket',\n            metaTitle: 'Casual Denim Jacket - H&M',\n            metaDescription: 'Classic denim jacket for casual wear'\n          },\n          analytics: {\n            views: 543,\n            likes: 32,\n            shares: 8,\n            purchases: 45\n          },\n          createdAt: new Date(),\n          updatedAt: new Date()\n        }\n      ];\n      this.newArrivalsSubject.next(mockProducts);\n    }\n  }\n\n  // Load and cache featured brands\n  async loadFeaturedBrands(): Promise<void> {\n    try {\n      const response = await this.getFeaturedBrands().toPromise();\n      if (response?.success && response?.brands) {\n        this.featuredBrandsSubject.next(response.brands);\n      }\n    } catch (error) {\n      console.error('Error loading featured brands:', error);\n      // Provide mock data as fallback\n      const mockBrands: FeaturedBrand[] = [\n        {\n          brand: 'Zara',\n          productCount: 1250,\n          avgRating: 4.3,\n          totalViews: 125000,\n          topProducts: [\n            {\n              _id: 'zara1',\n              name: 'Zara Elegant Dress',\n              description: 'Beautiful elegant dress for special occasions',\n              price: 3999,\n              originalPrice: 4999,\n              discount: 20,\n              category: 'women',\n              subcategory: 'dresses',\n              brand: 'Zara',\n              images: [{\n                url: 'https://images.unsplash.com/photo-1515372039744-b8f02a3ae446?w=400&h=400&fit=crop',\n                alt: 'Zara Elegant Dress',\n                isPrimary: true\n              }],\n              sizes: [\n                { size: 'S', stock: 10 },\n                { size: 'M', stock: 15 },\n                { size: 'L', stock: 8 }\n              ],\n              colors: [\n                { name: 'Black', code: '#000000' },\n                { name: 'Navy', code: '#000080' }\n              ],\n              vendor: {\n                _id: 'zara_vendor',\n                username: 'zara_official',\n                fullName: 'Zara Official Store',\n                avatar: 'https://images.unsplash.com/photo-1441986300917-64674bd600d8?w=100&h=100&fit=crop'\n              },\n              tags: ['elegant', 'dress', 'formal'],\n              features: ['Premium fabric', 'Elegant design', 'Comfortable fit'],\n              isActive: true,\n              isFeatured: true,\n              rating: { average: 4.3, count: 89 },\n              reviews: [],\n              seo: {\n                slug: 'zara-elegant-dress',\n                metaTitle: 'Zara Elegant Dress',\n                metaDescription: 'Beautiful elegant dress for special occasions'\n              },\n              analytics: { views: 890, likes: 67, shares: 12, purchases: 45 },\n              createdAt: new Date(),\n              updatedAt: new Date()\n            }\n          ]\n        },\n        {\n          brand: 'H&M',\n          productCount: 980,\n          avgRating: 4.1,\n          totalViews: 98000,\n          topProducts: [\n            {\n              _id: 'hm1',\n              name: 'H&M Casual T-Shirt',\n              description: 'Comfortable casual t-shirt for everyday wear',\n              price: 1299,\n              originalPrice: 1599,\n              discount: 19,\n              category: 'men',\n              subcategory: 'tops',\n              brand: 'H&M',\n              images: [{\n                url: 'https://images.unsplash.com/photo-1521572163474-6864f9cf17ab?w=400&h=400&fit=crop',\n                alt: 'H&M Casual T-Shirt',\n                isPrimary: true\n              }],\n              sizes: [\n                { size: 'S', stock: 20 },\n                { size: 'M', stock: 25 },\n                { size: 'L', stock: 18 },\n                { size: 'XL', stock: 12 }\n              ],\n              colors: [\n                { name: 'White', code: '#FFFFFF' },\n                { name: 'Gray', code: '#808080' },\n                { name: 'Blue', code: '#0000FF' }\n              ],\n              vendor: {\n                _id: 'hm_vendor',\n                username: 'hm_official',\n                fullName: 'H&M Official Store',\n                avatar: 'https://images.unsplash.com/photo-1441986300917-64674bd600d8?w=100&h=100&fit=crop'\n              },\n              tags: ['casual', 't-shirt', 'comfortable'],\n              features: ['Soft cotton', 'Casual fit', 'Versatile'],\n              isActive: true,\n              isFeatured: true,\n              rating: { average: 4.1, count: 156 },\n              reviews: [],\n              seo: {\n                slug: 'hm-casual-tshirt',\n                metaTitle: 'H&M Casual T-Shirt',\n                metaDescription: 'Comfortable casual t-shirt for everyday wear'\n              },\n              analytics: { views: 567, likes: 43, shares: 8, purchases: 78 },\n              createdAt: new Date(),\n              updatedAt: new Date()\n            }\n          ]\n        },\n        {\n          brand: 'Nike',\n          productCount: 750,\n          avgRating: 4.6,\n          totalViews: 156000,\n          topProducts: [\n            {\n              _id: 'nike1',\n              name: 'Nike Air Max',\n              description: 'Premium athletic shoes with air cushioning',\n              price: 8999,\n              originalPrice: 10999,\n              discount: 18,\n              category: 'men',\n              subcategory: 'shoes',\n              brand: 'Nike',\n              images: [{\n                url: 'https://images.unsplash.com/photo-1542291026-7eec264c27ff?w=400&h=400&fit=crop',\n                alt: 'Nike Air Max',\n                isPrimary: true\n              }],\n              sizes: [\n                { size: '7', stock: 8 },\n                { size: '8', stock: 12 },\n                { size: '9', stock: 15 },\n                { size: '10', stock: 10 },\n                { size: '11', stock: 6 }\n              ],\n              colors: [\n                { name: 'White', code: '#FFFFFF' },\n                { name: 'Black', code: '#000000' },\n                { name: 'Red', code: '#FF0000' }\n              ],\n              vendor: {\n                _id: 'nike_vendor',\n                username: 'nike_official',\n                fullName: 'Nike Official Store',\n                avatar: 'https://images.unsplash.com/photo-1542291026-7eec264c27ff?w=100&h=100&fit=crop'\n              },\n              tags: ['athletic', 'shoes', 'air-max'],\n              features: ['Air cushioning', 'Durable', 'Comfortable'],\n              isActive: true,\n              isFeatured: true,\n              rating: { average: 4.6, count: 234 },\n              reviews: [],\n              seo: {\n                slug: 'nike-air-max',\n                metaTitle: 'Nike Air Max',\n                metaDescription: 'Premium athletic shoes with air cushioning'\n              },\n              analytics: { views: 1234, likes: 98, shares: 25, purchases: 123 },\n              createdAt: new Date(),\n              updatedAt: new Date()\n            }\n          ]\n        },\n        {\n          brand: 'Adidas',\n          productCount: 680,\n          avgRating: 4.4,\n          totalViews: 134000,\n          topProducts: [\n            {\n              _id: 'adidas1',\n              name: 'Adidas Ultraboost',\n              description: 'High-performance running shoes with boost technology',\n              price: 9999,\n              originalPrice: 12999,\n              discount: 23,\n              category: 'men',\n              subcategory: 'shoes',\n              brand: 'Adidas',\n              images: [{\n                url: 'https://images.unsplash.com/photo-1551107696-a4b0c5a0d9a2?w=400&h=400&fit=crop',\n                alt: 'Adidas Ultraboost',\n                isPrimary: true\n              }],\n              sizes: [\n                { size: '7', stock: 5 },\n                { size: '8', stock: 10 },\n                { size: '9', stock: 12 },\n                { size: '10', stock: 8 },\n                { size: '11', stock: 4 }\n              ],\n              colors: [\n                { name: 'Black', code: '#000000' },\n                { name: 'White', code: '#FFFFFF' },\n                { name: 'Blue', code: '#0000FF' }\n              ],\n              vendor: {\n                _id: 'adidas_vendor',\n                username: 'adidas_official',\n                fullName: 'Adidas Official Store',\n                avatar: 'https://images.unsplash.com/photo-1551107696-a4b0c5a0d9a2?w=100&h=100&fit=crop'\n              },\n              tags: ['running', 'shoes', 'boost'],\n              features: ['Boost technology', 'Lightweight', 'Responsive'],\n              isActive: true,\n              isFeatured: true,\n              rating: { average: 4.4, count: 187 },\n              reviews: [],\n              seo: {\n                slug: 'adidas-ultraboost',\n                metaTitle: 'Adidas Ultraboost',\n                metaDescription: 'High-performance running shoes with boost technology'\n              },\n              analytics: { views: 987, likes: 76, shares: 19, purchases: 89 },\n              createdAt: new Date(),\n              updatedAt: new Date()\n            }\n          ]\n        }\n      ];\n      this.featuredBrandsSubject.next(mockBrands);\n    }\n  }\n\n  // Load and cache influencers\n  async loadInfluencers(page: number = 1, limit: number = 10): Promise<void> {\n    try {\n      const response = await this.getInfluencers(page, limit).toPromise();\n      if (response?.success && response?.influencers) {\n        this.influencersSubject.next(response.influencers);\n      }\n    } catch (error) {\n      console.error('Error loading influencers:', error);\n      // Provide mock data as fallback\n      const mockInfluencers: Influencer[] = [\n        {\n          _id: 'inf1',\n          username: 'fashionista_maya',\n          fullName: 'Maya Chen',\n          avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b47c?w=150&h=150&fit=crop&crop=face',\n          bio: 'Fashion enthusiast & style blogger',\n          socialStats: {\n            followersCount: 125000,\n            followingCount: 890,\n            postsCount: 456\n          },\n          isInfluencer: true\n        },\n        {\n          _id: 'inf2',\n          username: 'style_guru_alex',\n          fullName: 'Alex Rodriguez',\n          avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face',\n          bio: 'Street style & urban fashion',\n          socialStats: {\n            followersCount: 89000,\n            followingCount: 567,\n            postsCount: 234\n          },\n          isInfluencer: true\n        }\n      ];\n      this.influencersSubject.next(mockInfluencers);\n    }\n  }\n\n  // Clear all cached data\n  clearCache(): void {\n    this.trendingProductsSubject.next([]);\n    this.suggestedProductsSubject.next([]);\n    this.newArrivalsSubject.next([]);\n    this.featuredBrandsSubject.next([]);\n    this.influencersSubject.next([]);\n  }\n\n  // Get current cached data\n  getCurrentTrendingProducts(): Product[] {\n    return this.trendingProductsSubject.value;\n  }\n\n  getCurrentSuggestedProducts(): Product[] {\n    return this.suggestedProductsSubject.value;\n  }\n\n  getCurrentNewArrivals(): Product[] {\n    return this.newArrivalsSubject.value;\n  }\n\n  getCurrentFeaturedBrands(): FeaturedBrand[] {\n    return this.featuredBrandsSubject.value;\n  }\n\n  getCurrentInfluencers(): Influencer[] {\n    return this.influencersSubject.value;\n  }\n}\n"], "mappings": ";AAEA,SAAqBA,eAAe,QAAQ,MAAM;;;AA0DlD,OAAM,MAAOC,eAAe;EAiB1BC,YAAoBC,IAAgB;IAAhB,KAAAA,IAAI,GAAJA,IAAI;IAhBP,KAAAC,OAAO,GAAG,2BAA2B,CAAC,CAAC;IAExD;IACQ,KAAAC,uBAAuB,GAAG,IAAIL,eAAe,CAAY,EAAE,CAAC;IAC5D,KAAAM,wBAAwB,GAAG,IAAIN,eAAe,CAAY,EAAE,CAAC;IAC7D,KAAAO,kBAAkB,GAAG,IAAIP,eAAe,CAAY,EAAE,CAAC;IACvD,KAAAQ,qBAAqB,GAAG,IAAIR,eAAe,CAAkB,EAAE,CAAC;IAChE,KAAAS,kBAAkB,GAAG,IAAIT,eAAe,CAAe,EAAE,CAAC;IAElE;IACO,KAAAU,iBAAiB,GAAG,IAAI,CAACL,uBAAuB,CAACM,YAAY,EAAE;IAC/D,KAAAC,kBAAkB,GAAG,IAAI,CAACN,wBAAwB,CAACK,YAAY,EAAE;IACjE,KAAAE,YAAY,GAAG,IAAI,CAACN,kBAAkB,CAACI,YAAY,EAAE;IACrD,KAAAG,eAAe,GAAG,IAAI,CAACN,qBAAqB,CAACG,YAAY,EAAE;IAC3D,KAAAI,YAAY,GAAG,IAAI,CAACN,kBAAkB,CAACE,YAAY,EAAE;EAErB;EAEvC;EACAK,mBAAmBA,CAACC,IAAA,GAAe,CAAC,EAAEC,KAAA,GAAgB,EAAE;IACtD,OAAO,IAAI,CAACf,IAAI,CAACgB,GAAG,CAAmB,GAAG,IAAI,CAACf,OAAO,uBAAuB,EAAE;MAC7EgB,MAAM,EAAE;QAAEH,IAAI,EAAEA,IAAI,CAACI,QAAQ,EAAE;QAAEH,KAAK,EAAEA,KAAK,CAACG,QAAQ;MAAE;KACzD,CAAC;EACJ;EAEA;EACAC,oBAAoBA,CAACL,IAAA,GAAe,CAAC,EAAEC,KAAA,GAAgB,EAAE;IACvD,OAAO,IAAI,CAACf,IAAI,CAACgB,GAAG,CAAmB,GAAG,IAAI,CAACf,OAAO,wBAAwB,EAAE;MAC9EgB,MAAM,EAAE;QAAEH,IAAI,EAAEA,IAAI,CAACI,QAAQ,EAAE;QAAEH,KAAK,EAAEA,KAAK,CAACG,QAAQ;MAAE;KACzD,CAAC;EACJ;EAEA;EACAE,cAAcA,CAACN,IAAA,GAAe,CAAC,EAAEC,KAAA,GAAgB,EAAE;IACjD,OAAO,IAAI,CAACf,IAAI,CAACgB,GAAG,CAAmB,GAAG,IAAI,CAACf,OAAO,2BAA2B,EAAE;MACjFgB,MAAM,EAAE;QAAEH,IAAI,EAAEA,IAAI,CAACI,QAAQ,EAAE;QAAEH,KAAK,EAAEA,KAAK,CAACG,QAAQ;MAAE;KACzD,CAAC;EACJ;EAEA;EACAG,iBAAiBA,CAAA;IACf,OAAO,IAAI,CAACrB,IAAI,CAACgB,GAAG,CAAyB,GAAG,IAAI,CAACf,OAAO,8BAA8B,CAAC;EAC7F;EAEA;EACAqB,cAAcA,CAACR,IAAA,GAAe,CAAC,EAAEC,KAAA,GAAgB,EAAE;IACjD,OAAO,IAAI,CAACf,IAAI,CAACgB,GAAG,CAAsB,GAAG,IAAI,CAACf,OAAO,uBAAuB,EAAE;MAChFgB,MAAM,EAAE;QAAEH,IAAI,EAAEA,IAAI,CAACI,QAAQ,EAAE;QAAEH,KAAK,EAAEA,KAAK,CAACG,QAAQ;MAAE;KACzD,CAAC;EACJ;EAEA;EACMK,oBAAoBA,CAAA,EAAqC;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA,YAApCX,IAAA,GAAe,CAAC,EAAEC,KAAA,GAAgB,EAAE;MAC7D,IAAI;QACF,MAAMW,QAAQ,SAASF,KAAI,CAACX,mBAAmB,CAACC,IAAI,EAAEC,KAAK,CAAC,CAACY,SAAS,EAAE;QACxE,IAAID,QAAQ,EAAEE,OAAO,IAAIF,QAAQ,EAAEG,QAAQ,EAAE;UAC3CL,KAAI,CAACtB,uBAAuB,CAAC4B,IAAI,CAACJ,QAAQ,CAACG,QAAQ,CAAC;;OAEvD,CAAC,OAAOE,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;QACxD;QACA,MAAME,YAAY,GAAc,CAC9B;UACEC,GAAG,EAAE,KAAK;UACVC,IAAI,EAAE,mBAAmB;UACzBC,WAAW,EAAE,mCAAmC;UAChDC,KAAK,EAAE,IAAI;UACXC,aAAa,EAAE,IAAI;UACnBC,QAAQ,EAAE,EAAE;UACZC,QAAQ,EAAE,KAAK;UACfC,WAAW,EAAE,OAAO;UACpBC,KAAK,EAAE,MAAM;UACbC,MAAM,EAAE,CAAC;YACPC,GAAG,EAAE,gFAAgF;YACrFC,GAAG,EAAE,mBAAmB;YACxBC,SAAS,EAAE;WACZ,CAAC;UACFC,KAAK,EAAE,CACL;YAAEC,IAAI,EAAE,GAAG;YAAEC,KAAK,EAAE;UAAE,CAAE,EACxB;YAAED,IAAI,EAAE,GAAG;YAAEC,KAAK,EAAE;UAAE,CAAE,EACxB;YAAED,IAAI,EAAE,GAAG;YAAEC,KAAK,EAAE;UAAE,CAAE,EACxB;YAAED,IAAI,EAAE,IAAI;YAAEC,KAAK,EAAE;UAAE,CAAE,EACzB;YAAED,IAAI,EAAE,IAAI;YAAEC,KAAK,EAAE;UAAC,CAAE,CACzB;UACDC,MAAM,EAAE,CACN;YAAEf,IAAI,EAAE,OAAO;YAAEgB,IAAI,EAAE;UAAS,CAAE,EAClC;YAAEhB,IAAI,EAAE,OAAO;YAAEgB,IAAI,EAAE;UAAS,CAAE,EAClC;YAAEhB,IAAI,EAAE,KAAK;YAAEgB,IAAI,EAAE;UAAS,CAAE,CACjC;UACDC,MAAM,EAAE;YACNlB,GAAG,EAAE,SAAS;YACdmB,QAAQ,EAAE,eAAe;YACzBC,QAAQ,EAAE,qBAAqB;YAC/BC,MAAM,EAAE;WACT;UACDC,IAAI,EAAE,CAAC,UAAU,EAAE,UAAU,EAAE,QAAQ,CAAC;UACxCC,QAAQ,EAAE,CAAC,aAAa,EAAE,SAAS,EAAE,SAAS,CAAC;UAC/CC,QAAQ,EAAE,IAAI;UACdC,UAAU,EAAE,IAAI;UAChBC,MAAM,EAAE;YACNC,OAAO,EAAE,GAAG;YACZC,KAAK,EAAE;WACR;UACDC,OAAO,EAAE,EAAE;UACXC,GAAG,EAAE;YACHC,IAAI,EAAE,mBAAmB;YACzBC,SAAS,EAAE,0BAA0B;YACrCC,eAAe,EAAE;WAClB;UACDC,SAAS,EAAE;YACTC,KAAK,EAAE,IAAI;YACXC,KAAK,EAAE,EAAE;YACTC,MAAM,EAAE,EAAE;YACVC,SAAS,EAAE;WACZ;UACDC,SAAS,EAAE,IAAIC,IAAI,EAAE;UACrBC,SAAS,EAAE,IAAID,IAAI;SACpB,EACD;UACExC,GAAG,EAAE,KAAK;UACVC,IAAI,EAAE,iBAAiB;UACvBC,WAAW,EAAE,oCAAoC;UACjDC,KAAK,EAAE,IAAI;UACXC,aAAa,EAAE,IAAI;UACnBC,QAAQ,EAAE,EAAE;UACZC,QAAQ,EAAE,OAAO;UACjBC,WAAW,EAAE,MAAM;UACnBC,KAAK,EAAE,MAAM;UACbC,MAAM,EAAE,CAAC;YACPC,GAAG,EAAE,gFAAgF;YACrFC,GAAG,EAAE,iBAAiB;YACtBC,SAAS,EAAE;WACZ,CAAC;UACFC,KAAK,EAAE,CACL;YAAEC,IAAI,EAAE,UAAU;YAAEC,KAAK,EAAE;UAAE,CAAE,CAChC;UACDC,MAAM,EAAE,CACN;YAAEf,IAAI,EAAE,OAAO;YAAEgB,IAAI,EAAE;UAAS,CAAE,EAClC;YAAEhB,IAAI,EAAE,OAAO;YAAEgB,IAAI,EAAE;UAAS,CAAE,EAClC;YAAEhB,IAAI,EAAE,KAAK;YAAEgB,IAAI,EAAE;UAAS,CAAE,CACjC;UACDC,MAAM,EAAE;YACNlB,GAAG,EAAE,SAAS;YACdmB,QAAQ,EAAE,eAAe;YACzBC,QAAQ,EAAE,qBAAqB;YAC/BC,MAAM,EAAE;WACT;UACDC,IAAI,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,OAAO,CAAC;UACrCC,QAAQ,EAAE,CAAC,UAAU,EAAE,SAAS,EAAE,WAAW,CAAC;UAC9CC,QAAQ,EAAE,IAAI;UACdC,UAAU,EAAE,IAAI;UAChBC,MAAM,EAAE;YACNC,OAAO,EAAE,GAAG;YACZC,KAAK,EAAE;WACR;UACDC,OAAO,EAAE,EAAE;UACXC,GAAG,EAAE;YACHC,IAAI,EAAE,iBAAiB;YACvBC,SAAS,EAAE,wBAAwB;YACnCC,eAAe,EAAE;WAClB;UACDC,SAAS,EAAE;YACTC,KAAK,EAAE,GAAG;YACVC,KAAK,EAAE,EAAE;YACTC,MAAM,EAAE,EAAE;YACVC,SAAS,EAAE;WACZ;UACDC,SAAS,EAAE,IAAIC,IAAI,EAAE;UACrBC,SAAS,EAAE,IAAID,IAAI;SACpB,CACF;QACDlD,KAAI,CAACtB,uBAAuB,CAAC4B,IAAI,CAACG,YAAY,CAAC;;IAChD,GAAA2C,KAAA,OAAAC,SAAA;EACH;EAEA;EACMC,qBAAqBA,CAAA,EAAqC;IAAA,IAAAC,MAAA;IAAA,OAAAtD,iBAAA,YAApCX,IAAA,GAAe,CAAC,EAAEC,KAAA,GAAgB,EAAE;MAC9D,IAAI;QACF,MAAMW,QAAQ,SAASqD,MAAI,CAAC5D,oBAAoB,CAACL,IAAI,EAAEC,KAAK,CAAC,CAACY,SAAS,EAAE;QACzE,IAAID,QAAQ,EAAEE,OAAO,IAAIF,QAAQ,EAAEG,QAAQ,EAAE;UAC3CkD,MAAI,CAAC5E,wBAAwB,CAAC2B,IAAI,CAACJ,QAAQ,CAACG,QAAQ,CAAC;;OAExD,CAAC,OAAOE,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;;IAC1D,GAAA6C,KAAA,OAAAC,SAAA;EACH;EAEA;EACMG,eAAeA,CAAA,EAAqC;IAAA,IAAAC,MAAA;IAAA,OAAAxD,iBAAA,YAApCX,IAAA,GAAe,CAAC,EAAEC,KAAA,GAAgB,EAAE;MACxD,IAAI;QACF,MAAMW,QAAQ,SAASuD,MAAI,CAAC7D,cAAc,CAACN,IAAI,EAAEC,KAAK,CAAC,CAACY,SAAS,EAAE;QACnE,IAAID,QAAQ,EAAEE,OAAO,IAAIF,QAAQ,EAAEG,QAAQ,EAAE;UAC3CoD,MAAI,CAAC7E,kBAAkB,CAAC0B,IAAI,CAACJ,QAAQ,CAACG,QAAQ,CAAC;;OAElD,CAAC,OAAOE,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;QACnD;QACA,MAAME,YAAY,GAAc,CAC9B;UACEC,GAAG,EAAE,KAAK;UACVC,IAAI,EAAE,qBAAqB;UAC3BC,WAAW,EAAE,wDAAwD;UACrEC,KAAK,EAAE,IAAI;UACXC,aAAa,EAAE,IAAI;UACnBC,QAAQ,EAAE,EAAE;UACZC,QAAQ,EAAE,OAAO;UACjBC,WAAW,EAAE,SAAS;UACtBC,KAAK,EAAE,MAAM;UACbC,MAAM,EAAE,CAAC;YACPC,GAAG,EAAE,mFAAmF;YACxFC,GAAG,EAAE,qBAAqB;YAC1BC,SAAS,EAAE;WACZ,CAAC;UACFC,KAAK,EAAE,CACL;YAAEC,IAAI,EAAE,GAAG;YAAEC,KAAK,EAAE;UAAE,CAAE,EACxB;YAAED,IAAI,EAAE,GAAG;YAAEC,KAAK,EAAE;UAAE,CAAE,EACxB;YAAED,IAAI,EAAE,GAAG;YAAEC,KAAK,EAAE;UAAE,CAAE,EACxB;YAAED,IAAI,EAAE,IAAI;YAAEC,KAAK,EAAE;UAAE,CAAE,CAC1B;UACDC,MAAM,EAAE,CACN;YAAEf,IAAI,EAAE,KAAK;YAAEgB,IAAI,EAAE;UAAS,CAAE,EAChC;YAAEhB,IAAI,EAAE,MAAM;YAAEgB,IAAI,EAAE;UAAS,CAAE,EACjC;YAAEhB,IAAI,EAAE,OAAO;YAAEgB,IAAI,EAAE;UAAS,CAAE,CACnC;UACDC,MAAM,EAAE;YACNlB,GAAG,EAAE,SAAS;YACdmB,QAAQ,EAAE,eAAe;YACzBC,QAAQ,EAAE,qBAAqB;YAC/BC,MAAM,EAAE;WACT;UACDC,IAAI,EAAE,CAAC,KAAK,EAAE,OAAO,EAAE,QAAQ,EAAE,QAAQ,CAAC;UAC1CC,QAAQ,EAAE,CAAC,aAAa,EAAE,YAAY,EAAE,SAAS,CAAC;UAClDC,QAAQ,EAAE,IAAI;UACdC,UAAU,EAAE,KAAK;UACjBC,MAAM,EAAE;YACNC,OAAO,EAAE,GAAG;YACZC,KAAK,EAAE;WACR;UACDC,OAAO,EAAE,EAAE;UACXC,GAAG,EAAE;YACHC,IAAI,EAAE,qBAAqB;YAC3BC,SAAS,EAAE,4BAA4B;YACvCC,eAAe,EAAE;WAClB;UACDC,SAAS,EAAE;YACTC,KAAK,EAAE,GAAG;YACVC,KAAK,EAAE,EAAE;YACTC,MAAM,EAAE,EAAE;YACVC,SAAS,EAAE;WACZ;UACDC,SAAS,EAAE,IAAIC,IAAI,EAAE;UACrBC,SAAS,EAAE,IAAID,IAAI;SACpB,EACD;UACExC,GAAG,EAAE,KAAK;UACVC,IAAI,EAAE,qBAAqB;UAC3BC,WAAW,EAAE,sCAAsC;UACnDC,KAAK,EAAE,IAAI;UACXC,aAAa,EAAE,IAAI;UACnBC,QAAQ,EAAE,EAAE;UACZC,QAAQ,EAAE,OAAO;UACjBC,WAAW,EAAE,SAAS;UACtBC,KAAK,EAAE,KAAK;UACZC,MAAM,EAAE,CAAC;YACPC,GAAG,EAAE,gFAAgF;YACrFC,GAAG,EAAE,qBAAqB;YAC1BC,SAAS,EAAE;WACZ,CAAC;UACFC,KAAK,EAAE,CACL;YAAEC,IAAI,EAAE,GAAG;YAAEC,KAAK,EAAE;UAAE,CAAE,EACxB;YAAED,IAAI,EAAE,GAAG;YAAEC,KAAK,EAAE;UAAE,CAAE,EACxB;YAAED,IAAI,EAAE,GAAG;YAAEC,KAAK,EAAE;UAAE,CAAE,EACxB;YAAED,IAAI,EAAE,IAAI;YAAEC,KAAK,EAAE;UAAC,CAAE,CACzB;UACDC,MAAM,EAAE,CACN;YAAEf,IAAI,EAAE,MAAM;YAAEgB,IAAI,EAAE;UAAS,CAAE,EACjC;YAAEhB,IAAI,EAAE,OAAO;YAAEgB,IAAI,EAAE;UAAS,CAAE,CACnC;UACDC,MAAM,EAAE;YACNlB,GAAG,EAAE,SAAS;YACdmB,QAAQ,EAAE,aAAa;YACvBC,QAAQ,EAAE,oBAAoB;YAC9BC,MAAM,EAAE;WACT;UACDC,IAAI,EAAE,CAAC,KAAK,EAAE,QAAQ,EAAE,OAAO,EAAE,QAAQ,CAAC;UAC1CC,QAAQ,EAAE,CAAC,SAAS,EAAE,aAAa,EAAE,WAAW,CAAC;UACjDC,QAAQ,EAAE,IAAI;UACdC,UAAU,EAAE,KAAK;UACjBC,MAAM,EAAE;YACNC,OAAO,EAAE,GAAG;YACZC,KAAK,EAAE;WACR;UACDC,OAAO,EAAE,EAAE;UACXC,GAAG,EAAE;YACHC,IAAI,EAAE,qBAAqB;YAC3BC,SAAS,EAAE,2BAA2B;YACtCC,eAAe,EAAE;WAClB;UACDC,SAAS,EAAE;YACTC,KAAK,EAAE,GAAG;YACVC,KAAK,EAAE,EAAE;YACTC,MAAM,EAAE,CAAC;YACTC,SAAS,EAAE;WACZ;UACDC,SAAS,EAAE,IAAIC,IAAI,EAAE;UACrBC,SAAS,EAAE,IAAID,IAAI;SACpB,CACF;QACDO,MAAI,CAAC7E,kBAAkB,CAAC0B,IAAI,CAACG,YAAY,CAAC;;IAC3C,GAAA2C,KAAA,OAAAC,SAAA;EACH;EAEA;EACMK,kBAAkBA,CAAA;IAAA,IAAAC,MAAA;IAAA,OAAA1D,iBAAA;MACtB,IAAI;QACF,MAAMC,QAAQ,SAASyD,MAAI,CAAC9D,iBAAiB,EAAE,CAACM,SAAS,EAAE;QAC3D,IAAID,QAAQ,EAAEE,OAAO,IAAIF,QAAQ,EAAE0D,MAAM,EAAE;UACzCD,MAAI,CAAC9E,qBAAqB,CAACyB,IAAI,CAACJ,QAAQ,CAAC0D,MAAM,CAAC;;OAEnD,CAAC,OAAOrD,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;QACtD;QACA,MAAMsD,UAAU,GAAoB,CAClC;UACE3C,KAAK,EAAE,MAAM;UACb4C,YAAY,EAAE,IAAI;UAClBC,SAAS,EAAE,GAAG;UACdC,UAAU,EAAE,MAAM;UAClBC,WAAW,EAAE,CACX;YACEvD,GAAG,EAAE,OAAO;YACZC,IAAI,EAAE,oBAAoB;YAC1BC,WAAW,EAAE,+CAA+C;YAC5DC,KAAK,EAAE,IAAI;YACXC,aAAa,EAAE,IAAI;YACnBC,QAAQ,EAAE,EAAE;YACZC,QAAQ,EAAE,OAAO;YACjBC,WAAW,EAAE,SAAS;YACtBC,KAAK,EAAE,MAAM;YACbC,MAAM,EAAE,CAAC;cACPC,GAAG,EAAE,mFAAmF;cACxFC,GAAG,EAAE,oBAAoB;cACzBC,SAAS,EAAE;aACZ,CAAC;YACFC,KAAK,EAAE,CACL;cAAEC,IAAI,EAAE,GAAG;cAAEC,KAAK,EAAE;YAAE,CAAE,EACxB;cAAED,IAAI,EAAE,GAAG;cAAEC,KAAK,EAAE;YAAE,CAAE,EACxB;cAAED,IAAI,EAAE,GAAG;cAAEC,KAAK,EAAE;YAAC,CAAE,CACxB;YACDC,MAAM,EAAE,CACN;cAAEf,IAAI,EAAE,OAAO;cAAEgB,IAAI,EAAE;YAAS,CAAE,EAClC;cAAEhB,IAAI,EAAE,MAAM;cAAEgB,IAAI,EAAE;YAAS,CAAE,CAClC;YACDC,MAAM,EAAE;cACNlB,GAAG,EAAE,aAAa;cAClBmB,QAAQ,EAAE,eAAe;cACzBC,QAAQ,EAAE,qBAAqB;cAC/BC,MAAM,EAAE;aACT;YACDC,IAAI,EAAE,CAAC,SAAS,EAAE,OAAO,EAAE,QAAQ,CAAC;YACpCC,QAAQ,EAAE,CAAC,gBAAgB,EAAE,gBAAgB,EAAE,iBAAiB,CAAC;YACjEC,QAAQ,EAAE,IAAI;YACdC,UAAU,EAAE,IAAI;YAChBC,MAAM,EAAE;cAAEC,OAAO,EAAE,GAAG;cAAEC,KAAK,EAAE;YAAE,CAAE;YACnCC,OAAO,EAAE,EAAE;YACXC,GAAG,EAAE;cACHC,IAAI,EAAE,oBAAoB;cAC1BC,SAAS,EAAE,oBAAoB;cAC/BC,eAAe,EAAE;aAClB;YACDC,SAAS,EAAE;cAAEC,KAAK,EAAE,GAAG;cAAEC,KAAK,EAAE,EAAE;cAAEC,MAAM,EAAE,EAAE;cAAEC,SAAS,EAAE;YAAE,CAAE;YAC/DC,SAAS,EAAE,IAAIC,IAAI,EAAE;YACrBC,SAAS,EAAE,IAAID,IAAI;WACpB;SAEJ,EACD;UACEhC,KAAK,EAAE,KAAK;UACZ4C,YAAY,EAAE,GAAG;UACjBC,SAAS,EAAE,GAAG;UACdC,UAAU,EAAE,KAAK;UACjBC,WAAW,EAAE,CACX;YACEvD,GAAG,EAAE,KAAK;YACVC,IAAI,EAAE,oBAAoB;YAC1BC,WAAW,EAAE,8CAA8C;YAC3DC,KAAK,EAAE,IAAI;YACXC,aAAa,EAAE,IAAI;YACnBC,QAAQ,EAAE,EAAE;YACZC,QAAQ,EAAE,KAAK;YACfC,WAAW,EAAE,MAAM;YACnBC,KAAK,EAAE,KAAK;YACZC,MAAM,EAAE,CAAC;cACPC,GAAG,EAAE,mFAAmF;cACxFC,GAAG,EAAE,oBAAoB;cACzBC,SAAS,EAAE;aACZ,CAAC;YACFC,KAAK,EAAE,CACL;cAAEC,IAAI,EAAE,GAAG;cAAEC,KAAK,EAAE;YAAE,CAAE,EACxB;cAAED,IAAI,EAAE,GAAG;cAAEC,KAAK,EAAE;YAAE,CAAE,EACxB;cAAED,IAAI,EAAE,GAAG;cAAEC,KAAK,EAAE;YAAE,CAAE,EACxB;cAAED,IAAI,EAAE,IAAI;cAAEC,KAAK,EAAE;YAAE,CAAE,CAC1B;YACDC,MAAM,EAAE,CACN;cAAEf,IAAI,EAAE,OAAO;cAAEgB,IAAI,EAAE;YAAS,CAAE,EAClC;cAAEhB,IAAI,EAAE,MAAM;cAAEgB,IAAI,EAAE;YAAS,CAAE,EACjC;cAAEhB,IAAI,EAAE,MAAM;cAAEgB,IAAI,EAAE;YAAS,CAAE,CAClC;YACDC,MAAM,EAAE;cACNlB,GAAG,EAAE,WAAW;cAChBmB,QAAQ,EAAE,aAAa;cACvBC,QAAQ,EAAE,oBAAoB;cAC9BC,MAAM,EAAE;aACT;YACDC,IAAI,EAAE,CAAC,QAAQ,EAAE,SAAS,EAAE,aAAa,CAAC;YAC1CC,QAAQ,EAAE,CAAC,aAAa,EAAE,YAAY,EAAE,WAAW,CAAC;YACpDC,QAAQ,EAAE,IAAI;YACdC,UAAU,EAAE,IAAI;YAChBC,MAAM,EAAE;cAAEC,OAAO,EAAE,GAAG;cAAEC,KAAK,EAAE;YAAG,CAAE;YACpCC,OAAO,EAAE,EAAE;YACXC,GAAG,EAAE;cACHC,IAAI,EAAE,kBAAkB;cACxBC,SAAS,EAAE,oBAAoB;cAC/BC,eAAe,EAAE;aAClB;YACDC,SAAS,EAAE;cAAEC,KAAK,EAAE,GAAG;cAAEC,KAAK,EAAE,EAAE;cAAEC,MAAM,EAAE,CAAC;cAAEC,SAAS,EAAE;YAAE,CAAE;YAC9DC,SAAS,EAAE,IAAIC,IAAI,EAAE;YACrBC,SAAS,EAAE,IAAID,IAAI;WACpB;SAEJ,EACD;UACEhC,KAAK,EAAE,MAAM;UACb4C,YAAY,EAAE,GAAG;UACjBC,SAAS,EAAE,GAAG;UACdC,UAAU,EAAE,MAAM;UAClBC,WAAW,EAAE,CACX;YACEvD,GAAG,EAAE,OAAO;YACZC,IAAI,EAAE,cAAc;YACpBC,WAAW,EAAE,4CAA4C;YACzDC,KAAK,EAAE,IAAI;YACXC,aAAa,EAAE,KAAK;YACpBC,QAAQ,EAAE,EAAE;YACZC,QAAQ,EAAE,KAAK;YACfC,WAAW,EAAE,OAAO;YACpBC,KAAK,EAAE,MAAM;YACbC,MAAM,EAAE,CAAC;cACPC,GAAG,EAAE,gFAAgF;cACrFC,GAAG,EAAE,cAAc;cACnBC,SAAS,EAAE;aACZ,CAAC;YACFC,KAAK,EAAE,CACL;cAAEC,IAAI,EAAE,GAAG;cAAEC,KAAK,EAAE;YAAC,CAAE,EACvB;cAAED,IAAI,EAAE,GAAG;cAAEC,KAAK,EAAE;YAAE,CAAE,EACxB;cAAED,IAAI,EAAE,GAAG;cAAEC,KAAK,EAAE;YAAE,CAAE,EACxB;cAAED,IAAI,EAAE,IAAI;cAAEC,KAAK,EAAE;YAAE,CAAE,EACzB;cAAED,IAAI,EAAE,IAAI;cAAEC,KAAK,EAAE;YAAC,CAAE,CACzB;YACDC,MAAM,EAAE,CACN;cAAEf,IAAI,EAAE,OAAO;cAAEgB,IAAI,EAAE;YAAS,CAAE,EAClC;cAAEhB,IAAI,EAAE,OAAO;cAAEgB,IAAI,EAAE;YAAS,CAAE,EAClC;cAAEhB,IAAI,EAAE,KAAK;cAAEgB,IAAI,EAAE;YAAS,CAAE,CACjC;YACDC,MAAM,EAAE;cACNlB,GAAG,EAAE,aAAa;cAClBmB,QAAQ,EAAE,eAAe;cACzBC,QAAQ,EAAE,qBAAqB;cAC/BC,MAAM,EAAE;aACT;YACDC,IAAI,EAAE,CAAC,UAAU,EAAE,OAAO,EAAE,SAAS,CAAC;YACtCC,QAAQ,EAAE,CAAC,gBAAgB,EAAE,SAAS,EAAE,aAAa,CAAC;YACtDC,QAAQ,EAAE,IAAI;YACdC,UAAU,EAAE,IAAI;YAChBC,MAAM,EAAE;cAAEC,OAAO,EAAE,GAAG;cAAEC,KAAK,EAAE;YAAG,CAAE;YACpCC,OAAO,EAAE,EAAE;YACXC,GAAG,EAAE;cACHC,IAAI,EAAE,cAAc;cACpBC,SAAS,EAAE,cAAc;cACzBC,eAAe,EAAE;aAClB;YACDC,SAAS,EAAE;cAAEC,KAAK,EAAE,IAAI;cAAEC,KAAK,EAAE,EAAE;cAAEC,MAAM,EAAE,EAAE;cAAEC,SAAS,EAAE;YAAG,CAAE;YACjEC,SAAS,EAAE,IAAIC,IAAI,EAAE;YACrBC,SAAS,EAAE,IAAID,IAAI;WACpB;SAEJ,EACD;UACEhC,KAAK,EAAE,QAAQ;UACf4C,YAAY,EAAE,GAAG;UACjBC,SAAS,EAAE,GAAG;UACdC,UAAU,EAAE,MAAM;UAClBC,WAAW,EAAE,CACX;YACEvD,GAAG,EAAE,SAAS;YACdC,IAAI,EAAE,mBAAmB;YACzBC,WAAW,EAAE,sDAAsD;YACnEC,KAAK,EAAE,IAAI;YACXC,aAAa,EAAE,KAAK;YACpBC,QAAQ,EAAE,EAAE;YACZC,QAAQ,EAAE,KAAK;YACfC,WAAW,EAAE,OAAO;YACpBC,KAAK,EAAE,QAAQ;YACfC,MAAM,EAAE,CAAC;cACPC,GAAG,EAAE,gFAAgF;cACrFC,GAAG,EAAE,mBAAmB;cACxBC,SAAS,EAAE;aACZ,CAAC;YACFC,KAAK,EAAE,CACL;cAAEC,IAAI,EAAE,GAAG;cAAEC,KAAK,EAAE;YAAC,CAAE,EACvB;cAAED,IAAI,EAAE,GAAG;cAAEC,KAAK,EAAE;YAAE,CAAE,EACxB;cAAED,IAAI,EAAE,GAAG;cAAEC,KAAK,EAAE;YAAE,CAAE,EACxB;cAAED,IAAI,EAAE,IAAI;cAAEC,KAAK,EAAE;YAAC,CAAE,EACxB;cAAED,IAAI,EAAE,IAAI;cAAEC,KAAK,EAAE;YAAC,CAAE,CACzB;YACDC,MAAM,EAAE,CACN;cAAEf,IAAI,EAAE,OAAO;cAAEgB,IAAI,EAAE;YAAS,CAAE,EAClC;cAAEhB,IAAI,EAAE,OAAO;cAAEgB,IAAI,EAAE;YAAS,CAAE,EAClC;cAAEhB,IAAI,EAAE,MAAM;cAAEgB,IAAI,EAAE;YAAS,CAAE,CAClC;YACDC,MAAM,EAAE;cACNlB,GAAG,EAAE,eAAe;cACpBmB,QAAQ,EAAE,iBAAiB;cAC3BC,QAAQ,EAAE,uBAAuB;cACjCC,MAAM,EAAE;aACT;YACDC,IAAI,EAAE,CAAC,SAAS,EAAE,OAAO,EAAE,OAAO,CAAC;YACnCC,QAAQ,EAAE,CAAC,kBAAkB,EAAE,aAAa,EAAE,YAAY,CAAC;YAC3DC,QAAQ,EAAE,IAAI;YACdC,UAAU,EAAE,IAAI;YAChBC,MAAM,EAAE;cAAEC,OAAO,EAAE,GAAG;cAAEC,KAAK,EAAE;YAAG,CAAE;YACpCC,OAAO,EAAE,EAAE;YACXC,GAAG,EAAE;cACHC,IAAI,EAAE,mBAAmB;cACzBC,SAAS,EAAE,mBAAmB;cAC9BC,eAAe,EAAE;aAClB;YACDC,SAAS,EAAE;cAAEC,KAAK,EAAE,GAAG;cAAEC,KAAK,EAAE,EAAE;cAAEC,MAAM,EAAE,EAAE;cAAEC,SAAS,EAAE;YAAE,CAAE;YAC/DC,SAAS,EAAE,IAAIC,IAAI,EAAE;YACrBC,SAAS,EAAE,IAAID,IAAI;WACpB;SAEJ,CACF;QACDS,MAAI,CAAC9E,qBAAqB,CAACyB,IAAI,CAACuD,UAAU,CAAC;;IAC5C;EACH;EAEA;EACMK,eAAeA,CAAA,EAAqC;IAAA,IAAAC,MAAA;IAAA,OAAAlE,iBAAA,YAApCX,IAAA,GAAe,CAAC,EAAEC,KAAA,GAAgB,EAAE;MACxD,IAAI;QACF,MAAMW,QAAQ,SAASiE,MAAI,CAACrE,cAAc,CAACR,IAAI,EAAEC,KAAK,CAAC,CAACY,SAAS,EAAE;QACnE,IAAID,QAAQ,EAAEE,OAAO,IAAIF,QAAQ,EAAEkE,WAAW,EAAE;UAC9CD,MAAI,CAACrF,kBAAkB,CAACwB,IAAI,CAACJ,QAAQ,CAACkE,WAAW,CAAC;;OAErD,CAAC,OAAO7D,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;QAClD;QACA,MAAM8D,eAAe,GAAiB,CACpC;UACE3D,GAAG,EAAE,MAAM;UACXmB,QAAQ,EAAE,kBAAkB;UAC5BC,QAAQ,EAAE,WAAW;UACrBC,MAAM,EAAE,6FAA6F;UACrGuC,GAAG,EAAE,oCAAoC;UACzCC,WAAW,EAAE;YACXC,cAAc,EAAE,MAAM;YACtBC,cAAc,EAAE,GAAG;YACnBC,UAAU,EAAE;WACb;UACDC,YAAY,EAAE;SACf,EACD;UACEjE,GAAG,EAAE,MAAM;UACXmB,QAAQ,EAAE,iBAAiB;UAC3BC,QAAQ,EAAE,gBAAgB;UAC1BC,MAAM,EAAE,6FAA6F;UACrGuC,GAAG,EAAE,8BAA8B;UACnCC,WAAW,EAAE;YACXC,cAAc,EAAE,KAAK;YACrBC,cAAc,EAAE,GAAG;YACnBC,UAAU,EAAE;WACb;UACDC,YAAY,EAAE;SACf,CACF;QACDR,MAAI,CAACrF,kBAAkB,CAACwB,IAAI,CAAC+D,eAAe,CAAC;;IAC9C,GAAAjB,KAAA,OAAAC,SAAA;EACH;EAEA;EACAuB,UAAUA,CAAA;IACR,IAAI,CAAClG,uBAAuB,CAAC4B,IAAI,CAAC,EAAE,CAAC;IACrC,IAAI,CAAC3B,wBAAwB,CAAC2B,IAAI,CAAC,EAAE,CAAC;IACtC,IAAI,CAAC1B,kBAAkB,CAAC0B,IAAI,CAAC,EAAE,CAAC;IAChC,IAAI,CAACzB,qBAAqB,CAACyB,IAAI,CAAC,EAAE,CAAC;IACnC,IAAI,CAACxB,kBAAkB,CAACwB,IAAI,CAAC,EAAE,CAAC;EAClC;EAEA;EACAuE,0BAA0BA,CAAA;IACxB,OAAO,IAAI,CAACnG,uBAAuB,CAACoG,KAAK;EAC3C;EAEAC,2BAA2BA,CAAA;IACzB,OAAO,IAAI,CAACpG,wBAAwB,CAACmG,KAAK;EAC5C;EAEAE,qBAAqBA,CAAA;IACnB,OAAO,IAAI,CAACpG,kBAAkB,CAACkG,KAAK;EACtC;EAEAG,wBAAwBA,CAAA;IACtB,OAAO,IAAI,CAACpG,qBAAqB,CAACiG,KAAK;EACzC;EAEAI,qBAAqBA,CAAA;IACnB,OAAO,IAAI,CAACpG,kBAAkB,CAACgG,KAAK;EACtC;;;uBA3mBWxG,eAAe,EAAA6G,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA;IAAA;EAAA;;;aAAfhH,eAAe;MAAAiH,OAAA,EAAfjH,eAAe,CAAAkH,IAAA;MAAAC,UAAA,EAFd;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}