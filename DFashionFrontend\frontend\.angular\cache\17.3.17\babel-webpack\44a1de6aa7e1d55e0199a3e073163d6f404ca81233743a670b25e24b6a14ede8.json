{"ast": null, "code": "import _asyncToGenerator from \"E:/Fashion/DFashionFrontend/frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\nimport { IonicModule } from '@ionic/angular';\nimport { Subscription } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"@ionic/angular\";\nfunction TrendingInfluencerHighlightsComponent_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 9);\n    i0.ɵɵelement(1, \"ion-spinner\", 10);\n    i0.ɵɵelementStart(2, \"p\");\n    i0.ɵɵtext(3, \"Loading trending influencers...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction TrendingInfluencerHighlightsComponent_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 11);\n    i0.ɵɵelement(1, \"ion-icon\", 12);\n    i0.ɵɵelementStart(2, \"p\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"button\", 13);\n    i0.ɵɵlistener(\"click\", function TrendingInfluencerHighlightsComponent_div_9_Template_button_click_4_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onRetry());\n    });\n    i0.ɵɵtext(5, \"Try Again\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r1.error);\n  }\n}\nfunction TrendingInfluencerHighlightsComponent_div_10_div_1_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 36);\n    i0.ɵɵelement(1, \"ion-icon\", 37);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction TrendingInfluencerHighlightsComponent_div_10_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 20);\n    i0.ɵɵlistener(\"click\", function TrendingInfluencerHighlightsComponent_div_10_div_1_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onInfluencerClick(ctx_r1.featuredInfluencer));\n    });\n    i0.ɵɵelementStart(1, \"div\", 21)(2, \"div\", 22);\n    i0.ɵɵelement(3, \"img\", 23);\n    i0.ɵɵtemplate(4, TrendingInfluencerHighlightsComponent_div_10_div_1_div_4_Template, 2, 0, \"div\", 24);\n    i0.ɵɵelementStart(5, \"div\", 25);\n    i0.ɵɵelement(6, \"ion-icon\", 26);\n    i0.ɵɵtext(7, \" Trending \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"div\", 27)(9, \"h4\", 28);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"p\", 29);\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"p\", 30);\n    i0.ɵɵtext(14);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"div\", 31)(16, \"div\", 32)(17, \"span\", 33);\n    i0.ɵɵtext(18);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"span\", 34);\n    i0.ɵɵtext(20, \"Followers\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(21, \"div\", 32)(22, \"span\", 33);\n    i0.ɵɵtext(23);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(24, \"span\", 34);\n    i0.ɵɵtext(25, \"Posts\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(26, \"div\", 32)(27, \"span\", 33);\n    i0.ɵɵtext(28);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(29, \"span\", 34);\n    i0.ɵɵtext(30, \"Engagement\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(31, \"button\", 35);\n    i0.ɵɵlistener(\"click\", function TrendingInfluencerHighlightsComponent_div_10_div_1_Template_button_click_31_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.toggleFollow(ctx_r1.featuredInfluencer, $event));\n    });\n    i0.ɵɵtext(32);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"src\", ctx_r1.featuredInfluencer.avatar, i0.ɵɵsanitizeUrl)(\"alt\", ctx_r1.featuredInfluencer.fullName);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.featuredInfluencer.isVerified);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r1.featuredInfluencer.fullName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(\"@\" + ctx_r1.featuredInfluencer.username);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.featuredInfluencer.bio);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r1.formatCount(ctx_r1.featuredInfluencer.stats.followers));\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r1.formatCount(ctx_r1.featuredInfluencer.stats.posts));\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r1.featuredInfluencer.stats.engagementRate, \"%\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassProp(\"following\", ctx_r1.featuredInfluencer.isFollowing);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.featuredInfluencer.isFollowing ? \"Following\" : \"Follow\", \" \");\n  }\n}\nfunction TrendingInfluencerHighlightsComponent_div_10_div_3_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 36);\n    i0.ɵɵelement(1, \"ion-icon\", 37);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction TrendingInfluencerHighlightsComponent_div_10_div_3_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 45);\n  }\n}\nfunction TrendingInfluencerHighlightsComponent_div_10_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 38);\n    i0.ɵɵlistener(\"click\", function TrendingInfluencerHighlightsComponent_div_10_div_3_Template_div_click_0_listener() {\n      const influencer_r5 = i0.ɵɵrestoreView(_r4).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onInfluencerClick(influencer_r5));\n    });\n    i0.ɵɵelementStart(1, \"div\", 22);\n    i0.ɵɵelement(2, \"img\", 23);\n    i0.ɵɵtemplate(3, TrendingInfluencerHighlightsComponent_div_10_div_3_div_3_Template, 2, 0, \"div\", 24)(4, TrendingInfluencerHighlightsComponent_div_10_div_3_div_4_Template, 1, 0, \"div\", 39);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 40)(6, \"div\", 27)(7, \"h5\", 28);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"p\", 29);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"div\", 41)(12, \"span\", 42);\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"span\", 43);\n    i0.ɵɵtext(15);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(16, \"button\", 44);\n    i0.ɵɵlistener(\"click\", function TrendingInfluencerHighlightsComponent_div_10_div_3_Template_button_click_16_listener($event) {\n      const influencer_r5 = i0.ɵɵrestoreView(_r4).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.toggleFollow(influencer_r5, $event));\n    });\n    i0.ɵɵtext(17);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const influencer_r5 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"src\", influencer_r5.avatar, i0.ɵɵsanitizeUrl)(\"alt\", influencer_r5.fullName);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", influencer_r5.isVerified);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", influencer_r5.isOnline);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(influencer_r5.fullName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(\"@\" + influencer_r5.username);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r1.formatCount(influencer_r5.stats.followers), \" followers\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", influencer_r5.stats.engagementRate, \"% engagement\");\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"following\", influencer_r5.isFollowing);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", influencer_r5.isFollowing ? \"Following\" : \"Follow\", \" \");\n  }\n}\nfunction TrendingInfluencerHighlightsComponent_div_10_div_4_div_4_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 62);\n    i0.ɵɵelement(1, \"ion-icon\", 63);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction TrendingInfluencerHighlightsComponent_div_10_div_4_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 50);\n    i0.ɵɵlistener(\"click\", function TrendingInfluencerHighlightsComponent_div_10_div_4_div_4_Template_div_click_0_listener() {\n      const post_r7 = i0.ɵɵrestoreView(_r6).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.onPostClick(post_r7));\n    });\n    i0.ɵɵelementStart(1, \"div\", 51);\n    i0.ɵɵelement(2, \"img\", 52);\n    i0.ɵɵelementStart(3, \"div\", 53)(4, \"div\", 54)(5, \"div\", 55);\n    i0.ɵɵelement(6, \"ion-icon\", 56);\n    i0.ɵɵelementStart(7, \"span\");\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"div\", 55);\n    i0.ɵɵelement(10, \"ion-icon\", 57);\n    i0.ɵɵelementStart(11, \"span\");\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵtemplate(13, TrendingInfluencerHighlightsComponent_div_10_div_4_div_4_div_13_Template, 2, 0, \"div\", 58);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"div\", 59);\n    i0.ɵɵelement(15, \"img\", 60);\n    i0.ɵɵelementStart(16, \"span\", 61);\n    i0.ɵɵtext(17);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const post_r7 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"src\", post_r7.image, i0.ɵɵsanitizeUrl)(\"alt\", post_r7.caption);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r1.formatCount(post_r7.likes));\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r1.formatCount(post_r7.comments));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", post_r7.type === \"reel\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"src\", post_r7.author.avatar, i0.ɵɵsanitizeUrl)(\"alt\", post_r7.author.fullName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(post_r7.author.fullName);\n  }\n}\nfunction TrendingInfluencerHighlightsComponent_div_10_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 46)(1, \"h4\", 47);\n    i0.ɵɵtext(2, \"Recent Trending Posts\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 48);\n    i0.ɵɵtemplate(4, TrendingInfluencerHighlightsComponent_div_10_div_4_div_4_Template, 18, 8, \"div\", 49);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.recentPosts)(\"ngForTrackBy\", ctx_r1.trackByPostId);\n  }\n}\nfunction TrendingInfluencerHighlightsComponent_div_10_div_5_button_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 68);\n    i0.ɵɵlistener(\"click\", function TrendingInfluencerHighlightsComponent_div_10_div_5_button_4_Template_button_click_0_listener() {\n      const category_r9 = i0.ɵɵrestoreView(_r8).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.onCategoryClick(category_r9));\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵelementStart(2, \"span\", 69);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const category_r9 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" #\", category_r9.name, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.formatCount(category_r9.postCount));\n  }\n}\nfunction TrendingInfluencerHighlightsComponent_div_10_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 64)(1, \"h4\", 65);\n    i0.ɵɵtext(2, \"Trending Categories\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 66);\n    i0.ɵɵtemplate(4, TrendingInfluencerHighlightsComponent_div_10_div_5_button_4_Template, 4, 2, \"button\", 67);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.trendingCategories)(\"ngForTrackBy\", ctx_r1.trackByCategoryName);\n  }\n}\nfunction TrendingInfluencerHighlightsComponent_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 14);\n    i0.ɵɵtemplate(1, TrendingInfluencerHighlightsComponent_div_10_div_1_Template, 33, 12, \"div\", 15);\n    i0.ɵɵelementStart(2, \"div\", 16);\n    i0.ɵɵtemplate(3, TrendingInfluencerHighlightsComponent_div_10_div_3_Template, 18, 11, \"div\", 17);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(4, TrendingInfluencerHighlightsComponent_div_10_div_4_Template, 5, 2, \"div\", 18)(5, TrendingInfluencerHighlightsComponent_div_10_div_5_Template, 5, 2, \"div\", 19);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.featuredInfluencer);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.otherInfluencers)(\"ngForTrackBy\", ctx_r1.trackByInfluencerId);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.recentPosts.length > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.trendingCategories.length > 0);\n  }\n}\nfunction TrendingInfluencerHighlightsComponent_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 70);\n    i0.ɵɵelement(1, \"ion-icon\", 2);\n    i0.ɵɵelementStart(2, \"p\");\n    i0.ɵɵtext(3, \"No trending influencers available\");\n    i0.ɵɵelementEnd()();\n  }\n}\nexport class TrendingInfluencerHighlightsComponent {\n  constructor(router) {\n    this.router = router;\n    this.trendingInfluencers = [];\n    this.featuredInfluencer = null;\n    this.otherInfluencers = [];\n    this.recentPosts = [];\n    this.trendingCategories = [];\n    this.isLoading = true;\n    this.error = null;\n    this.subscription = new Subscription();\n  }\n  ngOnInit() {\n    this.loadTrendingInfluencers();\n  }\n  ngOnDestroy() {\n    this.subscription.unsubscribe();\n  }\n  loadTrendingInfluencers() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      try {\n        _this.isLoading = true;\n        _this.error = null;\n        // Mock data for now - replace with actual service call\n        yield _this.loadMockInfluencers();\n      } catch (error) {\n        console.error('Error loading trending influencers:', error);\n        _this.error = 'Failed to load trending influencers';\n        _this.isLoading = false;\n      }\n    })();\n  }\n  loadMockInfluencers() {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      // Simulate API call delay\n      yield new Promise(resolve => setTimeout(resolve, 900));\n      // Mock trending influencers\n      _this2.trendingInfluencers = [{\n        _id: 'inf1',\n        username: 'fashionista_maya',\n        fullName: 'Maya Sharma',\n        avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b47c?w=150&h=150&fit=crop&crop=face',\n        bio: 'Fashion enthusiast | Style blogger | Trendsetter ✨',\n        isVerified: true,\n        isFollowing: false,\n        isOnline: true,\n        stats: {\n          followers: 245000,\n          following: 1200,\n          posts: 890,\n          engagementRate: 8.5\n        },\n        categories: ['fashion', 'lifestyle', 'beauty'],\n        createdAt: new Date()\n      }, {\n        _id: 'inf2',\n        username: 'style_guru_raj',\n        fullName: 'Raj Patel',\n        avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face',\n        bio: 'Menswear expert | Fashion consultant | Style tips daily 👔',\n        isVerified: true,\n        isFollowing: true,\n        isOnline: false,\n        stats: {\n          followers: 189000,\n          following: 890,\n          posts: 567,\n          engagementRate: 7.2\n        },\n        categories: ['menswear', 'fashion', 'lifestyle'],\n        createdAt: new Date()\n      }, {\n        _id: 'inf3',\n        username: 'trendy_priya',\n        fullName: 'Priya Singh',\n        avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face',\n        bio: 'Affordable fashion | Budget styling | Fashion for all 💫',\n        isVerified: false,\n        isFollowing: false,\n        isOnline: true,\n        stats: {\n          followers: 156000,\n          following: 2300,\n          posts: 1234,\n          engagementRate: 9.1\n        },\n        categories: ['budget-fashion', 'styling', 'accessories'],\n        createdAt: new Date()\n      }, {\n        _id: 'inf4',\n        username: 'luxury_lifestyle_arjun',\n        fullName: 'Arjun Mehta',\n        avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face',\n        bio: 'Luxury fashion | High-end styling | Designer collections 🔥',\n        isVerified: true,\n        isFollowing: false,\n        isOnline: false,\n        stats: {\n          followers: 298000,\n          following: 567,\n          posts: 345,\n          engagementRate: 6.8\n        },\n        categories: ['luxury', 'designer', 'menswear'],\n        createdAt: new Date()\n      }];\n      // Set featured influencer (highest engagement rate)\n      _this2.featuredInfluencer = _this2.trendingInfluencers.reduce((prev, current) => prev.stats.engagementRate > current.stats.engagementRate ? prev : current);\n      // Set other influencers (excluding featured)\n      _this2.otherInfluencers = _this2.trendingInfluencers.filter(inf => inf._id !== _this2.featuredInfluencer?._id);\n      // Mock recent posts\n      _this2.recentPosts = [{\n        _id: 'post1',\n        image: 'https://images.unsplash.com/photo-1515372039744-b8f02a3ae446?w=300&h=300&fit=crop',\n        caption: 'Summer vibes with this amazing dress! 🌞',\n        type: 'post',\n        likes: 2340,\n        comments: 156,\n        author: _this2.featuredInfluencer,\n        createdAt: new Date()\n      }, {\n        _id: 'post2',\n        image: 'https://images.unsplash.com/photo-1441986300917-64674bd600d8?w=300&h=300&fit=crop',\n        caption: 'Perfect outfit for a casual day out',\n        type: 'reel',\n        likes: 1890,\n        comments: 89,\n        author: _this2.otherInfluencers[0],\n        createdAt: new Date()\n      }, {\n        _id: 'post3',\n        image: 'https://images.unsplash.com/photo-1445205170230-053b83016050?w=300&h=300&fit=crop',\n        caption: 'Styling tips for the modern woman',\n        type: 'post',\n        likes: 3456,\n        comments: 234,\n        author: _this2.otherInfluencers[1],\n        createdAt: new Date()\n      }, {\n        _id: 'post4',\n        image: 'https://images.unsplash.com/photo-1542291026-7eec264c27ff?w=300&h=300&fit=crop',\n        caption: 'Sneaker game strong! 👟',\n        type: 'reel',\n        likes: 2789,\n        comments: 167,\n        author: _this2.otherInfluencers[2],\n        createdAt: new Date()\n      }];\n      // Mock trending categories\n      _this2.trendingCategories = [{\n        name: 'SummerFashion',\n        postCount: 12500,\n        color: '#ff6b6b'\n      }, {\n        name: 'StreetStyle',\n        postCount: 8900,\n        color: '#4ecdc4'\n      }, {\n        name: 'Minimalist',\n        postCount: 6700,\n        color: '#45b7d1'\n      }, {\n        name: 'Vintage',\n        postCount: 5400,\n        color: '#f9ca24'\n      }, {\n        name: 'Sustainable',\n        postCount: 4200,\n        color: '#6c5ce7'\n      }];\n      _this2.isLoading = false;\n    })();\n  }\n  onInfluencerClick(influencer) {\n    this.router.navigate(['/profile', influencer.username]);\n  }\n  onPostClick(post) {\n    this.router.navigate(['/post', post._id]);\n  }\n  onCategoryClick(category) {\n    this.router.navigate(['/explore'], {\n      queryParams: {\n        hashtag: category.name\n      }\n    });\n  }\n  toggleFollow(influencer, event) {\n    event.stopPropagation();\n    influencer.isFollowing = !influencer.isFollowing;\n    if (influencer.isFollowing) {\n      influencer.stats.followers++;\n    } else {\n      influencer.stats.followers--;\n    }\n  }\n  formatCount(count) {\n    if (count >= 1000000) {\n      return (count / 1000000).toFixed(1) + 'M';\n    } else if (count >= 1000) {\n      return (count / 1000).toFixed(1) + 'K';\n    }\n    return count.toString();\n  }\n  trackByInfluencerId(index, influencer) {\n    return influencer._id;\n  }\n  trackByPostId(index, post) {\n    return post._id;\n  }\n  trackByCategoryName(index, category) {\n    return category.name;\n  }\n  onSeeAll(event) {\n    event.preventDefault();\n    this.router.navigate(['/trending/influencers']);\n  }\n  onRetry() {\n    this.loadTrendingInfluencers();\n  }\n  static {\n    this.ɵfac = function TrendingInfluencerHighlightsComponent_Factory(t) {\n      return new (t || TrendingInfluencerHighlightsComponent)(i0.ɵɵdirectiveInject(i1.Router));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: TrendingInfluencerHighlightsComponent,\n      selectors: [[\"app-trending-influencer-highlights\"]],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 12,\n      vars: 4,\n      consts: [[1, \"component-container\"], [1, \"component-header\"], [\"name\", \"people-outline\"], [\"href\", \"#\", 1, \"see-all\", 3, \"click\"], [1, \"component-content\"], [\"class\", \"loading-container\", 4, \"ngIf\"], [\"class\", \"error-container\", 4, \"ngIf\"], [\"class\", \"influencers-content\", 4, \"ngIf\"], [\"class\", \"empty-container\", 4, \"ngIf\"], [1, \"loading-container\"], [\"name\", \"crescent\"], [1, \"error-container\"], [\"name\", \"alert-circle-outline\"], [1, \"retry-btn\", 3, \"click\"], [1, \"influencers-content\"], [\"class\", \"featured-influencer\", 3, \"click\", 4, \"ngIf\"], [1, \"other-influencers\"], [\"class\", \"influencer-item\", 3, \"click\", 4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [\"class\", \"recent-posts\", 4, \"ngIf\"], [\"class\", \"trending-categories\", 4, \"ngIf\"], [1, \"featured-influencer\", 3, \"click\"], [1, \"influencer-card\"], [1, \"influencer-avatar-container\"], [1, \"influencer-avatar\", 3, \"src\", \"alt\"], [\"class\", \"verified-badge\", 4, \"ngIf\"], [1, \"trending-badge\"], [\"name\", \"flame\"], [1, \"influencer-info\"], [1, \"influencer-name\"], [1, \"influencer-username\"], [1, \"influencer-bio\"], [1, \"influencer-stats\"], [1, \"stat-item\"], [1, \"stat-value\"], [1, \"stat-label\"], [1, \"follow-btn\", 3, \"click\"], [1, \"verified-badge\"], [\"name\", \"checkmark-circle\"], [1, \"influencer-item\", 3, \"click\"], [\"class\", \"online-indicator\", 4, \"ngIf\"], [1, \"influencer-details\"], [1, \"influencer-metrics\"], [1, \"followers\"], [1, \"engagement\"], [1, \"follow-btn-small\", 3, \"click\"], [1, \"online-indicator\"], [1, \"recent-posts\"], [1, \"posts-title\"], [1, \"posts-grid\"], [\"class\", \"post-item\", 3, \"click\", 4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [1, \"post-item\", 3, \"click\"], [1, \"post-image-container\"], [1, \"post-image\", 3, \"src\", \"alt\"], [1, \"post-overlay\"], [1, \"post-stats\"], [1, \"stat\"], [\"name\", \"heart\"], [\"name\", \"chatbubble\"], [\"class\", \"post-type-badge\", 4, \"ngIf\"], [1, \"post-author\"], [1, \"author-avatar\", 3, \"src\", \"alt\"], [1, \"author-name\"], [1, \"post-type-badge\"], [\"name\", \"play\"], [1, \"trending-categories\"], [1, \"categories-title\"], [1, \"categories-list\"], [\"class\", \"category-tag\", 3, \"click\", 4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [1, \"category-tag\", 3, \"click\"], [1, \"category-count\"], [1, \"empty-container\"]],\n      template: function TrendingInfluencerHighlightsComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"h3\");\n          i0.ɵɵelement(3, \"ion-icon\", 2);\n          i0.ɵɵtext(4, \" Trending Influencers \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(5, \"a\", 3);\n          i0.ɵɵlistener(\"click\", function TrendingInfluencerHighlightsComponent_Template_a_click_5_listener($event) {\n            return ctx.onSeeAll($event);\n          });\n          i0.ɵɵtext(6, \"See All\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(7, \"div\", 4);\n          i0.ɵɵtemplate(8, TrendingInfluencerHighlightsComponent_div_8_Template, 4, 0, \"div\", 5)(9, TrendingInfluencerHighlightsComponent_div_9_Template, 6, 1, \"div\", 6)(10, TrendingInfluencerHighlightsComponent_div_10_Template, 6, 5, \"div\", 7)(11, TrendingInfluencerHighlightsComponent_div_11_Template, 4, 0, \"div\", 8);\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.error && !ctx.isLoading);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading && !ctx.error && ctx.trendingInfluencers.length > 0);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading && !ctx.error && ctx.trendingInfluencers.length === 0);\n        }\n      },\n      dependencies: [CommonModule, i2.NgForOf, i2.NgIf, RouterModule, IonicModule, i3.IonIcon, i3.IonSpinner],\n      styles: [\".loading-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n  padding: 40px 20px;\\n  color: #666;\\n}\\n.loading-container[_ngcontent-%COMP%]   ion-spinner[_ngcontent-%COMP%] {\\n  margin-bottom: 16px;\\n}\\n.loading-container[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 14px;\\n}\\n\\n.error-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n  padding: 40px 20px;\\n  color: #e74c3c;\\n  text-align: center;\\n}\\n.error-container[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n  font-size: 48px;\\n  margin-bottom: 16px;\\n  color: #e74c3c;\\n}\\n.error-container[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0 0 16px 0;\\n  font-size: 14px;\\n}\\n.error-container[_ngcontent-%COMP%]   .retry-btn[_ngcontent-%COMP%] {\\n  background: #e74c3c;\\n  color: white;\\n  border: none;\\n  padding: 8px 16px;\\n  border-radius: 6px;\\n  font-size: 12px;\\n  cursor: pointer;\\n  transition: background 0.3s ease;\\n}\\n.error-container[_ngcontent-%COMP%]   .retry-btn[_ngcontent-%COMP%]:hover {\\n  background: #c0392b;\\n}\\n\\n.empty-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n  padding: 40px 20px;\\n  color: #999;\\n  text-align: center;\\n}\\n.empty-container[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n  font-size: 48px;\\n  margin-bottom: 16px;\\n  color: #ccc;\\n}\\n.empty-container[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 14px;\\n}\\n\\n.influencers-content[_ngcontent-%COMP%]   .featured-influencer[_ngcontent-%COMP%] {\\n  margin-bottom: 24px;\\n  cursor: pointer;\\n}\\n.influencers-content[_ngcontent-%COMP%]   .featured-influencer[_ngcontent-%COMP%]   .influencer-card[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\\n  border-radius: 16px;\\n  padding: 24px;\\n  color: white;\\n  position: relative;\\n  overflow: hidden;\\n  transition: transform 0.3s ease, box-shadow 0.3s ease;\\n}\\n.influencers-content[_ngcontent-%COMP%]   .featured-influencer[_ngcontent-%COMP%]   .influencer-card[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-4px);\\n  box-shadow: 0 12px 30px rgba(102, 126, 234, 0.3);\\n}\\n.influencers-content[_ngcontent-%COMP%]   .featured-influencer[_ngcontent-%COMP%]   .influencer-card[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  background: url('data:image/svg+xml,<svg xmlns=\\\"http://www.w3.org/2000/svg\\\" viewBox=\\\"0 0 100 100\\\"><defs><pattern id=\\\"grain\\\" width=\\\"100\\\" height=\\\"100\\\" patternUnits=\\\"userSpaceOnUse\\\"><circle cx=\\\"25\\\" cy=\\\"25\\\" r=\\\"1\\\" fill=\\\"white\\\" opacity=\\\"0.1\\\"/><circle cx=\\\"75\\\" cy=\\\"75\\\" r=\\\"1\\\" fill=\\\"white\\\" opacity=\\\"0.1\\\"/></pattern></defs><rect width=\\\"100\\\" height=\\\"100\\\" fill=\\\"url(%23grain)\\\"/></svg>');\\n  opacity: 0.1;\\n  pointer-events: none;\\n}\\n.influencers-content[_ngcontent-%COMP%]   .featured-influencer[_ngcontent-%COMP%]   .influencer-card[_ngcontent-%COMP%]   .influencer-avatar-container[_ngcontent-%COMP%] {\\n  position: relative;\\n  width: 80px;\\n  height: 80px;\\n  margin: 0 auto 16px;\\n}\\n.influencers-content[_ngcontent-%COMP%]   .featured-influencer[_ngcontent-%COMP%]   .influencer-card[_ngcontent-%COMP%]   .influencer-avatar-container[_ngcontent-%COMP%]   .influencer-avatar[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 100%;\\n  border-radius: 50%;\\n  object-fit: cover;\\n  border: 3px solid rgba(255, 255, 255, 0.3);\\n  transition: transform 0.3s ease;\\n}\\n.influencers-content[_ngcontent-%COMP%]   .featured-influencer[_ngcontent-%COMP%]   .influencer-card[_ngcontent-%COMP%]   .influencer-avatar-container[_ngcontent-%COMP%]:hover   .influencer-avatar[_ngcontent-%COMP%] {\\n  transform: scale(1.05);\\n}\\n.influencers-content[_ngcontent-%COMP%]   .featured-influencer[_ngcontent-%COMP%]   .influencer-card[_ngcontent-%COMP%]   .influencer-avatar-container[_ngcontent-%COMP%]   .verified-badge[_ngcontent-%COMP%] {\\n  position: absolute;\\n  bottom: 2px;\\n  right: 2px;\\n  background: #1da1f2;\\n  border-radius: 50%;\\n  width: 24px;\\n  height: 24px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n}\\n.influencers-content[_ngcontent-%COMP%]   .featured-influencer[_ngcontent-%COMP%]   .influencer-card[_ngcontent-%COMP%]   .influencer-avatar-container[_ngcontent-%COMP%]   .verified-badge[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  color: white;\\n}\\n.influencers-content[_ngcontent-%COMP%]   .featured-influencer[_ngcontent-%COMP%]   .influencer-card[_ngcontent-%COMP%]   .influencer-avatar-container[_ngcontent-%COMP%]   .trending-badge[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: -8px;\\n  right: -8px;\\n  background: linear-gradient(135deg, #ff6b6b, #ee5a24);\\n  color: white;\\n  padding: 4px 8px;\\n  border-radius: 12px;\\n  font-size: 10px;\\n  font-weight: 600;\\n  display: flex;\\n  align-items: center;\\n  gap: 4px;\\n  box-shadow: 0 2px 8px rgba(255, 107, 107, 0.4);\\n}\\n.influencers-content[_ngcontent-%COMP%]   .featured-influencer[_ngcontent-%COMP%]   .influencer-card[_ngcontent-%COMP%]   .influencer-avatar-container[_ngcontent-%COMP%]   .trending-badge[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  animation: _ngcontent-%COMP%_flicker 1.5s infinite alternate;\\n}\\n.influencers-content[_ngcontent-%COMP%]   .featured-influencer[_ngcontent-%COMP%]   .influencer-card[_ngcontent-%COMP%]   .influencer-info[_ngcontent-%COMP%] {\\n  text-align: center;\\n}\\n.influencers-content[_ngcontent-%COMP%]   .featured-influencer[_ngcontent-%COMP%]   .influencer-card[_ngcontent-%COMP%]   .influencer-info[_ngcontent-%COMP%]   .influencer-name[_ngcontent-%COMP%] {\\n  font-size: 20px;\\n  font-weight: 700;\\n  margin: 0 0 4px 0;\\n  color: white;\\n}\\n.influencers-content[_ngcontent-%COMP%]   .featured-influencer[_ngcontent-%COMP%]   .influencer-card[_ngcontent-%COMP%]   .influencer-info[_ngcontent-%COMP%]   .influencer-username[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  margin: 0 0 8px 0;\\n  opacity: 0.8;\\n}\\n.influencers-content[_ngcontent-%COMP%]   .featured-influencer[_ngcontent-%COMP%]   .influencer-card[_ngcontent-%COMP%]   .influencer-info[_ngcontent-%COMP%]   .influencer-bio[_ngcontent-%COMP%] {\\n  font-size: 13px;\\n  margin: 0 0 16px 0;\\n  opacity: 0.9;\\n  line-height: 1.4;\\n}\\n.influencers-content[_ngcontent-%COMP%]   .featured-influencer[_ngcontent-%COMP%]   .influencer-card[_ngcontent-%COMP%]   .influencer-info[_ngcontent-%COMP%]   .influencer-stats[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-around;\\n  margin-bottom: 16px;\\n}\\n.influencers-content[_ngcontent-%COMP%]   .featured-influencer[_ngcontent-%COMP%]   .influencer-card[_ngcontent-%COMP%]   .influencer-info[_ngcontent-%COMP%]   .influencer-stats[_ngcontent-%COMP%]   .stat-item[_ngcontent-%COMP%] {\\n  text-align: center;\\n}\\n.influencers-content[_ngcontent-%COMP%]   .featured-influencer[_ngcontent-%COMP%]   .influencer-card[_ngcontent-%COMP%]   .influencer-info[_ngcontent-%COMP%]   .influencer-stats[_ngcontent-%COMP%]   .stat-item[_ngcontent-%COMP%]   .stat-value[_ngcontent-%COMP%] {\\n  display: block;\\n  font-size: 16px;\\n  font-weight: 700;\\n  color: white;\\n}\\n.influencers-content[_ngcontent-%COMP%]   .featured-influencer[_ngcontent-%COMP%]   .influencer-card[_ngcontent-%COMP%]   .influencer-info[_ngcontent-%COMP%]   .influencer-stats[_ngcontent-%COMP%]   .stat-item[_ngcontent-%COMP%]   .stat-label[_ngcontent-%COMP%] {\\n  font-size: 11px;\\n  opacity: 0.8;\\n  text-transform: uppercase;\\n  letter-spacing: 0.5px;\\n}\\n.influencers-content[_ngcontent-%COMP%]   .featured-influencer[_ngcontent-%COMP%]   .influencer-card[_ngcontent-%COMP%]   .influencer-info[_ngcontent-%COMP%]   .follow-btn[_ngcontent-%COMP%] {\\n  background: rgba(255, 255, 255, 0.2);\\n  color: white;\\n  border: 2px solid rgba(255, 255, 255, 0.3);\\n  padding: 8px 24px;\\n  border-radius: 20px;\\n  font-size: 14px;\\n  font-weight: 600;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n}\\n.influencers-content[_ngcontent-%COMP%]   .featured-influencer[_ngcontent-%COMP%]   .influencer-card[_ngcontent-%COMP%]   .influencer-info[_ngcontent-%COMP%]   .follow-btn[_ngcontent-%COMP%]:hover {\\n  background: rgba(255, 255, 255, 0.3);\\n  border-color: rgba(255, 255, 255, 0.5);\\n  transform: scale(1.05);\\n}\\n.influencers-content[_ngcontent-%COMP%]   .featured-influencer[_ngcontent-%COMP%]   .influencer-card[_ngcontent-%COMP%]   .influencer-info[_ngcontent-%COMP%]   .follow-btn.following[_ngcontent-%COMP%] {\\n  background: rgba(255, 255, 255, 0.9);\\n  color: #667eea;\\n  border-color: rgba(255, 255, 255, 0.9);\\n}\\n.influencers-content[_ngcontent-%COMP%]   .other-influencers[_ngcontent-%COMP%] {\\n  margin-bottom: 24px;\\n}\\n.influencers-content[_ngcontent-%COMP%]   .other-influencers[_ngcontent-%COMP%]   .influencer-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  padding: 12px;\\n  border-radius: 12px;\\n  margin-bottom: 8px;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n  border: 1px solid #f0f0f0;\\n}\\n.influencers-content[_ngcontent-%COMP%]   .other-influencers[_ngcontent-%COMP%]   .influencer-item[_ngcontent-%COMP%]:hover {\\n  background: #f8f9fa;\\n  transform: translateX(4px);\\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\\n}\\n.influencers-content[_ngcontent-%COMP%]   .other-influencers[_ngcontent-%COMP%]   .influencer-item[_ngcontent-%COMP%]   .influencer-avatar-container[_ngcontent-%COMP%] {\\n  position: relative;\\n  width: 50px;\\n  height: 50px;\\n  margin-right: 12px;\\n}\\n.influencers-content[_ngcontent-%COMP%]   .other-influencers[_ngcontent-%COMP%]   .influencer-item[_ngcontent-%COMP%]   .influencer-avatar-container[_ngcontent-%COMP%]   .influencer-avatar[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 100%;\\n  border-radius: 50%;\\n  object-fit: cover;\\n  border: 2px solid #f0f0f0;\\n}\\n.influencers-content[_ngcontent-%COMP%]   .other-influencers[_ngcontent-%COMP%]   .influencer-item[_ngcontent-%COMP%]   .influencer-avatar-container[_ngcontent-%COMP%]   .verified-badge[_ngcontent-%COMP%] {\\n  position: absolute;\\n  bottom: -2px;\\n  right: -2px;\\n  background: #1da1f2;\\n  border-radius: 50%;\\n  width: 18px;\\n  height: 18px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n}\\n.influencers-content[_ngcontent-%COMP%]   .other-influencers[_ngcontent-%COMP%]   .influencer-item[_ngcontent-%COMP%]   .influencer-avatar-container[_ngcontent-%COMP%]   .verified-badge[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n  font-size: 10px;\\n  color: white;\\n}\\n.influencers-content[_ngcontent-%COMP%]   .other-influencers[_ngcontent-%COMP%]   .influencer-item[_ngcontent-%COMP%]   .influencer-avatar-container[_ngcontent-%COMP%]   .online-indicator[_ngcontent-%COMP%] {\\n  position: absolute;\\n  bottom: 2px;\\n  right: 2px;\\n  width: 12px;\\n  height: 12px;\\n  background: #2ecc71;\\n  border: 2px solid white;\\n  border-radius: 50%;\\n}\\n.influencers-content[_ngcontent-%COMP%]   .other-influencers[_ngcontent-%COMP%]   .influencer-item[_ngcontent-%COMP%]   .influencer-details[_ngcontent-%COMP%] {\\n  flex: 1;\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n}\\n.influencers-content[_ngcontent-%COMP%]   .other-influencers[_ngcontent-%COMP%]   .influencer-item[_ngcontent-%COMP%]   .influencer-details[_ngcontent-%COMP%]   .influencer-info[_ngcontent-%COMP%]   .influencer-name[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  font-weight: 600;\\n  margin: 0 0 2px 0;\\n  color: #333;\\n}\\n.influencers-content[_ngcontent-%COMP%]   .other-influencers[_ngcontent-%COMP%]   .influencer-item[_ngcontent-%COMP%]   .influencer-details[_ngcontent-%COMP%]   .influencer-info[_ngcontent-%COMP%]   .influencer-username[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  margin: 0 0 4px 0;\\n  color: #666;\\n}\\n.influencers-content[_ngcontent-%COMP%]   .other-influencers[_ngcontent-%COMP%]   .influencer-item[_ngcontent-%COMP%]   .influencer-details[_ngcontent-%COMP%]   .influencer-info[_ngcontent-%COMP%]   .influencer-metrics[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 12px;\\n}\\n.influencers-content[_ngcontent-%COMP%]   .other-influencers[_ngcontent-%COMP%]   .influencer-item[_ngcontent-%COMP%]   .influencer-details[_ngcontent-%COMP%]   .influencer-info[_ngcontent-%COMP%]   .influencer-metrics[_ngcontent-%COMP%]   .followers[_ngcontent-%COMP%], .influencers-content[_ngcontent-%COMP%]   .other-influencers[_ngcontent-%COMP%]   .influencer-item[_ngcontent-%COMP%]   .influencer-details[_ngcontent-%COMP%]   .influencer-info[_ngcontent-%COMP%]   .influencer-metrics[_ngcontent-%COMP%]   .engagement[_ngcontent-%COMP%] {\\n  font-size: 10px;\\n  color: #999;\\n}\\n.influencers-content[_ngcontent-%COMP%]   .other-influencers[_ngcontent-%COMP%]   .influencer-item[_ngcontent-%COMP%]   .influencer-details[_ngcontent-%COMP%]   .follow-btn-small[_ngcontent-%COMP%] {\\n  background: #6c5ce7;\\n  color: white;\\n  border: none;\\n  padding: 6px 16px;\\n  border-radius: 16px;\\n  font-size: 12px;\\n  font-weight: 600;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n}\\n.influencers-content[_ngcontent-%COMP%]   .other-influencers[_ngcontent-%COMP%]   .influencer-item[_ngcontent-%COMP%]   .influencer-details[_ngcontent-%COMP%]   .follow-btn-small[_ngcontent-%COMP%]:hover {\\n  background: #5a4fcf;\\n  transform: scale(1.05);\\n}\\n.influencers-content[_ngcontent-%COMP%]   .other-influencers[_ngcontent-%COMP%]   .influencer-item[_ngcontent-%COMP%]   .influencer-details[_ngcontent-%COMP%]   .follow-btn-small.following[_ngcontent-%COMP%] {\\n  background: #e9ecef;\\n  color: #6c757d;\\n  border: 1px solid #dee2e6;\\n}\\n.influencers-content[_ngcontent-%COMP%]   .other-influencers[_ngcontent-%COMP%]   .influencer-item[_ngcontent-%COMP%]   .influencer-details[_ngcontent-%COMP%]   .follow-btn-small.following[_ngcontent-%COMP%]:hover {\\n  background: #f8f9fa;\\n}\\n.influencers-content[_ngcontent-%COMP%]   .recent-posts[_ngcontent-%COMP%] {\\n  margin-bottom: 24px;\\n}\\n.influencers-content[_ngcontent-%COMP%]   .recent-posts[_ngcontent-%COMP%]   .posts-title[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  font-weight: 600;\\n  margin: 0 0 16px 0;\\n  color: #333;\\n}\\n.influencers-content[_ngcontent-%COMP%]   .recent-posts[_ngcontent-%COMP%]   .posts-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));\\n  gap: 12px;\\n}\\n.influencers-content[_ngcontent-%COMP%]   .recent-posts[_ngcontent-%COMP%]   .posts-grid[_ngcontent-%COMP%]   .post-item[_ngcontent-%COMP%] {\\n  cursor: pointer;\\n  border-radius: 8px;\\n  overflow: hidden;\\n  transition: transform 0.3s ease;\\n}\\n.influencers-content[_ngcontent-%COMP%]   .recent-posts[_ngcontent-%COMP%]   .posts-grid[_ngcontent-%COMP%]   .post-item[_ngcontent-%COMP%]:hover {\\n  transform: scale(1.02);\\n}\\n.influencers-content[_ngcontent-%COMP%]   .recent-posts[_ngcontent-%COMP%]   .posts-grid[_ngcontent-%COMP%]   .post-item[_ngcontent-%COMP%]   .post-image-container[_ngcontent-%COMP%] {\\n  position: relative;\\n  width: 100%;\\n  height: 120px;\\n  overflow: hidden;\\n}\\n.influencers-content[_ngcontent-%COMP%]   .recent-posts[_ngcontent-%COMP%]   .posts-grid[_ngcontent-%COMP%]   .post-item[_ngcontent-%COMP%]   .post-image-container[_ngcontent-%COMP%]   .post-image[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 100%;\\n  object-fit: cover;\\n  transition: transform 0.3s ease;\\n}\\n.influencers-content[_ngcontent-%COMP%]   .recent-posts[_ngcontent-%COMP%]   .posts-grid[_ngcontent-%COMP%]   .post-item[_ngcontent-%COMP%]   .post-image-container[_ngcontent-%COMP%]:hover   .post-image[_ngcontent-%COMP%] {\\n  transform: scale(1.1);\\n}\\n.influencers-content[_ngcontent-%COMP%]   .recent-posts[_ngcontent-%COMP%]   .posts-grid[_ngcontent-%COMP%]   .post-item[_ngcontent-%COMP%]   .post-image-container[_ngcontent-%COMP%]   .post-overlay[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  background: rgba(0, 0, 0, 0.4);\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  opacity: 0;\\n  transition: opacity 0.3s ease;\\n}\\n.influencers-content[_ngcontent-%COMP%]   .recent-posts[_ngcontent-%COMP%]   .posts-grid[_ngcontent-%COMP%]   .post-item[_ngcontent-%COMP%]   .post-image-container[_ngcontent-%COMP%]   .post-overlay[_ngcontent-%COMP%]   .post-stats[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 16px;\\n}\\n.influencers-content[_ngcontent-%COMP%]   .recent-posts[_ngcontent-%COMP%]   .posts-grid[_ngcontent-%COMP%]   .post-item[_ngcontent-%COMP%]   .post-image-container[_ngcontent-%COMP%]   .post-overlay[_ngcontent-%COMP%]   .post-stats[_ngcontent-%COMP%]   .stat[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 4px;\\n  color: white;\\n  font-size: 12px;\\n  font-weight: 600;\\n}\\n.influencers-content[_ngcontent-%COMP%]   .recent-posts[_ngcontent-%COMP%]   .posts-grid[_ngcontent-%COMP%]   .post-item[_ngcontent-%COMP%]   .post-image-container[_ngcontent-%COMP%]   .post-overlay[_ngcontent-%COMP%]   .post-stats[_ngcontent-%COMP%]   .stat[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n}\\n.influencers-content[_ngcontent-%COMP%]   .recent-posts[_ngcontent-%COMP%]   .posts-grid[_ngcontent-%COMP%]   .post-item[_ngcontent-%COMP%]   .post-image-container[_ngcontent-%COMP%]:hover   .post-overlay[_ngcontent-%COMP%] {\\n  opacity: 1;\\n}\\n.influencers-content[_ngcontent-%COMP%]   .recent-posts[_ngcontent-%COMP%]   .posts-grid[_ngcontent-%COMP%]   .post-item[_ngcontent-%COMP%]   .post-image-container[_ngcontent-%COMP%]   .post-type-badge[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 6px;\\n  right: 6px;\\n  background: rgba(0, 0, 0, 0.7);\\n  color: white;\\n  border-radius: 50%;\\n  width: 24px;\\n  height: 24px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n}\\n.influencers-content[_ngcontent-%COMP%]   .recent-posts[_ngcontent-%COMP%]   .posts-grid[_ngcontent-%COMP%]   .post-item[_ngcontent-%COMP%]   .post-image-container[_ngcontent-%COMP%]   .post-type-badge[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n}\\n.influencers-content[_ngcontent-%COMP%]   .recent-posts[_ngcontent-%COMP%]   .posts-grid[_ngcontent-%COMP%]   .post-item[_ngcontent-%COMP%]   .post-author[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 6px;\\n  padding: 8px;\\n  background: white;\\n}\\n.influencers-content[_ngcontent-%COMP%]   .recent-posts[_ngcontent-%COMP%]   .posts-grid[_ngcontent-%COMP%]   .post-item[_ngcontent-%COMP%]   .post-author[_ngcontent-%COMP%]   .author-avatar[_ngcontent-%COMP%] {\\n  width: 20px;\\n  height: 20px;\\n  border-radius: 50%;\\n  object-fit: cover;\\n}\\n.influencers-content[_ngcontent-%COMP%]   .recent-posts[_ngcontent-%COMP%]   .posts-grid[_ngcontent-%COMP%]   .post-item[_ngcontent-%COMP%]   .post-author[_ngcontent-%COMP%]   .author-name[_ngcontent-%COMP%] {\\n  font-size: 10px;\\n  font-weight: 600;\\n  color: #333;\\n  flex: 1;\\n  white-space: nowrap;\\n  overflow: hidden;\\n  text-overflow: ellipsis;\\n}\\n.influencers-content[_ngcontent-%COMP%]   .trending-categories[_ngcontent-%COMP%]   .categories-title[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  font-weight: 600;\\n  margin: 0 0 16px 0;\\n  color: #333;\\n}\\n.influencers-content[_ngcontent-%COMP%]   .trending-categories[_ngcontent-%COMP%]   .categories-list[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-wrap: wrap;\\n  gap: 8px;\\n}\\n.influencers-content[_ngcontent-%COMP%]   .trending-categories[_ngcontent-%COMP%]   .categories-list[_ngcontent-%COMP%]   .category-tag[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #6c5ce7, #a29bfe);\\n  color: white;\\n  border: none;\\n  padding: 8px 12px;\\n  border-radius: 20px;\\n  font-size: 12px;\\n  font-weight: 600;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n  display: flex;\\n  align-items: center;\\n  gap: 6px;\\n}\\n.influencers-content[_ngcontent-%COMP%]   .trending-categories[_ngcontent-%COMP%]   .categories-list[_ngcontent-%COMP%]   .category-tag[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-2px);\\n  box-shadow: 0 4px 12px rgba(108, 92, 231, 0.3);\\n}\\n.influencers-content[_ngcontent-%COMP%]   .trending-categories[_ngcontent-%COMP%]   .categories-list[_ngcontent-%COMP%]   .category-tag[_ngcontent-%COMP%]   .category-count[_ngcontent-%COMP%] {\\n  background: rgba(255, 255, 255, 0.2);\\n  padding: 2px 6px;\\n  border-radius: 10px;\\n  font-size: 10px;\\n}\\n\\n@keyframes _ngcontent-%COMP%_flicker {\\n  0% {\\n    opacity: 1;\\n  }\\n  100% {\\n    opacity: 0.7;\\n  }\\n}\\n@media (max-width: 768px) {\\n  .influencers-content[_ngcontent-%COMP%]   .featured-influencer[_ngcontent-%COMP%]   .influencer-card[_ngcontent-%COMP%] {\\n    padding: 20px;\\n  }\\n  .influencers-content[_ngcontent-%COMP%]   .featured-influencer[_ngcontent-%COMP%]   .influencer-card[_ngcontent-%COMP%]   .influencer-avatar-container[_ngcontent-%COMP%] {\\n    width: 70px;\\n    height: 70px;\\n  }\\n  .influencers-content[_ngcontent-%COMP%]   .featured-influencer[_ngcontent-%COMP%]   .influencer-card[_ngcontent-%COMP%]   .influencer-info[_ngcontent-%COMP%]   .influencer-name[_ngcontent-%COMP%] {\\n    font-size: 18px;\\n  }\\n  .influencers-content[_ngcontent-%COMP%]   .featured-influencer[_ngcontent-%COMP%]   .influencer-card[_ngcontent-%COMP%]   .influencer-info[_ngcontent-%COMP%]   .influencer-stats[_ngcontent-%COMP%]   .stat-item[_ngcontent-%COMP%]   .stat-value[_ngcontent-%COMP%] {\\n    font-size: 14px;\\n  }\\n  .influencers-content[_ngcontent-%COMP%]   .other-influencers[_ngcontent-%COMP%]   .influencer-item[_ngcontent-%COMP%] {\\n    padding: 10px;\\n  }\\n  .influencers-content[_ngcontent-%COMP%]   .other-influencers[_ngcontent-%COMP%]   .influencer-item[_ngcontent-%COMP%]   .influencer-avatar-container[_ngcontent-%COMP%] {\\n    width: 45px;\\n    height: 45px;\\n    margin-right: 10px;\\n  }\\n  .influencers-content[_ngcontent-%COMP%]   .other-influencers[_ngcontent-%COMP%]   .influencer-item[_ngcontent-%COMP%]   .influencer-details[_ngcontent-%COMP%]   .follow-btn-small[_ngcontent-%COMP%] {\\n    padding: 5px 12px;\\n    font-size: 11px;\\n  }\\n  .influencers-content[_ngcontent-%COMP%]   .recent-posts[_ngcontent-%COMP%]   .posts-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));\\n    gap: 10px;\\n  }\\n  .influencers-content[_ngcontent-%COMP%]   .recent-posts[_ngcontent-%COMP%]   .posts-grid[_ngcontent-%COMP%]   .post-item[_ngcontent-%COMP%]   .post-image-container[_ngcontent-%COMP%] {\\n    height: 100px;\\n  }\\n  .influencers-content[_ngcontent-%COMP%]   .recent-posts[_ngcontent-%COMP%]   .posts-grid[_ngcontent-%COMP%]   .post-item[_ngcontent-%COMP%]   .post-author[_ngcontent-%COMP%] {\\n    padding: 6px;\\n  }\\n  .influencers-content[_ngcontent-%COMP%]   .recent-posts[_ngcontent-%COMP%]   .posts-grid[_ngcontent-%COMP%]   .post-item[_ngcontent-%COMP%]   .post-author[_ngcontent-%COMP%]   .author-name[_ngcontent-%COMP%] {\\n    font-size: 9px;\\n  }\\n  .influencers-content[_ngcontent-%COMP%]   .trending-categories[_ngcontent-%COMP%]   .categories-list[_ngcontent-%COMP%]   .category-tag[_ngcontent-%COMP%] {\\n    padding: 6px 10px;\\n    font-size: 11px;\\n  }\\n}\\n@media (max-width: 425px) {\\n  .influencers-content[_ngcontent-%COMP%]   .featured-influencer[_ngcontent-%COMP%]   .influencer-card[_ngcontent-%COMP%] {\\n    padding: 16px;\\n  }\\n  .influencers-content[_ngcontent-%COMP%]   .featured-influencer[_ngcontent-%COMP%]   .influencer-card[_ngcontent-%COMP%]   .influencer-avatar-container[_ngcontent-%COMP%] {\\n    width: 60px;\\n    height: 60px;\\n  }\\n  .influencers-content[_ngcontent-%COMP%]   .featured-influencer[_ngcontent-%COMP%]   .influencer-card[_ngcontent-%COMP%]   .influencer-info[_ngcontent-%COMP%]   .influencer-name[_ngcontent-%COMP%] {\\n    font-size: 16px;\\n  }\\n  .influencers-content[_ngcontent-%COMP%]   .featured-influencer[_ngcontent-%COMP%]   .influencer-card[_ngcontent-%COMP%]   .influencer-info[_ngcontent-%COMP%]   .influencer-bio[_ngcontent-%COMP%] {\\n    font-size: 12px;\\n  }\\n  .influencers-content[_ngcontent-%COMP%]   .featured-influencer[_ngcontent-%COMP%]   .influencer-card[_ngcontent-%COMP%]   .influencer-info[_ngcontent-%COMP%]   .influencer-stats[_ngcontent-%COMP%]   .stat-item[_ngcontent-%COMP%]   .stat-value[_ngcontent-%COMP%] {\\n    font-size: 13px;\\n  }\\n  .influencers-content[_ngcontent-%COMP%]   .featured-influencer[_ngcontent-%COMP%]   .influencer-card[_ngcontent-%COMP%]   .influencer-info[_ngcontent-%COMP%]   .influencer-stats[_ngcontent-%COMP%]   .stat-item[_ngcontent-%COMP%]   .stat-label[_ngcontent-%COMP%] {\\n    font-size: 10px;\\n  }\\n  .influencers-content[_ngcontent-%COMP%]   .featured-influencer[_ngcontent-%COMP%]   .influencer-card[_ngcontent-%COMP%]   .influencer-info[_ngcontent-%COMP%]   .follow-btn[_ngcontent-%COMP%] {\\n    padding: 6px 20px;\\n    font-size: 13px;\\n  }\\n  .influencers-content[_ngcontent-%COMP%]   .other-influencers[_ngcontent-%COMP%]   .influencer-item[_ngcontent-%COMP%] {\\n    padding: 8px;\\n  }\\n  .influencers-content[_ngcontent-%COMP%]   .other-influencers[_ngcontent-%COMP%]   .influencer-item[_ngcontent-%COMP%]   .influencer-avatar-container[_ngcontent-%COMP%] {\\n    width: 40px;\\n    height: 40px;\\n    margin-right: 8px;\\n  }\\n  .influencers-content[_ngcontent-%COMP%]   .other-influencers[_ngcontent-%COMP%]   .influencer-item[_ngcontent-%COMP%]   .influencer-details[_ngcontent-%COMP%]   .influencer-info[_ngcontent-%COMP%]   .influencer-name[_ngcontent-%COMP%] {\\n    font-size: 13px;\\n  }\\n  .influencers-content[_ngcontent-%COMP%]   .other-influencers[_ngcontent-%COMP%]   .influencer-item[_ngcontent-%COMP%]   .influencer-details[_ngcontent-%COMP%]   .influencer-info[_ngcontent-%COMP%]   .influencer-username[_ngcontent-%COMP%] {\\n    font-size: 11px;\\n  }\\n  .influencers-content[_ngcontent-%COMP%]   .other-influencers[_ngcontent-%COMP%]   .influencer-item[_ngcontent-%COMP%]   .influencer-details[_ngcontent-%COMP%]   .influencer-info[_ngcontent-%COMP%]   .influencer-metrics[_ngcontent-%COMP%]   .followers[_ngcontent-%COMP%], .influencers-content[_ngcontent-%COMP%]   .other-influencers[_ngcontent-%COMP%]   .influencer-item[_ngcontent-%COMP%]   .influencer-details[_ngcontent-%COMP%]   .influencer-info[_ngcontent-%COMP%]   .influencer-metrics[_ngcontent-%COMP%]   .engagement[_ngcontent-%COMP%] {\\n    font-size: 9px;\\n  }\\n  .influencers-content[_ngcontent-%COMP%]   .other-influencers[_ngcontent-%COMP%]   .influencer-item[_ngcontent-%COMP%]   .influencer-details[_ngcontent-%COMP%]   .follow-btn-small[_ngcontent-%COMP%] {\\n    padding: 4px 10px;\\n    font-size: 10px;\\n  }\\n  .influencers-content[_ngcontent-%COMP%]   .recent-posts[_ngcontent-%COMP%]   .posts-title[_ngcontent-%COMP%] {\\n    font-size: 14px;\\n  }\\n  .influencers-content[_ngcontent-%COMP%]   .recent-posts[_ngcontent-%COMP%]   .posts-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: repeat(auto-fit, minmax(80px, 1fr));\\n    gap: 8px;\\n  }\\n  .influencers-content[_ngcontent-%COMP%]   .recent-posts[_ngcontent-%COMP%]   .posts-grid[_ngcontent-%COMP%]   .post-item[_ngcontent-%COMP%]   .post-image-container[_ngcontent-%COMP%] {\\n    height: 80px;\\n  }\\n  .influencers-content[_ngcontent-%COMP%]   .recent-posts[_ngcontent-%COMP%]   .posts-grid[_ngcontent-%COMP%]   .post-item[_ngcontent-%COMP%]   .post-author[_ngcontent-%COMP%] {\\n    padding: 4px;\\n  }\\n  .influencers-content[_ngcontent-%COMP%]   .recent-posts[_ngcontent-%COMP%]   .posts-grid[_ngcontent-%COMP%]   .post-item[_ngcontent-%COMP%]   .post-author[_ngcontent-%COMP%]   .author-avatar[_ngcontent-%COMP%] {\\n    width: 16px;\\n    height: 16px;\\n  }\\n  .influencers-content[_ngcontent-%COMP%]   .recent-posts[_ngcontent-%COMP%]   .posts-grid[_ngcontent-%COMP%]   .post-item[_ngcontent-%COMP%]   .post-author[_ngcontent-%COMP%]   .author-name[_ngcontent-%COMP%] {\\n    font-size: 8px;\\n  }\\n  .influencers-content[_ngcontent-%COMP%]   .trending-categories[_ngcontent-%COMP%]   .categories-title[_ngcontent-%COMP%] {\\n    font-size: 14px;\\n  }\\n  .influencers-content[_ngcontent-%COMP%]   .trending-categories[_ngcontent-%COMP%]   .categories-list[_ngcontent-%COMP%]   .category-tag[_ngcontent-%COMP%] {\\n    padding: 5px 8px;\\n    font-size: 10px;\\n  }\\n  .influencers-content[_ngcontent-%COMP%]   .trending-categories[_ngcontent-%COMP%]   .categories-list[_ngcontent-%COMP%]   .category-tag[_ngcontent-%COMP%]   .category-count[_ngcontent-%COMP%] {\\n    font-size: 9px;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "RouterModule", "IonicModule", "Subscription", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵtext", "ɵɵelementEnd", "ɵɵlistener", "TrendingInfluencerHighlightsComponent_div_9_Template_button_click_4_listener", "ɵɵrestoreView", "_r1", "ctx_r1", "ɵɵnextContext", "ɵɵresetView", "onRetry", "ɵɵadvance", "ɵɵtextInterpolate", "error", "TrendingInfluencerHighlightsComponent_div_10_div_1_Template_div_click_0_listener", "_r3", "onInfluencerClick", "featuredInfluencer", "ɵɵtemplate", "TrendingInfluencerHighlightsComponent_div_10_div_1_div_4_Template", "TrendingInfluencerHighlightsComponent_div_10_div_1_Template_button_click_31_listener", "$event", "to<PERSON><PERSON><PERSON><PERSON>", "ɵɵproperty", "avatar", "ɵɵsanitizeUrl", "fullName", "isVerified", "username", "bio", "formatCount", "stats", "followers", "posts", "ɵɵtextInterpolate1", "engagementRate", "ɵɵclassProp", "isFollowing", "TrendingInfluencerHighlightsComponent_div_10_div_3_Template_div_click_0_listener", "influencer_r5", "_r4", "$implicit", "TrendingInfluencerHighlightsComponent_div_10_div_3_div_3_Template", "TrendingInfluencerHighlightsComponent_div_10_div_3_div_4_Template", "TrendingInfluencerHighlightsComponent_div_10_div_3_Template_button_click_16_listener", "isOnline", "TrendingInfluencerHighlightsComponent_div_10_div_4_div_4_Template_div_click_0_listener", "post_r7", "_r6", "onPostClick", "TrendingInfluencerHighlightsComponent_div_10_div_4_div_4_div_13_Template", "image", "caption", "likes", "comments", "type", "author", "TrendingInfluencerHighlightsComponent_div_10_div_4_div_4_Template", "recentPosts", "trackByPostId", "TrendingInfluencerHighlightsComponent_div_10_div_5_button_4_Template_button_click_0_listener", "category_r9", "_r8", "onCategoryClick", "name", "postCount", "TrendingInfluencerHighlightsComponent_div_10_div_5_button_4_Template", "trendingCategories", "trackByCategoryName", "TrendingInfluencerHighlightsComponent_div_10_div_1_Template", "TrendingInfluencerHighlightsComponent_div_10_div_3_Template", "TrendingInfluencerHighlightsComponent_div_10_div_4_Template", "TrendingInfluencerHighlightsComponent_div_10_div_5_Template", "otherInfluencers", "trackByInfluencerId", "length", "TrendingInfluencerHighlightsComponent", "constructor", "router", "trendingInfluencers", "isLoading", "subscription", "ngOnInit", "loadTrendingInfluencers", "ngOnDestroy", "unsubscribe", "_this", "_asyncToGenerator", "loadMockInfluencers", "console", "_this2", "Promise", "resolve", "setTimeout", "_id", "following", "categories", "createdAt", "Date", "reduce", "prev", "current", "filter", "inf", "color", "influencer", "navigate", "post", "category", "queryParams", "hashtag", "event", "stopPropagation", "count", "toFixed", "toString", "index", "onSeeAll", "preventDefault", "ɵɵdirectiveInject", "i1", "Router", "selectors", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "TrendingInfluencerHighlightsComponent_Template", "rf", "ctx", "TrendingInfluencerHighlightsComponent_Template_a_click_5_listener", "TrendingInfluencerHighlightsComponent_div_8_Template", "TrendingInfluencerHighlightsComponent_div_9_Template", "TrendingInfluencerHighlightsComponent_div_10_Template", "TrendingInfluencerHighlightsComponent_div_11_Template", "i2", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "i3", "IonIcon", "Ion<PERSON><PERSON><PERSON>", "styles"], "sources": ["E:\\Fashion\\DFashionFrontend\\frontend\\src\\app\\features\\home\\components\\trending-now\\trending-influencer-highlights\\trending-influencer-highlights.component.ts", "E:\\Fashion\\DFashionFrontend\\frontend\\src\\app\\features\\home\\components\\trending-now\\trending-influencer-highlights\\trending-influencer-highlights.component.html"], "sourcesContent": ["import { Component, OnInit, OnDestroy } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\nimport { IonicModule } from '@ionic/angular';\nimport { Router } from '@angular/router';\nimport { Subscription } from 'rxjs';\n\ninterface TrendingInfluencer {\n  _id: string;\n  username: string;\n  fullName: string;\n  avatar: string;\n  bio: string;\n  isVerified: boolean;\n  isFollowing: boolean;\n  isOnline: boolean;\n  stats: {\n    followers: number;\n    following: number;\n    posts: number;\n    engagementRate: number;\n  };\n  categories: string[];\n  createdAt: Date;\n}\n\ninterface TrendingPost {\n  _id: string;\n  image: string;\n  caption: string;\n  type: 'post' | 'reel';\n  likes: number;\n  comments: number;\n  author: {\n    _id: string;\n    username: string;\n    fullName: string;\n    avatar: string;\n  };\n  createdAt: Date;\n}\n\ninterface TrendingCategory {\n  name: string;\n  postCount: number;\n  color: string;\n}\n\n@Component({\n  selector: 'app-trending-influencer-highlights',\n  standalone: true,\n  imports: [\n    CommonModule,\n    RouterModule,\n    IonicModule\n  ],\n  templateUrl: './trending-influencer-highlights.component.html',\n  styleUrls: ['./trending-influencer-highlights.component.scss']\n})\nexport class TrendingInfluencerHighlightsComponent implements OnInit, OnDestroy {\n  trendingInfluencers: TrendingInfluencer[] = [];\n  featuredInfluencer: TrendingInfluencer | null = null;\n  otherInfluencers: TrendingInfluencer[] = [];\n  recentPosts: TrendingPost[] = [];\n  trendingCategories: TrendingCategory[] = [];\n  isLoading = true;\n  error: string | null = null;\n  private subscription: Subscription = new Subscription();\n\n  constructor(private router: Router) {}\n\n  ngOnInit() {\n    this.loadTrendingInfluencers();\n  }\n\n  ngOnDestroy() {\n    this.subscription.unsubscribe();\n  }\n\n  private async loadTrendingInfluencers() {\n    try {\n      this.isLoading = true;\n      this.error = null;\n      \n      // Mock data for now - replace with actual service call\n      await this.loadMockInfluencers();\n      \n    } catch (error) {\n      console.error('Error loading trending influencers:', error);\n      this.error = 'Failed to load trending influencers';\n      this.isLoading = false;\n    }\n  }\n\n  private async loadMockInfluencers() {\n    // Simulate API call delay\n    await new Promise(resolve => setTimeout(resolve, 900));\n\n    // Mock trending influencers\n    this.trendingInfluencers = [\n      {\n        _id: 'inf1',\n        username: 'fashionista_maya',\n        fullName: 'Maya Sharma',\n        avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b47c?w=150&h=150&fit=crop&crop=face',\n        bio: 'Fashion enthusiast | Style blogger | Trendsetter ✨',\n        isVerified: true,\n        isFollowing: false,\n        isOnline: true,\n        stats: {\n          followers: 245000,\n          following: 1200,\n          posts: 890,\n          engagementRate: 8.5\n        },\n        categories: ['fashion', 'lifestyle', 'beauty'],\n        createdAt: new Date()\n      },\n      {\n        _id: 'inf2',\n        username: 'style_guru_raj',\n        fullName: 'Raj Patel',\n        avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face',\n        bio: 'Menswear expert | Fashion consultant | Style tips daily 👔',\n        isVerified: true,\n        isFollowing: true,\n        isOnline: false,\n        stats: {\n          followers: 189000,\n          following: 890,\n          posts: 567,\n          engagementRate: 7.2\n        },\n        categories: ['menswear', 'fashion', 'lifestyle'],\n        createdAt: new Date()\n      },\n      {\n        _id: 'inf3',\n        username: 'trendy_priya',\n        fullName: 'Priya Singh',\n        avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face',\n        bio: 'Affordable fashion | Budget styling | Fashion for all 💫',\n        isVerified: false,\n        isFollowing: false,\n        isOnline: true,\n        stats: {\n          followers: 156000,\n          following: 2300,\n          posts: 1234,\n          engagementRate: 9.1\n        },\n        categories: ['budget-fashion', 'styling', 'accessories'],\n        createdAt: new Date()\n      },\n      {\n        _id: 'inf4',\n        username: 'luxury_lifestyle_arjun',\n        fullName: 'Arjun Mehta',\n        avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face',\n        bio: 'Luxury fashion | High-end styling | Designer collections 🔥',\n        isVerified: true,\n        isFollowing: false,\n        isOnline: false,\n        stats: {\n          followers: 298000,\n          following: 567,\n          posts: 345,\n          engagementRate: 6.8\n        },\n        categories: ['luxury', 'designer', 'menswear'],\n        createdAt: new Date()\n      }\n    ];\n\n    // Set featured influencer (highest engagement rate)\n    this.featuredInfluencer = this.trendingInfluencers.reduce((prev, current) => \n      (prev.stats.engagementRate > current.stats.engagementRate) ? prev : current\n    );\n\n    // Set other influencers (excluding featured)\n    this.otherInfluencers = this.trendingInfluencers.filter(inf => inf._id !== this.featuredInfluencer?._id);\n\n    // Mock recent posts\n    this.recentPosts = [\n      {\n        _id: 'post1',\n        image: 'https://images.unsplash.com/photo-1515372039744-b8f02a3ae446?w=300&h=300&fit=crop',\n        caption: 'Summer vibes with this amazing dress! 🌞',\n        type: 'post',\n        likes: 2340,\n        comments: 156,\n        author: this.featuredInfluencer!,\n        createdAt: new Date()\n      },\n      {\n        _id: 'post2',\n        image: 'https://images.unsplash.com/photo-1441986300917-64674bd600d8?w=300&h=300&fit=crop',\n        caption: 'Perfect outfit for a casual day out',\n        type: 'reel',\n        likes: 1890,\n        comments: 89,\n        author: this.otherInfluencers[0],\n        createdAt: new Date()\n      },\n      {\n        _id: 'post3',\n        image: 'https://images.unsplash.com/photo-1445205170230-053b83016050?w=300&h=300&fit=crop',\n        caption: 'Styling tips for the modern woman',\n        type: 'post',\n        likes: 3456,\n        comments: 234,\n        author: this.otherInfluencers[1],\n        createdAt: new Date()\n      },\n      {\n        _id: 'post4',\n        image: 'https://images.unsplash.com/photo-1542291026-7eec264c27ff?w=300&h=300&fit=crop',\n        caption: 'Sneaker game strong! 👟',\n        type: 'reel',\n        likes: 2789,\n        comments: 167,\n        author: this.otherInfluencers[2],\n        createdAt: new Date()\n      }\n    ];\n\n    // Mock trending categories\n    this.trendingCategories = [\n      { name: 'SummerFashion', postCount: 12500, color: '#ff6b6b' },\n      { name: 'StreetStyle', postCount: 8900, color: '#4ecdc4' },\n      { name: 'Minimalist', postCount: 6700, color: '#45b7d1' },\n      { name: 'Vintage', postCount: 5400, color: '#f9ca24' },\n      { name: 'Sustainable', postCount: 4200, color: '#6c5ce7' }\n    ];\n\n    this.isLoading = false;\n  }\n\n  onInfluencerClick(influencer: TrendingInfluencer) {\n    this.router.navigate(['/profile', influencer.username]);\n  }\n\n  onPostClick(post: TrendingPost) {\n    this.router.navigate(['/post', post._id]);\n  }\n\n  onCategoryClick(category: TrendingCategory) {\n    this.router.navigate(['/explore'], {\n      queryParams: { hashtag: category.name }\n    });\n  }\n\n  toggleFollow(influencer: TrendingInfluencer, event: Event) {\n    event.stopPropagation();\n    \n    influencer.isFollowing = !influencer.isFollowing;\n    if (influencer.isFollowing) {\n      influencer.stats.followers++;\n    } else {\n      influencer.stats.followers--;\n    }\n  }\n\n  formatCount(count: number): string {\n    if (count >= 1000000) {\n      return (count / 1000000).toFixed(1) + 'M';\n    } else if (count >= 1000) {\n      return (count / 1000).toFixed(1) + 'K';\n    }\n    return count.toString();\n  }\n\n  trackByInfluencerId(index: number, influencer: TrendingInfluencer): string {\n    return influencer._id;\n  }\n\n  trackByPostId(index: number, post: TrendingPost): string {\n    return post._id;\n  }\n\n  trackByCategoryName(index: number, category: TrendingCategory): string {\n    return category.name;\n  }\n\n  onSeeAll(event: Event) {\n    event.preventDefault();\n    this.router.navigate(['/trending/influencers']);\n  }\n\n  onRetry() {\n    this.loadTrendingInfluencers();\n  }\n}\n", "<div class=\"component-container\">\n  <div class=\"component-header\">\n    <h3>\n      <ion-icon name=\"people-outline\"></ion-icon>\n      Trending Influencers\n    </h3>\n    <a href=\"#\" class=\"see-all\" (click)=\"onSeeAll($event)\">See All</a>\n  </div>\n\n  <div class=\"component-content\">\n    <div *ngIf=\"isLoading\" class=\"loading-container\">\n      <ion-spinner name=\"crescent\"></ion-spinner>\n      <p>Loading trending influencers...</p>\n    </div>\n\n    <div *ngIf=\"error && !isLoading\" class=\"error-container\">\n      <ion-icon name=\"alert-circle-outline\"></ion-icon>\n      <p>{{ error }}</p>\n      <button class=\"retry-btn\" (click)=\"onRetry()\">Try Again</button>\n    </div>\n\n    <div *ngIf=\"!isLoading && !error && trendingInfluencers.length > 0\" class=\"influencers-content\">\n      <div class=\"featured-influencer\" *ngIf=\"featuredInfluencer\" (click)=\"onInfluencerClick(featuredInfluencer)\">\n        <div class=\"influencer-card\">\n          <div class=\"influencer-avatar-container\">\n            <img [src]=\"featuredInfluencer.avatar\" [alt]=\"featuredInfluencer.fullName\" class=\"influencer-avatar\">\n            <div class=\"verified-badge\" *ngIf=\"featuredInfluencer.isVerified\">\n              <ion-icon name=\"checkmark-circle\"></ion-icon>\n            </div>\n            <div class=\"trending-badge\">\n              <ion-icon name=\"flame\"></ion-icon>\n              Trending\n            </div>\n          </div>\n          <div class=\"influencer-info\">\n            <h4 class=\"influencer-name\">{{ featuredInfluencer.fullName }}</h4>\n            <p class=\"influencer-username\">{{ '@' + featuredInfluencer.username }}</p>\n            <p class=\"influencer-bio\">{{ featuredInfluencer.bio }}</p>\n            <div class=\"influencer-stats\">\n              <div class=\"stat-item\">\n                <span class=\"stat-value\">{{ formatCount(featuredInfluencer.stats.followers) }}</span>\n                <span class=\"stat-label\">Followers</span>\n              </div>\n              <div class=\"stat-item\">\n                <span class=\"stat-value\">{{ formatCount(featuredInfluencer.stats.posts) }}</span>\n                <span class=\"stat-label\">Posts</span>\n              </div>\n              <div class=\"stat-item\">\n                <span class=\"stat-value\">{{ featuredInfluencer.stats.engagementRate }}%</span>\n                <span class=\"stat-label\">Engagement</span>\n              </div>\n            </div>\n            <button class=\"follow-btn\" [class.following]=\"featuredInfluencer.isFollowing\" (click)=\"toggleFollow(featuredInfluencer, $event)\">\n              {{ featuredInfluencer.isFollowing ? 'Following' : 'Follow' }}\n            </button>\n          </div>\n        </div>\n      </div>\n\n      <div class=\"other-influencers\">\n        <div \n          *ngFor=\"let influencer of otherInfluencers; trackBy: trackByInfluencerId\" \n          class=\"influencer-item\"\n          (click)=\"onInfluencerClick(influencer)\"\n        >\n          <div class=\"influencer-avatar-container\">\n            <img [src]=\"influencer.avatar\" [alt]=\"influencer.fullName\" class=\"influencer-avatar\">\n            <div class=\"verified-badge\" *ngIf=\"influencer.isVerified\">\n              <ion-icon name=\"checkmark-circle\"></ion-icon>\n            </div>\n            <div class=\"online-indicator\" *ngIf=\"influencer.isOnline\"></div>\n          </div>\n          <div class=\"influencer-details\">\n            <div class=\"influencer-info\">\n              <h5 class=\"influencer-name\">{{ influencer.fullName }}</h5>\n              <p class=\"influencer-username\">{{ '@' + influencer.username }}</p>\n              <div class=\"influencer-metrics\">\n                <span class=\"followers\">{{ formatCount(influencer.stats.followers) }} followers</span>\n                <span class=\"engagement\">{{ influencer.stats.engagementRate }}% engagement</span>\n              </div>\n            </div>\n            <button \n              class=\"follow-btn-small\" \n              [class.following]=\"influencer.isFollowing\"\n              (click)=\"toggleFollow(influencer, $event)\"\n            >\n              {{ influencer.isFollowing ? 'Following' : 'Follow' }}\n            </button>\n          </div>\n        </div>\n      </div>\n\n      <div class=\"recent-posts\" *ngIf=\"recentPosts.length > 0\">\n        <h4 class=\"posts-title\">Recent Trending Posts</h4>\n        <div class=\"posts-grid\">\n          <div \n            *ngFor=\"let post of recentPosts; trackBy: trackByPostId\" \n            class=\"post-item\"\n            (click)=\"onPostClick(post)\"\n          >\n            <div class=\"post-image-container\">\n              <img [src]=\"post.image\" [alt]=\"post.caption\" class=\"post-image\">\n              <div class=\"post-overlay\">\n                <div class=\"post-stats\">\n                  <div class=\"stat\">\n                    <ion-icon name=\"heart\"></ion-icon>\n                    <span>{{ formatCount(post.likes) }}</span>\n                  </div>\n                  <div class=\"stat\">\n                    <ion-icon name=\"chatbubble\"></ion-icon>\n                    <span>{{ formatCount(post.comments) }}</span>\n                  </div>\n                </div>\n              </div>\n              <div class=\"post-type-badge\" *ngIf=\"post.type === 'reel'\">\n                <ion-icon name=\"play\"></ion-icon>\n              </div>\n            </div>\n            <div class=\"post-author\">\n              <img [src]=\"post.author.avatar\" [alt]=\"post.author.fullName\" class=\"author-avatar\">\n              <span class=\"author-name\">{{ post.author.fullName }}</span>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <div class=\"trending-categories\" *ngIf=\"trendingCategories.length > 0\">\n        <h4 class=\"categories-title\">Trending Categories</h4>\n        <div class=\"categories-list\">\n          <button \n            *ngFor=\"let category of trendingCategories; trackBy: trackByCategoryName\"\n            class=\"category-tag\"\n            (click)=\"onCategoryClick(category)\"\n          >\n            #{{ category.name }}\n            <span class=\"category-count\">{{ formatCount(category.postCount) }}</span>\n          </button>\n        </div>\n      </div>\n    </div>\n\n    <div *ngIf=\"!isLoading && !error && trendingInfluencers.length === 0\" class=\"empty-container\">\n      <ion-icon name=\"people-outline\"></ion-icon>\n      <p>No trending influencers available</p>\n    </div>\n  </div>\n</div>\n"], "mappings": ";AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,QAAQ,gBAAgB;AAE5C,SAASC,YAAY,QAAQ,MAAM;;;;;;;ICK/BC,EAAA,CAAAC,cAAA,aAAiD;IAC/CD,EAAA,CAAAE,SAAA,sBAA2C;IAC3CF,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAG,MAAA,sCAA+B;IACpCH,EADoC,CAAAI,YAAA,EAAI,EAClC;;;;;;IAENJ,EAAA,CAAAC,cAAA,cAAyD;IACvDD,EAAA,CAAAE,SAAA,mBAAiD;IACjDF,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAG,MAAA,GAAW;IAAAH,EAAA,CAAAI,YAAA,EAAI;IAClBJ,EAAA,CAAAC,cAAA,iBAA8C;IAApBD,EAAA,CAAAK,UAAA,mBAAAC,6EAAA;MAAAN,EAAA,CAAAO,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAG,OAAA,EAAS;IAAA,EAAC;IAACZ,EAAA,CAAAG,MAAA,gBAAS;IACzDH,EADyD,CAAAI,YAAA,EAAS,EAC5D;;;;IAFDJ,EAAA,CAAAa,SAAA,GAAW;IAAXb,EAAA,CAAAc,iBAAA,CAAAL,MAAA,CAAAM,KAAA,CAAW;;;;;IASRf,EAAA,CAAAC,cAAA,cAAkE;IAChED,EAAA,CAAAE,SAAA,mBAA6C;IAC/CF,EAAA,CAAAI,YAAA,EAAM;;;;;;IANZJ,EAAA,CAAAC,cAAA,cAA4G;IAAhDD,EAAA,CAAAK,UAAA,mBAAAW,iFAAA;MAAAhB,EAAA,CAAAO,aAAA,CAAAU,GAAA;MAAA,MAAAR,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAS,iBAAA,CAAAT,MAAA,CAAAU,kBAAA,CAAqC;IAAA,EAAC;IAEvGnB,EADF,CAAAC,cAAA,cAA6B,cACc;IACvCD,EAAA,CAAAE,SAAA,cAAqG;IACrGF,EAAA,CAAAoB,UAAA,IAAAC,iEAAA,kBAAkE;IAGlErB,EAAA,CAAAC,cAAA,cAA4B;IAC1BD,EAAA,CAAAE,SAAA,mBAAkC;IAClCF,EAAA,CAAAG,MAAA,iBACF;IACFH,EADE,CAAAI,YAAA,EAAM,EACF;IAEJJ,EADF,CAAAC,cAAA,cAA6B,aACC;IAAAD,EAAA,CAAAG,MAAA,IAAiC;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAClEJ,EAAA,CAAAC,cAAA,aAA+B;IAAAD,EAAA,CAAAG,MAAA,IAAuC;IAAAH,EAAA,CAAAI,YAAA,EAAI;IAC1EJ,EAAA,CAAAC,cAAA,aAA0B;IAAAD,EAAA,CAAAG,MAAA,IAA4B;IAAAH,EAAA,CAAAI,YAAA,EAAI;IAGtDJ,EAFJ,CAAAC,cAAA,eAA8B,eACL,gBACI;IAAAD,EAAA,CAAAG,MAAA,IAAqD;IAAAH,EAAA,CAAAI,YAAA,EAAO;IACrFJ,EAAA,CAAAC,cAAA,gBAAyB;IAAAD,EAAA,CAAAG,MAAA,iBAAS;IACpCH,EADoC,CAAAI,YAAA,EAAO,EACrC;IAEJJ,EADF,CAAAC,cAAA,eAAuB,gBACI;IAAAD,EAAA,CAAAG,MAAA,IAAiD;IAAAH,EAAA,CAAAI,YAAA,EAAO;IACjFJ,EAAA,CAAAC,cAAA,gBAAyB;IAAAD,EAAA,CAAAG,MAAA,aAAK;IAChCH,EADgC,CAAAI,YAAA,EAAO,EACjC;IAEJJ,EADF,CAAAC,cAAA,eAAuB,gBACI;IAAAD,EAAA,CAAAG,MAAA,IAA8C;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAC9EJ,EAAA,CAAAC,cAAA,gBAAyB;IAAAD,EAAA,CAAAG,MAAA,kBAAU;IAEvCH,EAFuC,CAAAI,YAAA,EAAO,EACtC,EACF;IACNJ,EAAA,CAAAC,cAAA,kBAAiI;IAAnDD,EAAA,CAAAK,UAAA,mBAAAiB,qFAAAC,MAAA;MAAAvB,EAAA,CAAAO,aAAA,CAAAU,GAAA;MAAA,MAAAR,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAe,YAAA,CAAAf,MAAA,CAAAU,kBAAA,EAAAI,MAAA,CAAwC;IAAA,EAAC;IAC9HvB,EAAA,CAAAG,MAAA,IACF;IAGNH,EAHM,CAAAI,YAAA,EAAS,EACL,EACF,EACF;;;;IAhCKJ,EAAA,CAAAa,SAAA,GAAiC;IAACb,EAAlC,CAAAyB,UAAA,QAAAhB,MAAA,CAAAU,kBAAA,CAAAO,MAAA,EAAA1B,EAAA,CAAA2B,aAAA,CAAiC,QAAAlB,MAAA,CAAAU,kBAAA,CAAAS,QAAA,CAAoC;IAC7C5B,EAAA,CAAAa,SAAA,EAAmC;IAAnCb,EAAA,CAAAyB,UAAA,SAAAhB,MAAA,CAAAU,kBAAA,CAAAU,UAAA,CAAmC;IASpC7B,EAAA,CAAAa,SAAA,GAAiC;IAAjCb,EAAA,CAAAc,iBAAA,CAAAL,MAAA,CAAAU,kBAAA,CAAAS,QAAA,CAAiC;IAC9B5B,EAAA,CAAAa,SAAA,GAAuC;IAAvCb,EAAA,CAAAc,iBAAA,OAAAL,MAAA,CAAAU,kBAAA,CAAAW,QAAA,CAAuC;IAC5C9B,EAAA,CAAAa,SAAA,GAA4B;IAA5Bb,EAAA,CAAAc,iBAAA,CAAAL,MAAA,CAAAU,kBAAA,CAAAY,GAAA,CAA4B;IAGzB/B,EAAA,CAAAa,SAAA,GAAqD;IAArDb,EAAA,CAAAc,iBAAA,CAAAL,MAAA,CAAAuB,WAAA,CAAAvB,MAAA,CAAAU,kBAAA,CAAAc,KAAA,CAAAC,SAAA,EAAqD;IAIrDlC,EAAA,CAAAa,SAAA,GAAiD;IAAjDb,EAAA,CAAAc,iBAAA,CAAAL,MAAA,CAAAuB,WAAA,CAAAvB,MAAA,CAAAU,kBAAA,CAAAc,KAAA,CAAAE,KAAA,EAAiD;IAIjDnC,EAAA,CAAAa,SAAA,GAA8C;IAA9Cb,EAAA,CAAAoC,kBAAA,KAAA3B,MAAA,CAAAU,kBAAA,CAAAc,KAAA,CAAAI,cAAA,MAA8C;IAIhDrC,EAAA,CAAAa,SAAA,GAAkD;IAAlDb,EAAA,CAAAsC,WAAA,cAAA7B,MAAA,CAAAU,kBAAA,CAAAoB,WAAA,CAAkD;IAC3EvC,EAAA,CAAAa,SAAA,EACF;IADEb,EAAA,CAAAoC,kBAAA,MAAA3B,MAAA,CAAAU,kBAAA,CAAAoB,WAAA,+BACF;;;;;IAaAvC,EAAA,CAAAC,cAAA,cAA0D;IACxDD,EAAA,CAAAE,SAAA,mBAA6C;IAC/CF,EAAA,CAAAI,YAAA,EAAM;;;;;IACNJ,EAAA,CAAAE,SAAA,cAAgE;;;;;;IAVpEF,EAAA,CAAAC,cAAA,cAIC;IADCD,EAAA,CAAAK,UAAA,mBAAAmC,iFAAA;MAAA,MAAAC,aAAA,GAAAzC,EAAA,CAAAO,aAAA,CAAAmC,GAAA,EAAAC,SAAA;MAAA,MAAAlC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAS,iBAAA,CAAAuB,aAAA,CAA6B;IAAA,EAAC;IAEvCzC,EAAA,CAAAC,cAAA,cAAyC;IACvCD,EAAA,CAAAE,SAAA,cAAqF;IAIrFF,EAHA,CAAAoB,UAAA,IAAAwB,iEAAA,kBAA0D,IAAAC,iEAAA,kBAGA;IAC5D7C,EAAA,CAAAI,YAAA,EAAM;IAGFJ,EAFJ,CAAAC,cAAA,cAAgC,cACD,aACC;IAAAD,EAAA,CAAAG,MAAA,GAAyB;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAC1DJ,EAAA,CAAAC,cAAA,YAA+B;IAAAD,EAAA,CAAAG,MAAA,IAA+B;IAAAH,EAAA,CAAAI,YAAA,EAAI;IAEhEJ,EADF,CAAAC,cAAA,eAAgC,gBACN;IAAAD,EAAA,CAAAG,MAAA,IAAuD;IAAAH,EAAA,CAAAI,YAAA,EAAO;IACtFJ,EAAA,CAAAC,cAAA,gBAAyB;IAAAD,EAAA,CAAAG,MAAA,IAAiD;IAE9EH,EAF8E,CAAAI,YAAA,EAAO,EAC7E,EACF;IACNJ,EAAA,CAAAC,cAAA,kBAIC;IADCD,EAAA,CAAAK,UAAA,mBAAAyC,qFAAAvB,MAAA;MAAA,MAAAkB,aAAA,GAAAzC,EAAA,CAAAO,aAAA,CAAAmC,GAAA,EAAAC,SAAA;MAAA,MAAAlC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAe,YAAA,CAAAiB,aAAA,EAAAlB,MAAA,CAAgC;IAAA,EAAC;IAE1CvB,EAAA,CAAAG,MAAA,IACF;IAEJH,EAFI,CAAAI,YAAA,EAAS,EACL,EACF;;;;;IAvBGJ,EAAA,CAAAa,SAAA,GAAyB;IAACb,EAA1B,CAAAyB,UAAA,QAAAgB,aAAA,CAAAf,MAAA,EAAA1B,EAAA,CAAA2B,aAAA,CAAyB,QAAAc,aAAA,CAAAb,QAAA,CAA4B;IAC7B5B,EAAA,CAAAa,SAAA,EAA2B;IAA3Bb,EAAA,CAAAyB,UAAA,SAAAgB,aAAA,CAAAZ,UAAA,CAA2B;IAGzB7B,EAAA,CAAAa,SAAA,EAAyB;IAAzBb,EAAA,CAAAyB,UAAA,SAAAgB,aAAA,CAAAM,QAAA,CAAyB;IAI1B/C,EAAA,CAAAa,SAAA,GAAyB;IAAzBb,EAAA,CAAAc,iBAAA,CAAA2B,aAAA,CAAAb,QAAA,CAAyB;IACtB5B,EAAA,CAAAa,SAAA,GAA+B;IAA/Bb,EAAA,CAAAc,iBAAA,OAAA2B,aAAA,CAAAX,QAAA,CAA+B;IAEpC9B,EAAA,CAAAa,SAAA,GAAuD;IAAvDb,EAAA,CAAAoC,kBAAA,KAAA3B,MAAA,CAAAuB,WAAA,CAAAS,aAAA,CAAAR,KAAA,CAAAC,SAAA,gBAAuD;IACtDlC,EAAA,CAAAa,SAAA,GAAiD;IAAjDb,EAAA,CAAAoC,kBAAA,KAAAK,aAAA,CAAAR,KAAA,CAAAI,cAAA,iBAAiD;IAK5ErC,EAAA,CAAAa,SAAA,EAA0C;IAA1Cb,EAAA,CAAAsC,WAAA,cAAAG,aAAA,CAAAF,WAAA,CAA0C;IAG1CvC,EAAA,CAAAa,SAAA,EACF;IADEb,EAAA,CAAAoC,kBAAA,MAAAK,aAAA,CAAAF,WAAA,+BACF;;;;;IA2BEvC,EAAA,CAAAC,cAAA,cAA0D;IACxDD,EAAA,CAAAE,SAAA,mBAAiC;IACnCF,EAAA,CAAAI,YAAA,EAAM;;;;;;IArBVJ,EAAA,CAAAC,cAAA,cAIC;IADCD,EAAA,CAAAK,UAAA,mBAAA2C,uFAAA;MAAA,MAAAC,OAAA,GAAAjD,EAAA,CAAAO,aAAA,CAAA2C,GAAA,EAAAP,SAAA;MAAA,MAAAlC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAA0C,WAAA,CAAAF,OAAA,CAAiB;IAAA,EAAC;IAE3BjD,EAAA,CAAAC,cAAA,cAAkC;IAChCD,EAAA,CAAAE,SAAA,cAAgE;IAG5DF,EAFJ,CAAAC,cAAA,cAA0B,cACA,cACJ;IAChBD,EAAA,CAAAE,SAAA,mBAAkC;IAClCF,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAG,MAAA,GAA6B;IACrCH,EADqC,CAAAI,YAAA,EAAO,EACtC;IACNJ,EAAA,CAAAC,cAAA,cAAkB;IAChBD,EAAA,CAAAE,SAAA,oBAAuC;IACvCF,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAG,MAAA,IAAgC;IAG5CH,EAH4C,CAAAI,YAAA,EAAO,EACzC,EACF,EACF;IACNJ,EAAA,CAAAoB,UAAA,KAAAgC,wEAAA,kBAA0D;IAG5DpD,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAC,cAAA,eAAyB;IACvBD,EAAA,CAAAE,SAAA,eAAmF;IACnFF,EAAA,CAAAC,cAAA,gBAA0B;IAAAD,EAAA,CAAAG,MAAA,IAA0B;IAExDH,EAFwD,CAAAI,YAAA,EAAO,EACvD,EACF;;;;;IArBGJ,EAAA,CAAAa,SAAA,GAAkB;IAACb,EAAnB,CAAAyB,UAAA,QAAAwB,OAAA,CAAAI,KAAA,EAAArD,EAAA,CAAA2B,aAAA,CAAkB,QAAAsB,OAAA,CAAAK,OAAA,CAAqB;IAKhCtD,EAAA,CAAAa,SAAA,GAA6B;IAA7Bb,EAAA,CAAAc,iBAAA,CAAAL,MAAA,CAAAuB,WAAA,CAAAiB,OAAA,CAAAM,KAAA,EAA6B;IAI7BvD,EAAA,CAAAa,SAAA,GAAgC;IAAhCb,EAAA,CAAAc,iBAAA,CAAAL,MAAA,CAAAuB,WAAA,CAAAiB,OAAA,CAAAO,QAAA,EAAgC;IAIdxD,EAAA,CAAAa,SAAA,EAA0B;IAA1Bb,EAAA,CAAAyB,UAAA,SAAAwB,OAAA,CAAAQ,IAAA,YAA0B;IAKnDzD,EAAA,CAAAa,SAAA,GAA0B;IAACb,EAA3B,CAAAyB,UAAA,QAAAwB,OAAA,CAAAS,MAAA,CAAAhC,MAAA,EAAA1B,EAAA,CAAA2B,aAAA,CAA0B,QAAAsB,OAAA,CAAAS,MAAA,CAAA9B,QAAA,CAA6B;IAClC5B,EAAA,CAAAa,SAAA,GAA0B;IAA1Bb,EAAA,CAAAc,iBAAA,CAAAmC,OAAA,CAAAS,MAAA,CAAA9B,QAAA,CAA0B;;;;;IA3B1D5B,EADF,CAAAC,cAAA,cAAyD,aAC/B;IAAAD,EAAA,CAAAG,MAAA,4BAAqB;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAClDJ,EAAA,CAAAC,cAAA,cAAwB;IACtBD,EAAA,CAAAoB,UAAA,IAAAuC,iEAAA,mBAIC;IAyBL3D,EADE,CAAAI,YAAA,EAAM,EACF;;;;IA5BiBJ,EAAA,CAAAa,SAAA,GAAgB;IAAAb,EAAhB,CAAAyB,UAAA,YAAAhB,MAAA,CAAAmD,WAAA,CAAgB,iBAAAnD,MAAA,CAAAoD,aAAA,CAAsB;;;;;;IAiCzD7D,EAAA,CAAAC,cAAA,iBAIC;IADCD,EAAA,CAAAK,UAAA,mBAAAyD,6FAAA;MAAA,MAAAC,WAAA,GAAA/D,EAAA,CAAAO,aAAA,CAAAyD,GAAA,EAAArB,SAAA;MAAA,MAAAlC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAwD,eAAA,CAAAF,WAAA,CAAyB;IAAA,EAAC;IAEnC/D,EAAA,CAAAG,MAAA,GACA;IAAAH,EAAA,CAAAC,cAAA,eAA6B;IAAAD,EAAA,CAAAG,MAAA,GAAqC;IACpEH,EADoE,CAAAI,YAAA,EAAO,EAClE;;;;;IAFPJ,EAAA,CAAAa,SAAA,EACA;IADAb,EAAA,CAAAoC,kBAAA,OAAA2B,WAAA,CAAAG,IAAA,MACA;IAA6BlE,EAAA,CAAAa,SAAA,GAAqC;IAArCb,EAAA,CAAAc,iBAAA,CAAAL,MAAA,CAAAuB,WAAA,CAAA+B,WAAA,CAAAI,SAAA,EAAqC;;;;;IARtEnE,EADF,CAAAC,cAAA,cAAuE,aACxC;IAAAD,EAAA,CAAAG,MAAA,0BAAmB;IAAAH,EAAA,CAAAI,YAAA,EAAK;IACrDJ,EAAA,CAAAC,cAAA,cAA6B;IAC3BD,EAAA,CAAAoB,UAAA,IAAAgD,oEAAA,qBAIC;IAKLpE,EADE,CAAAI,YAAA,EAAM,EACF;;;;IARqBJ,EAAA,CAAAa,SAAA,GAAuB;IAAAb,EAAvB,CAAAyB,UAAA,YAAAhB,MAAA,CAAA4D,kBAAA,CAAuB,iBAAA5D,MAAA,CAAA6D,mBAAA,CAA4B;;;;;IA7GhFtE,EAAA,CAAAC,cAAA,cAAgG;IAC9FD,EAAA,CAAAoB,UAAA,IAAAmD,2DAAA,oBAA4G;IAqC5GvE,EAAA,CAAAC,cAAA,cAA+B;IAC7BD,EAAA,CAAAoB,UAAA,IAAAoD,2DAAA,oBAIC;IA0BHxE,EAAA,CAAAI,YAAA,EAAM;IAoCNJ,EAlCA,CAAAoB,UAAA,IAAAqD,2DAAA,kBAAyD,IAAAC,2DAAA,kBAkCc;IAazE1E,EAAA,CAAAI,YAAA,EAAM;;;;IArH8BJ,EAAA,CAAAa,SAAA,EAAwB;IAAxBb,EAAA,CAAAyB,UAAA,SAAAhB,MAAA,CAAAU,kBAAA,CAAwB;IAuC/BnB,EAAA,CAAAa,SAAA,GAAqB;IAAAb,EAArB,CAAAyB,UAAA,YAAAhB,MAAA,CAAAkE,gBAAA,CAAqB,iBAAAlE,MAAA,CAAAmE,mBAAA,CAA4B;IA+BjD5E,EAAA,CAAAa,SAAA,EAA4B;IAA5Bb,EAAA,CAAAyB,UAAA,SAAAhB,MAAA,CAAAmD,WAAA,CAAAiB,MAAA,KAA4B;IAkCrB7E,EAAA,CAAAa,SAAA,EAAmC;IAAnCb,EAAA,CAAAyB,UAAA,SAAAhB,MAAA,CAAA4D,kBAAA,CAAAQ,MAAA,KAAmC;;;;;IAevE7E,EAAA,CAAAC,cAAA,cAA8F;IAC5FD,EAAA,CAAAE,SAAA,kBAA2C;IAC3CF,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAG,MAAA,wCAAiC;IACtCH,EADsC,CAAAI,YAAA,EAAI,EACpC;;;ADrFV,OAAM,MAAO0E,qCAAqC;EAUhDC,YAAoBC,MAAc;IAAd,KAAAA,MAAM,GAANA,MAAM;IAT1B,KAAAC,mBAAmB,GAAyB,EAAE;IAC9C,KAAA9D,kBAAkB,GAA8B,IAAI;IACpD,KAAAwD,gBAAgB,GAAyB,EAAE;IAC3C,KAAAf,WAAW,GAAmB,EAAE;IAChC,KAAAS,kBAAkB,GAAuB,EAAE;IAC3C,KAAAa,SAAS,GAAG,IAAI;IAChB,KAAAnE,KAAK,GAAkB,IAAI;IACnB,KAAAoE,YAAY,GAAiB,IAAIpF,YAAY,EAAE;EAElB;EAErCqF,QAAQA,CAAA;IACN,IAAI,CAACC,uBAAuB,EAAE;EAChC;EAEAC,WAAWA,CAAA;IACT,IAAI,CAACH,YAAY,CAACI,WAAW,EAAE;EACjC;EAEcF,uBAAuBA,CAAA;IAAA,IAAAG,KAAA;IAAA,OAAAC,iBAAA;MACnC,IAAI;QACFD,KAAI,CAACN,SAAS,GAAG,IAAI;QACrBM,KAAI,CAACzE,KAAK,GAAG,IAAI;QAEjB;QACA,MAAMyE,KAAI,CAACE,mBAAmB,EAAE;OAEjC,CAAC,OAAO3E,KAAK,EAAE;QACd4E,OAAO,CAAC5E,KAAK,CAAC,qCAAqC,EAAEA,KAAK,CAAC;QAC3DyE,KAAI,CAACzE,KAAK,GAAG,qCAAqC;QAClDyE,KAAI,CAACN,SAAS,GAAG,KAAK;;IACvB;EACH;EAEcQ,mBAAmBA,CAAA;IAAA,IAAAE,MAAA;IAAA,OAAAH,iBAAA;MAC/B;MACA,MAAM,IAAII,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,GAAG,CAAC,CAAC;MAEtD;MACAF,MAAI,CAACX,mBAAmB,GAAG,CACzB;QACEe,GAAG,EAAE,MAAM;QACXlE,QAAQ,EAAE,kBAAkB;QAC5BF,QAAQ,EAAE,aAAa;QACvBF,MAAM,EAAE,6FAA6F;QACrGK,GAAG,EAAE,oDAAoD;QACzDF,UAAU,EAAE,IAAI;QAChBU,WAAW,EAAE,KAAK;QAClBQ,QAAQ,EAAE,IAAI;QACdd,KAAK,EAAE;UACLC,SAAS,EAAE,MAAM;UACjB+D,SAAS,EAAE,IAAI;UACf9D,KAAK,EAAE,GAAG;UACVE,cAAc,EAAE;SACjB;QACD6D,UAAU,EAAE,CAAC,SAAS,EAAE,WAAW,EAAE,QAAQ,CAAC;QAC9CC,SAAS,EAAE,IAAIC,IAAI;OACpB,EACD;QACEJ,GAAG,EAAE,MAAM;QACXlE,QAAQ,EAAE,gBAAgB;QAC1BF,QAAQ,EAAE,WAAW;QACrBF,MAAM,EAAE,6FAA6F;QACrGK,GAAG,EAAE,4DAA4D;QACjEF,UAAU,EAAE,IAAI;QAChBU,WAAW,EAAE,IAAI;QACjBQ,QAAQ,EAAE,KAAK;QACfd,KAAK,EAAE;UACLC,SAAS,EAAE,MAAM;UACjB+D,SAAS,EAAE,GAAG;UACd9D,KAAK,EAAE,GAAG;UACVE,cAAc,EAAE;SACjB;QACD6D,UAAU,EAAE,CAAC,UAAU,EAAE,SAAS,EAAE,WAAW,CAAC;QAChDC,SAAS,EAAE,IAAIC,IAAI;OACpB,EACD;QACEJ,GAAG,EAAE,MAAM;QACXlE,QAAQ,EAAE,cAAc;QACxBF,QAAQ,EAAE,aAAa;QACvBF,MAAM,EAAE,6FAA6F;QACrGK,GAAG,EAAE,0DAA0D;QAC/DF,UAAU,EAAE,KAAK;QACjBU,WAAW,EAAE,KAAK;QAClBQ,QAAQ,EAAE,IAAI;QACdd,KAAK,EAAE;UACLC,SAAS,EAAE,MAAM;UACjB+D,SAAS,EAAE,IAAI;UACf9D,KAAK,EAAE,IAAI;UACXE,cAAc,EAAE;SACjB;QACD6D,UAAU,EAAE,CAAC,gBAAgB,EAAE,SAAS,EAAE,aAAa,CAAC;QACxDC,SAAS,EAAE,IAAIC,IAAI;OACpB,EACD;QACEJ,GAAG,EAAE,MAAM;QACXlE,QAAQ,EAAE,wBAAwB;QAClCF,QAAQ,EAAE,aAAa;QACvBF,MAAM,EAAE,6FAA6F;QACrGK,GAAG,EAAE,6DAA6D;QAClEF,UAAU,EAAE,IAAI;QAChBU,WAAW,EAAE,KAAK;QAClBQ,QAAQ,EAAE,KAAK;QACfd,KAAK,EAAE;UACLC,SAAS,EAAE,MAAM;UACjB+D,SAAS,EAAE,GAAG;UACd9D,KAAK,EAAE,GAAG;UACVE,cAAc,EAAE;SACjB;QACD6D,UAAU,EAAE,CAAC,QAAQ,EAAE,UAAU,EAAE,UAAU,CAAC;QAC9CC,SAAS,EAAE,IAAIC,IAAI;OACpB,CACF;MAED;MACAR,MAAI,CAACzE,kBAAkB,GAAGyE,MAAI,CAACX,mBAAmB,CAACoB,MAAM,CAAC,CAACC,IAAI,EAAEC,OAAO,KACrED,IAAI,CAACrE,KAAK,CAACI,cAAc,GAAGkE,OAAO,CAACtE,KAAK,CAACI,cAAc,GAAIiE,IAAI,GAAGC,OAAO,CAC5E;MAED;MACAX,MAAI,CAACjB,gBAAgB,GAAGiB,MAAI,CAACX,mBAAmB,CAACuB,MAAM,CAACC,GAAG,IAAIA,GAAG,CAACT,GAAG,KAAKJ,MAAI,CAACzE,kBAAkB,EAAE6E,GAAG,CAAC;MAExG;MACAJ,MAAI,CAAChC,WAAW,GAAG,CACjB;QACEoC,GAAG,EAAE,OAAO;QACZ3C,KAAK,EAAE,mFAAmF;QAC1FC,OAAO,EAAE,0CAA0C;QACnDG,IAAI,EAAE,MAAM;QACZF,KAAK,EAAE,IAAI;QACXC,QAAQ,EAAE,GAAG;QACbE,MAAM,EAAEkC,MAAI,CAACzE,kBAAmB;QAChCgF,SAAS,EAAE,IAAIC,IAAI;OACpB,EACD;QACEJ,GAAG,EAAE,OAAO;QACZ3C,KAAK,EAAE,mFAAmF;QAC1FC,OAAO,EAAE,qCAAqC;QAC9CG,IAAI,EAAE,MAAM;QACZF,KAAK,EAAE,IAAI;QACXC,QAAQ,EAAE,EAAE;QACZE,MAAM,EAAEkC,MAAI,CAACjB,gBAAgB,CAAC,CAAC,CAAC;QAChCwB,SAAS,EAAE,IAAIC,IAAI;OACpB,EACD;QACEJ,GAAG,EAAE,OAAO;QACZ3C,KAAK,EAAE,mFAAmF;QAC1FC,OAAO,EAAE,mCAAmC;QAC5CG,IAAI,EAAE,MAAM;QACZF,KAAK,EAAE,IAAI;QACXC,QAAQ,EAAE,GAAG;QACbE,MAAM,EAAEkC,MAAI,CAACjB,gBAAgB,CAAC,CAAC,CAAC;QAChCwB,SAAS,EAAE,IAAIC,IAAI;OACpB,EACD;QACEJ,GAAG,EAAE,OAAO;QACZ3C,KAAK,EAAE,gFAAgF;QACvFC,OAAO,EAAE,yBAAyB;QAClCG,IAAI,EAAE,MAAM;QACZF,KAAK,EAAE,IAAI;QACXC,QAAQ,EAAE,GAAG;QACbE,MAAM,EAAEkC,MAAI,CAACjB,gBAAgB,CAAC,CAAC,CAAC;QAChCwB,SAAS,EAAE,IAAIC,IAAI;OACpB,CACF;MAED;MACAR,MAAI,CAACvB,kBAAkB,GAAG,CACxB;QAAEH,IAAI,EAAE,eAAe;QAAEC,SAAS,EAAE,KAAK;QAAEuC,KAAK,EAAE;MAAS,CAAE,EAC7D;QAAExC,IAAI,EAAE,aAAa;QAAEC,SAAS,EAAE,IAAI;QAAEuC,KAAK,EAAE;MAAS,CAAE,EAC1D;QAAExC,IAAI,EAAE,YAAY;QAAEC,SAAS,EAAE,IAAI;QAAEuC,KAAK,EAAE;MAAS,CAAE,EACzD;QAAExC,IAAI,EAAE,SAAS;QAAEC,SAAS,EAAE,IAAI;QAAEuC,KAAK,EAAE;MAAS,CAAE,EACtD;QAAExC,IAAI,EAAE,aAAa;QAAEC,SAAS,EAAE,IAAI;QAAEuC,KAAK,EAAE;MAAS,CAAE,CAC3D;MAEDd,MAAI,CAACV,SAAS,GAAG,KAAK;IAAC;EACzB;EAEAhE,iBAAiBA,CAACyF,UAA8B;IAC9C,IAAI,CAAC3B,MAAM,CAAC4B,QAAQ,CAAC,CAAC,UAAU,EAAED,UAAU,CAAC7E,QAAQ,CAAC,CAAC;EACzD;EAEAqB,WAAWA,CAAC0D,IAAkB;IAC5B,IAAI,CAAC7B,MAAM,CAAC4B,QAAQ,CAAC,CAAC,OAAO,EAAEC,IAAI,CAACb,GAAG,CAAC,CAAC;EAC3C;EAEA/B,eAAeA,CAAC6C,QAA0B;IACxC,IAAI,CAAC9B,MAAM,CAAC4B,QAAQ,CAAC,CAAC,UAAU,CAAC,EAAE;MACjCG,WAAW,EAAE;QAAEC,OAAO,EAAEF,QAAQ,CAAC5C;MAAI;KACtC,CAAC;EACJ;EAEA1C,YAAYA,CAACmF,UAA8B,EAAEM,KAAY;IACvDA,KAAK,CAACC,eAAe,EAAE;IAEvBP,UAAU,CAACpE,WAAW,GAAG,CAACoE,UAAU,CAACpE,WAAW;IAChD,IAAIoE,UAAU,CAACpE,WAAW,EAAE;MAC1BoE,UAAU,CAAC1E,KAAK,CAACC,SAAS,EAAE;KAC7B,MAAM;MACLyE,UAAU,CAAC1E,KAAK,CAACC,SAAS,EAAE;;EAEhC;EAEAF,WAAWA,CAACmF,KAAa;IACvB,IAAIA,KAAK,IAAI,OAAO,EAAE;MACpB,OAAO,CAACA,KAAK,GAAG,OAAO,EAAEC,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG;KAC1C,MAAM,IAAID,KAAK,IAAI,IAAI,EAAE;MACxB,OAAO,CAACA,KAAK,GAAG,IAAI,EAAEC,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG;;IAExC,OAAOD,KAAK,CAACE,QAAQ,EAAE;EACzB;EAEAzC,mBAAmBA,CAAC0C,KAAa,EAAEX,UAA8B;IAC/D,OAAOA,UAAU,CAACX,GAAG;EACvB;EAEAnC,aAAaA,CAACyD,KAAa,EAAET,IAAkB;IAC7C,OAAOA,IAAI,CAACb,GAAG;EACjB;EAEA1B,mBAAmBA,CAACgD,KAAa,EAAER,QAA0B;IAC3D,OAAOA,QAAQ,CAAC5C,IAAI;EACtB;EAEAqD,QAAQA,CAACN,KAAY;IACnBA,KAAK,CAACO,cAAc,EAAE;IACtB,IAAI,CAACxC,MAAM,CAAC4B,QAAQ,CAAC,CAAC,uBAAuB,CAAC,CAAC;EACjD;EAEAhG,OAAOA,CAAA;IACL,IAAI,CAACyE,uBAAuB,EAAE;EAChC;;;uBAxOWP,qCAAqC,EAAA9E,EAAA,CAAAyH,iBAAA,CAAAC,EAAA,CAAAC,MAAA;IAAA;EAAA;;;YAArC7C,qCAAqC;MAAA8C,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAA9H,EAAA,CAAA+H,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,+CAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCzD9CrI,EAFJ,CAAAC,cAAA,aAAiC,aACD,SACxB;UACFD,EAAA,CAAAE,SAAA,kBAA2C;UAC3CF,EAAA,CAAAG,MAAA,6BACF;UAAAH,EAAA,CAAAI,YAAA,EAAK;UACLJ,EAAA,CAAAC,cAAA,WAAuD;UAA3BD,EAAA,CAAAK,UAAA,mBAAAkI,kEAAAhH,MAAA;YAAA,OAAS+G,GAAA,CAAAf,QAAA,CAAAhG,MAAA,CAAgB;UAAA,EAAC;UAACvB,EAAA,CAAAG,MAAA,cAAO;UAChEH,EADgE,CAAAI,YAAA,EAAI,EAC9D;UAENJ,EAAA,CAAAC,cAAA,aAA+B;UAoI7BD,EAnIA,CAAAoB,UAAA,IAAAoH,oDAAA,iBAAiD,IAAAC,oDAAA,iBAKQ,KAAAC,qDAAA,iBAMuC,KAAAC,qDAAA,iBAwHF;UAKlG3I,EADE,CAAAI,YAAA,EAAM,EACF;;;UAxIIJ,EAAA,CAAAa,SAAA,GAAe;UAAfb,EAAA,CAAAyB,UAAA,SAAA6G,GAAA,CAAApD,SAAA,CAAe;UAKflF,EAAA,CAAAa,SAAA,EAAyB;UAAzBb,EAAA,CAAAyB,UAAA,SAAA6G,GAAA,CAAAvH,KAAA,KAAAuH,GAAA,CAAApD,SAAA,CAAyB;UAMzBlF,EAAA,CAAAa,SAAA,EAA4D;UAA5Db,EAAA,CAAAyB,UAAA,UAAA6G,GAAA,CAAApD,SAAA,KAAAoD,GAAA,CAAAvH,KAAA,IAAAuH,GAAA,CAAArD,mBAAA,CAAAJ,MAAA,KAA4D;UAwH5D7E,EAAA,CAAAa,SAAA,EAA8D;UAA9Db,EAAA,CAAAyB,UAAA,UAAA6G,GAAA,CAAApD,SAAA,KAAAoD,GAAA,CAAAvH,KAAA,IAAAuH,GAAA,CAAArD,mBAAA,CAAAJ,MAAA,OAA8D;;;qBDzFpEjF,YAAY,EAAAgJ,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EACZjJ,YAAY,EACZC,WAAW,EAAAiJ,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,UAAA;MAAAC,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}